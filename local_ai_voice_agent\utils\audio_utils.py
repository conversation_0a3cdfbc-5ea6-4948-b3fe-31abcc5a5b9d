"""
Audio processing utilities for local AI voice agent
Handles audio format conversion, enhancement, and processing
"""

import asyncio
import io
import logging
import wave
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import scipy.signal
from livekit import rtc
from livekit.agents.utils import AudioBuffer

logger = logging.getLogger(__name__)


class AudioProcessor:
    """Audio processing utilities"""
    
    def __init__(
        self,
        *,
        sample_rate: int = 16000,
        channels: int = 1,
        chunk_size: int = 1024,
        enable_enhancement: bool = True,
    ):
        """
        Initialize Audio Processor
        
        Args:
            sample_rate: Target sample rate
            channels: Number of audio channels
            chunk_size: Audio chunk size for processing
            enable_enhancement: Whether to enable audio enhancement
        """
        self._sample_rate = sample_rate
        self._channels = channels
        self._chunk_size = chunk_size
        self._enable_enhancement = enable_enhancement
    
    def convert_sample_rate(
        self,
        audio_data: np.ndarray,
        original_rate: int,
        target_rate: int,
    ) -> np.ndarray:
        """
        Convert audio sample rate
        
        Args:
            audio_data: Input audio data
            original_rate: Original sample rate
            target_rate: Target sample rate
            
        Returns:
            Resampled audio data
        """
        if original_rate == target_rate:
            return audio_data
        
        try:
            # Calculate resampling ratio
            ratio = target_rate / original_rate
            
            # Use scipy for high-quality resampling
            resampled = scipy.signal.resample(
                audio_data,
                int(len(audio_data) * ratio)
            )
            
            return resampled.astype(audio_data.dtype)
            
        except Exception as e:
            logger.error(f"Sample rate conversion failed: {e}")
            return audio_data
    
    def normalize_audio(self, audio_data: np.ndarray, target_level: float = 0.8) -> np.ndarray:
        """
        Normalize audio to target level
        
        Args:
            audio_data: Input audio data
            target_level: Target normalization level (0.0 to 1.0)
            
        Returns:
            Normalized audio data
        """
        try:
            # Calculate RMS level
            rms = np.sqrt(np.mean(audio_data ** 2))
            
            if rms > 0:
                # Calculate scaling factor
                scale = target_level / rms
                
                # Apply scaling with clipping protection
                normalized = audio_data * scale
                normalized = np.clip(normalized, -1.0, 1.0)
                
                return normalized
            else:
                return audio_data
                
        except Exception as e:
            logger.error(f"Audio normalization failed: {e}")
            return audio_data
    
    def apply_noise_reduction(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Apply basic noise reduction
        
        Args:
            audio_data: Input audio data
            
        Returns:
            Noise-reduced audio data
        """
        try:
            # Simple spectral subtraction for noise reduction
            # This is a basic implementation - more sophisticated methods exist
            
            # Apply high-pass filter to remove low-frequency noise
            nyquist = self._sample_rate / 2
            cutoff = 80  # Hz
            normalized_cutoff = cutoff / nyquist
            
            b, a = scipy.signal.butter(4, normalized_cutoff, btype='high')
            filtered = scipy.signal.filtfilt(b, a, audio_data)
            
            return filtered.astype(audio_data.dtype)
            
        except Exception as e:
            logger.error(f"Noise reduction failed: {e}")
            return audio_data
    
    def enhance_speech(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Enhance speech quality
        
        Args:
            audio_data: Input audio data
            
        Returns:
            Enhanced audio data
        """
        if not self._enable_enhancement:
            return audio_data
        
        try:
            enhanced = audio_data.copy()
            
            # Apply noise reduction
            enhanced = self.apply_noise_reduction(enhanced)
            
            # Normalize audio
            enhanced = self.normalize_audio(enhanced, target_level=0.7)
            
            # Apply gentle compression to even out volume
            enhanced = self._apply_compression(enhanced)
            
            return enhanced
            
        except Exception as e:
            logger.error(f"Speech enhancement failed: {e}")
            return audio_data
    
    def _apply_compression(self, audio_data: np.ndarray, threshold: float = 0.5, ratio: float = 4.0) -> np.ndarray:
        """
        Apply audio compression
        
        Args:
            audio_data: Input audio data
            threshold: Compression threshold
            ratio: Compression ratio
            
        Returns:
            Compressed audio data
        """
        try:
            # Simple compression algorithm
            compressed = audio_data.copy()
            
            # Find samples above threshold
            above_threshold = np.abs(compressed) > threshold
            
            # Apply compression to samples above threshold
            compressed[above_threshold] = (
                np.sign(compressed[above_threshold]) * 
                (threshold + (np.abs(compressed[above_threshold]) - threshold) / ratio)
            )
            
            return compressed
            
        except Exception as e:
            logger.error(f"Audio compression failed: {e}")
            return audio_data
    
    def convert_to_mono(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Convert stereo audio to mono
        
        Args:
            audio_data: Input audio data (may be stereo)
            
        Returns:
            Mono audio data
        """
        try:
            if audio_data.ndim == 1:
                return audio_data
            elif audio_data.ndim == 2:
                # Average the channels
                return np.mean(audio_data, axis=1)
            else:
                logger.warning(f"Unexpected audio shape: {audio_data.shape}")
                return audio_data.flatten()
                
        except Exception as e:
            logger.error(f"Mono conversion failed: {e}")
            return audio_data
    
    def buffer_to_numpy(self, buffer: AudioBuffer) -> np.ndarray:
        """
        Convert AudioBuffer to numpy array
        
        Args:
            buffer: LiveKit AudioBuffer
            
        Returns:
            Numpy array of audio data
        """
        try:
            # Combine all frames in the buffer
            combined_frame = rtc.combine_audio_frames(buffer)
            
            # Convert to numpy array
            audio_data = np.frombuffer(combined_frame.data, dtype=np.int16)
            
            # Convert to float32 and normalize
            audio_data = audio_data.astype(np.float32) / 32768.0
            
            # Ensure mono audio
            if combined_frame.num_channels > 1:
                audio_data = audio_data.reshape(-1, combined_frame.num_channels)
                audio_data = self.convert_to_mono(audio_data)
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Buffer conversion failed: {e}")
            return np.array([], dtype=np.float32)
    
    def numpy_to_audio_frame(
        self,
        audio_data: np.ndarray,
        sample_rate: Optional[int] = None,
    ) -> rtc.AudioFrame:
        """
        Convert numpy array to AudioFrame
        
        Args:
            audio_data: Numpy audio data
            sample_rate: Sample rate (uses default if None)
            
        Returns:
            LiveKit AudioFrame
        """
        try:
            sample_rate = sample_rate or self._sample_rate
            
            # Ensure audio is in correct format
            if audio_data.dtype != np.int16:
                # Convert float to int16
                audio_data = (audio_data * 32767).astype(np.int16)
            
            # Create AudioFrame
            frame = rtc.AudioFrame(
                data=audio_data.tobytes(),
                sample_rate=sample_rate,
                num_channels=self._channels,
                samples_per_channel=len(audio_data) // self._channels
            )
            
            return frame
            
        except Exception as e:
            logger.error(f"AudioFrame conversion failed: {e}")
            raise
    
    def save_audio_to_file(
        self,
        audio_data: np.ndarray,
        filename: str,
        sample_rate: Optional[int] = None,
    ):
        """
        Save audio data to WAV file
        
        Args:
            audio_data: Audio data to save
            filename: Output filename
            sample_rate: Sample rate (uses default if None)
        """
        try:
            sample_rate = sample_rate or self._sample_rate
            
            # Ensure audio is in correct format
            if audio_data.dtype != np.int16:
                audio_data = (audio_data * 32767).astype(np.int16)
            
            # Save to WAV file
            with wave.open(filename, 'wb') as wav_file:
                wav_file.setnchannels(self._channels)
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())
            
            logger.info(f"Audio saved to {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save audio: {e}")
    
    def load_audio_from_file(self, filename: str) -> Tuple[np.ndarray, int]:
        """
        Load audio data from WAV file
        
        Args:
            filename: Input filename
            
        Returns:
            Tuple of (audio_data, sample_rate)
        """
        try:
            with wave.open(filename, 'rb') as wav_file:
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                frames = wav_file.readframes(wav_file.getnframes())
                
                # Convert to numpy array
                audio_data = np.frombuffer(frames, dtype=np.int16)
                
                # Convert to float32
                audio_data = audio_data.astype(np.float32) / 32768.0
                
                # Handle multi-channel audio
                if channels > 1:
                    audio_data = audio_data.reshape(-1, channels)
                    audio_data = self.convert_to_mono(audio_data)
                
                return audio_data, sample_rate
                
        except Exception as e:
            logger.error(f"Failed to load audio: {e}")
            return np.array([]), 0
    
    def detect_silence(
        self,
        audio_data: np.ndarray,
        threshold: float = 0.01,
        min_duration: float = 0.5,
    ) -> List[Tuple[float, float]]:
        """
        Detect silent segments in audio
        
        Args:
            audio_data: Input audio data
            threshold: Silence threshold
            min_duration: Minimum silence duration in seconds
            
        Returns:
            List of (start_time, end_time) tuples for silent segments
        """
        try:
            # Calculate frame-wise energy
            frame_length = int(0.025 * self._sample_rate)  # 25ms frames
            hop_length = int(0.010 * self._sample_rate)    # 10ms hop
            
            energy = []
            for i in range(0, len(audio_data) - frame_length, hop_length):
                frame = audio_data[i:i + frame_length]
                energy.append(np.mean(frame ** 2))
            
            energy = np.array(energy)
            
            # Find silent frames
            silent_frames = energy < threshold
            
            # Find continuous silent segments
            silent_segments = []
            in_silence = False
            start_frame = 0
            
            for i, is_silent in enumerate(silent_frames):
                if is_silent and not in_silence:
                    # Start of silence
                    in_silence = True
                    start_frame = i
                elif not is_silent and in_silence:
                    # End of silence
                    in_silence = False
                    duration = (i - start_frame) * hop_length / self._sample_rate
                    
                    if duration >= min_duration:
                        start_time = start_frame * hop_length / self._sample_rate
                        end_time = i * hop_length / self._sample_rate
                        silent_segments.append((start_time, end_time))
            
            return silent_segments
            
        except Exception as e:
            logger.error(f"Silence detection failed: {e}")
            return []
    
    def trim_silence(
        self,
        audio_data: np.ndarray,
        threshold: float = 0.01,
    ) -> np.ndarray:
        """
        Trim silence from beginning and end of audio
        
        Args:
            audio_data: Input audio data
            threshold: Silence threshold
            
        Returns:
            Trimmed audio data
        """
        try:
            # Find non-silent samples
            energy = audio_data ** 2
            non_silent = energy > threshold
            
            if not np.any(non_silent):
                return audio_data  # All silent
            
            # Find first and last non-silent samples
            first_sound = np.argmax(non_silent)
            last_sound = len(non_silent) - np.argmax(non_silent[::-1]) - 1
            
            return audio_data[first_sound:last_sound + 1]
            
        except Exception as e:
            logger.error(f"Silence trimming failed: {e}")
            return audio_data

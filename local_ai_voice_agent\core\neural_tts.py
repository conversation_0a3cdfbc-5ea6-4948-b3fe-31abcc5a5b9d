"""
High-Quality Neural Text-to-Speech System
Uses advanced neural models for natural, human-like voice synthesis
"""

import asyncio
import logging
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading
import queue

logger = logging.getLogger(__name__)


class NeuralTTSEngine:
    """High-quality neural TTS with multiple engine options"""
    
    def __init__(self):
        self.current_engine = None
        self.available_engines = {}
        self.voice_settings = {
            "speed": 1.0,
            "pitch": 1.0,
            "volume": 0.8,
            "voice_id": "female_natural"
        }
        
        # Try to initialize the best available engine
        self.initialize_best_engine()
    
    def initialize_best_engine(self):
        """Initialize the best available TTS engine"""
        logger.info("🎙️ Initializing high-quality neural TTS...")
        
        # Try engines in order of quality
        engines_to_try = [
            ("coqui_xtts", self._init_coqui_xtts),
            ("realtime_tts", self._init_realtime_tts),
            ("edge_tts", self._init_edge_tts),
            ("pyttsx3_enhanced", self._init_pyttsx3_enhanced)
        ]
        
        for engine_name, init_func in engines_to_try:
            try:
                logger.info(f"Trying {engine_name}...")
                if init_func():
                    self.current_engine = engine_name
                    logger.info(f"✓ Successfully initialized {engine_name}")
                    return True
            except Exception as e:
                logger.warning(f"Failed to initialize {engine_name}: {e}")
                continue
        
        logger.error("❌ No TTS engines could be initialized")
        return False
    
    def _init_coqui_xtts(self) -> bool:
        """Initialize Coqui XTTS (highest quality)"""
        try:
            # Check if TTS is installed
            import TTS
            from TTS.api import TTS as CoquiTTS
            
            # Initialize with a good English model
            model_name = "tts_models/en/ljspeech/tacotron2-DDC"
            self.coqui_tts = CoquiTTS(model_name=model_name)
            
            self.available_engines["coqui_xtts"] = {
                "name": "Coqui XTTS",
                "quality": "highest",
                "description": "Neural TTS with natural voice synthesis"
            }
            
            logger.info("✓ Coqui XTTS initialized successfully")
            return True
            
        except ImportError:
            logger.info("Coqui TTS not installed. Install with: pip install TTS")
            return False
        except Exception as e:
            logger.warning(f"Coqui XTTS initialization failed: {e}")
            return False
    
    def _init_realtime_tts(self) -> bool:
        """Initialize RealtimeTTS (very high quality, streaming)"""
        try:
            from RealtimeTTS import TextToAudioStream, CoquiEngine
            
            # Initialize with Coqui engine
            engine = CoquiEngine()
            self.realtime_stream = TextToAudioStream(engine)
            
            self.available_engines["realtime_tts"] = {
                "name": "RealtimeTTS",
                "quality": "very_high", 
                "description": "Real-time streaming neural TTS"
            }
            
            logger.info("✓ RealtimeTTS initialized successfully")
            return True
            
        except ImportError:
            logger.info("RealtimeTTS not installed. Install with: pip install RealtimeTTS")
            return False
        except Exception as e:
            logger.warning(f"RealtimeTTS initialization failed: {e}")
            return False
    
    def _init_edge_tts(self) -> bool:
        """Initialize Edge TTS (Microsoft's neural voices, free)"""
        try:
            import edge_tts
            
            # Available high-quality voices
            self.edge_voices = {
                "female_natural": "en-US-AriaNeural",
                "female_friendly": "en-US-JennyNeural", 
                "female_calm": "en-US-MichelleNeural",
                "male_natural": "en-US-GuyNeural",
                "male_friendly": "en-US-BrianNeural"
            }
            
            self.available_engines["edge_tts"] = {
                "name": "Microsoft Edge TTS",
                "quality": "high",
                "description": "Microsoft's neural voices (requires internet)"
            }
            
            logger.info("✓ Edge TTS initialized successfully")
            return True
            
        except ImportError:
            logger.info("Edge TTS not installed. Install with: pip install edge-tts")
            return False
        except Exception as e:
            logger.warning(f"Edge TTS initialization failed: {e}")
            return False
    
    def _init_pyttsx3_enhanced(self) -> bool:
        """Initialize enhanced pyttsx3 as fallback"""
        try:
            import pyttsx3
            
            self.pyttsx3_engine = pyttsx3.init()
            
            # Try to find better voices
            voices = self.pyttsx3_engine.getProperty('voices')
            best_voice = None
            
            for voice in voices:
                voice_name = voice.name.lower()
                # Look for higher quality voices
                if any(keyword in voice_name for keyword in ['zira', 'hazel', 'aria', 'jenny']):
                    best_voice = voice.id
                    break
            
            if best_voice:
                self.pyttsx3_engine.setProperty('voice', best_voice)
            
            # Optimize settings for better quality
            self.pyttsx3_engine.setProperty('rate', 180)  # Slower, more natural
            self.pyttsx3_engine.setProperty('volume', 0.8)
            
            self.available_engines["pyttsx3_enhanced"] = {
                "name": "Enhanced pyttsx3",
                "quality": "medium",
                "description": "Improved Windows SAPI voices"
            }
            
            logger.info("✓ Enhanced pyttsx3 initialized successfully")
            return True
            
        except Exception as e:
            logger.warning(f"Enhanced pyttsx3 initialization failed: {e}")
            return False
    
    async def speak_neural(self, text: str, emotion: str = "neutral", voice_id: str = None) -> bool:
        """Speak text using the best available neural TTS"""
        try:
            if not self.current_engine:
                logger.error("No TTS engine available")
                return False
            
            # Clean and prepare text
            clean_text = self._prepare_text_for_speech(text)
            
            # Apply emotional modulation
            voice_params = self._get_emotional_voice_params(emotion)
            
            # Use the current engine
            if self.current_engine == "coqui_xtts":
                return await self._speak_coqui(clean_text, voice_params)
            elif self.current_engine == "realtime_tts":
                return await self._speak_realtime(clean_text, voice_params)
            elif self.current_engine == "edge_tts":
                return await self._speak_edge(clean_text, voice_params, voice_id)
            elif self.current_engine == "pyttsx3_enhanced":
                return await self._speak_pyttsx3_enhanced(clean_text, voice_params)
            
            return False
            
        except Exception as e:
            logger.error(f"Neural TTS error: {e}")
            return False
    
    def _prepare_text_for_speech(self, text: str) -> str:
        """Prepare text for natural speech synthesis"""
        # Remove markdown and special characters
        import re
        
        # Remove code blocks
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
        text = re.sub(r'`.*?`', '', text)
        
        # Remove special markup
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # Italic
        
        # Clean up spacing
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Add natural pauses
        text = text.replace('.', '. ')
        text = text.replace(',', ', ')
        text = text.replace('!', '! ')
        text = text.replace('?', '? ')
        
        return text
    
    def _get_emotional_voice_params(self, emotion: str) -> Dict[str, Any]:
        """Get voice parameters for different emotions"""
        emotional_params = {
            "happy": {"speed": 1.1, "pitch": 1.1, "volume": 0.9},
            "excited": {"speed": 1.2, "pitch": 1.2, "volume": 0.95},
            "sad": {"speed": 0.8, "pitch": 0.9, "volume": 0.7},
            "calm": {"speed": 0.9, "pitch": 1.0, "volume": 0.8},
            "friendly": {"speed": 1.0, "pitch": 1.05, "volume": 0.85},
            "thoughtful": {"speed": 0.85, "pitch": 0.95, "volume": 0.8},
            "neutral": {"speed": 1.0, "pitch": 1.0, "volume": 0.8}
        }
        
        return emotional_params.get(emotion, emotional_params["neutral"])
    
    async def _speak_coqui(self, text: str, voice_params: Dict) -> bool:
        """Speak using Coqui XTTS"""
        try:
            # Generate audio
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                self.coqui_tts.tts_to_file(text=text, file_path=tmp_file.name)
                
                # Play the audio file
                await self._play_audio_file(tmp_file.name)
                
                # Clean up
                os.unlink(tmp_file.name)
                
            logger.info(f"🗣️ Josie (Coqui): {text}")
            return True
            
        except Exception as e:
            logger.error(f"Coqui TTS error: {e}")
            return False
    
    async def _speak_realtime(self, text: str, voice_params: Dict) -> bool:
        """Speak using RealtimeTTS"""
        try:
            # Stream the text
            self.realtime_stream.feed(text)
            self.realtime_stream.play()
            
            logger.info(f"🗣️ Josie (RealtimeTTS): {text}")
            return True
            
        except Exception as e:
            logger.error(f"RealtimeTTS error: {e}")
            return False
    
    async def _speak_edge(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using Edge TTS"""
        tmp_file_path = None
        try:
            import edge_tts

            # Select voice
            voice = voice_id or self.edge_voices.get(self.voice_settings["voice_id"], "en-US-AriaNeural")

            # Generate speech
            communicate = edge_tts.Communicate(text, voice)

            # Create temporary file
            tmp_file = tempfile.NamedTemporaryFile(suffix=".mp3", delete=False)
            tmp_file_path = tmp_file.name
            tmp_file.close()

            # Save audio to file
            await communicate.save(tmp_file_path)

            # Wait a moment for file to be fully written
            await asyncio.sleep(0.1)

            # Play the audio file
            await self._play_audio_file(tmp_file_path)

            logger.info(f"🗣️ Josie (Edge TTS): {text}")
            return True

        except Exception as e:
            logger.error(f"Edge TTS error: {e}")
            return False
        finally:
            # Clean up temporary file
            if tmp_file_path and os.path.exists(tmp_file_path):
                try:
                    await asyncio.sleep(0.5)  # Give time for playback to finish
                    os.unlink(tmp_file_path)
                except Exception as cleanup_error:
                    logger.warning(f"Could not clean up temp file: {cleanup_error}")
    
    async def _speak_pyttsx3_enhanced(self, text: str, voice_params: Dict) -> bool:
        """Speak using enhanced pyttsx3"""
        try:
            # Apply voice parameters
            current_rate = self.pyttsx3_engine.getProperty('rate')
            current_volume = self.pyttsx3_engine.getProperty('volume')
            
            new_rate = int(current_rate * voice_params.get('speed', 1.0))
            new_volume = voice_params.get('volume', 0.8)
            
            self.pyttsx3_engine.setProperty('rate', new_rate)
            self.pyttsx3_engine.setProperty('volume', new_volume)
            
            # Speak
            self.pyttsx3_engine.say(text)
            self.pyttsx3_engine.runAndWait()
            
            logger.info(f"🗣️ Josie (Enhanced): {text}")
            return True
            
        except Exception as e:
            logger.error(f"Enhanced pyttsx3 error: {e}")
            return False
    
    async def _play_audio_file(self, file_path: str):
        """Play audio file using available player"""
        try:
            # Ensure file exists and is accessible
            if not os.path.exists(file_path):
                logger.error(f"Audio file not found: {file_path}")
                return

            # Try pygame first (most reliable)
            try:
                import pygame
                pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=1024)
                pygame.mixer.init()

                # Load and play
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()

                # Wait for playback to complete
                while pygame.mixer.music.get_busy():
                    await asyncio.sleep(0.1)

                pygame.mixer.quit()
                logger.info("✓ Audio played successfully with pygame")
                return

            except ImportError:
                logger.info("Pygame not available, trying other methods...")
            except Exception as e:
                logger.warning(f"Pygame playback failed: {e}")

            # Try Windows-specific methods
            import platform
            if platform.system() == "Windows":
                try:
                    # Try VLC if available
                    import subprocess
                    result = subprocess.run([
                        "powershell", "-c",
                        f"$player = New-Object -ComObject WMPlayer.OCX; "
                        f"$player.URL = '{file_path}'; "
                        f"$player.controls.play(); "
                        f"while($player.playState -ne 1) {{ Start-Sleep -Milliseconds 100 }}; "
                        f"while($player.playState -eq 3) {{ Start-Sleep -Milliseconds 100 }}"
                    ], capture_output=True, timeout=30, text=True)

                    if result.returncode == 0:
                        logger.info("✓ Audio played successfully with Windows Media Player")
                        return
                    else:
                        logger.warning(f"Windows Media Player failed: {result.stderr}")

                except Exception as e:
                    logger.warning(f"Windows Media Player failed: {e}")

                try:
                    # Simple fallback - just open the file
                    import subprocess
                    subprocess.Popen([file_path], shell=True)
                    await asyncio.sleep(3)  # Give time for playback
                    logger.info("✓ Audio opened with default player")
                    return
                except Exception as e:
                    logger.warning(f"Default player failed: {e}")

            logger.error("All audio playback methods failed")

        except Exception as e:
            logger.error(f"Audio playback error: {e}")
    
    def get_available_voices(self) -> Dict[str, Any]:
        """Get information about available voices"""
        return {
            "current_engine": self.current_engine,
            "available_engines": self.available_engines,
            "voice_settings": self.voice_settings
        }
    
    def set_voice_preference(self, voice_id: str):
        """Set voice preference"""
        self.voice_settings["voice_id"] = voice_id
        logger.info(f"Voice preference set to: {voice_id}")


# Convenience function
async def create_neural_tts() -> NeuralTTSEngine:
    """Create and initialize neural TTS engine"""
    engine = NeuralTTSEngine()
    return engine

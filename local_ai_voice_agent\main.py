"""
Main entry point for Local AI Voice Agent
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

from livekit.agents import cli, WorkerOptions, WorkerType
from livekit.agents.llm import function_tool

from .agents.voice_agent import LocalVoiceAgent, create_agent_session, prewarm
from .utils.model_utils import ModelManager
from .utils.audio_utils import AudioProcessor

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logger = logging.getLogger(__name__)


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("local_voice_agent.log")
        ]
    )


async def main():
    """Main entry point"""
    setup_logging()
    logger.info("Starting Local AI Voice Agent")
    
    try:
        # Initialize model manager
        model_manager = ModelManager()
        
        # Check model availability
        logger.info("Checking model availability...")
        ollama_models = await model_manager.check_ollama_models()
        
        for model_id, available in ollama_models.items():
            status = "✓" if available else "✗"
            logger.info(f"  {status} {model_id}")
        
        # Create worker options
        worker_options = WorkerOptions(
            entrypoint_fnc=create_agent_session,
            prewarm_fnc=prewarm,
            worker_type=WorkerType.ROOM,
        )
        
        # Start the agent
        await cli.run_app(worker_options)
        
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    except Exception as e:
        logger.error(f"Error starting agent: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

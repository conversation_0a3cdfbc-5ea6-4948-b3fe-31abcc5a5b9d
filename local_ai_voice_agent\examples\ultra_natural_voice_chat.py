"""
Ultra-Natural Voice Conversation System
The most human-like, conversational AI voice experience
"""

import asyncio
import logging
import sys
import time
import os
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.append(str(project_root / "local_ai_voice_agent" / "core"))

# Import our systems
from conversation_engine import ConversationEngine
from ultra_natural_tts import UltraNaturalTTS

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UltraNaturalVoiceAgent:
    """Ultra-natural voice agent with the most human-like voice"""
    
    def __init__(self):
        self.conversation_engine = ConversationEngine()
        self.ultra_tts = None
        self.whisper_model = None
        self.is_listening = False
        self.is_speaking = False
        self.conversation_active = True
        
        self.setup_components()
    
    def setup_components(self):
        """Initialize all ultra-natural components"""
        try:
            # Initialize Ultra-Natural TTS
            logger.info("🎭 Initializing ultra-natural voice system...")
            self.ultra_tts = UltraNaturalTTS()
            
            if self.ultra_tts.current_engine:
                engine_info = self.ultra_tts.available_engines[self.ultra_tts.current_engine]
                logger.info(f"✓ Using {engine_info['name']} - {engine_info['description']}")
                
                # Show naturalness level
                voice_info = self.ultra_tts.get_voice_info()
                naturalness = voice_info.get('naturalness_level', 'natural')
                logger.info(f"🎭 Voice Naturalness Level: {naturalness.upper()}")
                
            else:
                logger.error("❌ No ultra-natural TTS engine available")
                return False
            
            # Initialize Whisper for STT
            import whisper
            logger.info("🧠 Loading Whisper model...")
            self.whisper_model = whisper.load_model("base")
            logger.info("✓ Speech recognition ready")
            
            logger.info("🎉 Ultra-natural voice system ready!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize ultra-natural components: {e}")
            return False
    
    def listen_for_speech(self, duration: int = 6) -> Optional[str]:
        """Enhanced speech recognition"""
        try:
            import pyaudio
            import numpy as np
            
            # Audio settings
            CHUNK = 1024
            FORMAT = pyaudio.paInt16
            CHANNELS = 1
            RATE = 16000
            
            logger.info(f"🎤 Listening for natural speech ({duration}s)...")
            
            # Initialize PyAudio
            audio = pyaudio.PyAudio()
            
            # Open stream
            stream = audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK
            )
            
            frames = []
            
            # Record audio
            for i in range(0, int(RATE / CHUNK * duration)):
                data = stream.read(CHUNK)
                frames.append(data)
            
            # Stop recording
            stream.stop_stream()
            stream.close()
            audio.terminate()
            
            # Convert to numpy array
            audio_data = b''.join(frames)
            audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Use Whisper to transcribe
            logger.info("🔄 Processing speech...")
            result = self.whisper_model.transcribe(audio_np, language="en")
            text = result["text"].strip()
            
            if text and len(text) > 2:
                logger.info(f"👤 You: {text}")
                return text
            else:
                logger.info("🔇 No clear speech detected")
                return None
                
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return None
    
    async def get_intelligent_response(self, user_input: str) -> str:
        """Get intelligent response using conversation engine"""
        try:
            import httpx
            
            # Build AI prompt with personality and context
            prompt_data = self.conversation_engine.build_ai_prompt(user_input)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://localhost:11434/v1/chat/completions",
                    json=prompt_data,
                    timeout=45.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        ai_response = result["choices"][0]["message"]["content"]
                        
                        # Process response through conversation engine
                        processed_response = self.conversation_engine.process_ai_response(ai_response)
                        
                        return processed_response
                
                return "I'm having trouble thinking right now. Could you say that again?"
                
        except Exception as e:
            logger.error(f"AI response error: {e}")
            return "Sorry, I'm having some technical difficulties. Let's try again."
    
    async def ultra_natural_conversation_loop(self):
        """Ultra-natural conversation loop with the most human-like voice"""
        logger.info("🎭 Starting ultra-natural voice conversation...")
        
        # Show voice system information
        voice_info = self.ultra_tts.get_voice_info()
        logger.info(f"🎙️ Voice Engine: {voice_info['current_engine']}")
        logger.info(f"🎭 Naturalness Level: {voice_info.get('naturalness_level', 'natural').upper()}")
        
        # Ultra-natural greeting
        greeting = self.conversation_engine.get_greeting()
        await self.ultra_tts.speak_ultra_natural(greeting, "friendly")
        
        # Ask for name if new user
        if not self.conversation_engine.memory.user_name:
            name_prompt = "What should I call you?"
            await self.ultra_tts.speak_ultra_natural(name_prompt, "conversational")
            
            # Listen for name
            user_name = self.listen_for_speech(duration=5)
            if user_name:
                # Extract name
                name = user_name.split()[-1] if user_name else "friend"
                self.conversation_engine.set_user_name(name)
                
                name_response = f"Nice to meet you, {name}! I'll remember that."
                await self.ultra_tts.speak_ultra_natural(name_response, "happy")
        
        logger.info("\n" + "="*80)
        logger.info("🎭 ULTRA-NATURAL VOICE CONVERSATION ACTIVE")
        logger.info("="*80)
        logger.info("Ultra-Natural Features:")
        logger.info("  🎭 Most human-like voice synthesis available")
        logger.info("  🗣️ Natural speech patterns and emotional expression")
        logger.info("  🧠 Advanced conversation intelligence")
        logger.info("  💭 Emotional context awareness")
        logger.info("  🤝 Relationship building and memory")
        logger.info("  ⚡ Real-time natural conversation flow")
        logger.info("\nVoice Quality:")
        current_engine = self.ultra_tts.current_engine
        if current_engine in self.ultra_tts.available_engines:
            engine_info = self.ultra_tts.available_engines[current_engine]
            logger.info(f"  Engine: {engine_info['name']}")
            logger.info(f"  Quality: {engine_info['quality']}")
            logger.info(f"  Description: {engine_info['description']}")
            logger.info(f"  Naturalness: {voice_info.get('naturalness_level', 'natural').upper()}")
        logger.info("\nAPI Keys (for premium voices):")
        logger.info("  - Set ELEVENLABS_API_KEY for ultra-natural ElevenLabs voices")
        logger.info("  - Set OPENAI_API_KEY for OpenAI TTS voices")
        logger.info("\nInstructions:")
        logger.info("  - Speak naturally when Josie is listening")
        logger.info("  - Experience ultra-natural voice quality")
        logger.info("  - Say 'goodbye' to end gracefully")
        logger.info("  - Press Ctrl+C to force quit")
        logger.info("="*80)
        
        try:
            conversation_count = 0
            while self.conversation_active:
                # Listen for user input
                user_speech = self.listen_for_speech(duration=7)
                
                if user_speech:
                    conversation_count += 1
                    
                    # Check for exit commands
                    if any(word in user_speech.lower() for word in ['goodbye', 'quit', 'exit', 'stop', 'bye bye']):
                        # Ultra-natural farewell
                        user_name = self.conversation_engine.memory.user_name or "friend"
                        relationship = self.conversation_engine.memory.relationship_level
                        
                        if relationship == "close_friend":
                            farewell = f"Goodbye {user_name}! I really enjoyed our chat. Can't wait to talk again soon!"
                        elif relationship == "friend":
                            farewell = f"See you later, {user_name}! Thanks for the great conversation!"
                        else:
                            farewell = f"Goodbye {user_name}! It was wonderful talking with you!"
                        
                        await self.ultra_tts.speak_ultra_natural(farewell, "happy")
                        break
                    
                    # Get intelligent response
                    logger.info("🤖 Josie is thinking...")
                    start_time = time.time()
                    
                    ai_response = await self.get_intelligent_response(user_speech)
                    
                    response_time = time.time() - start_time
                    logger.info(f"⚡ Response generated in {response_time:.1f}s")
                    
                    # Detect emotion for ultra-natural voice
                    emotion = self.conversation_engine.context.user_mood
                    if emotion == "neutral":
                        emotion = "conversational"
                    
                    # Speak with ultra-natural voice
                    await self.ultra_tts.speak_ultra_natural(ai_response, emotion)
                    
                    # Small pause before next listening cycle
                    await asyncio.sleep(0.5)
                    
                else:
                    # No speech detected - gentle prompt with ultra-natural voice
                    if conversation_count == 0:
                        prompt = "I'm here and listening. What would you like to talk about?"
                        await self.ultra_tts.speak_ultra_natural(prompt, "friendly")
                    else:
                        prompt = "I'm still here. Feel free to continue."
                        await self.ultra_tts.speak_ultra_natural(prompt, "calm")
                    
                    await asyncio.sleep(1)
                    
        except KeyboardInterrupt:
            logger.info("\n🛑 Conversation interrupted")
            farewell = "Oh! Goodbye for now!"
            await self.ultra_tts.speak_ultra_natural(farewell, "conversational")
        except Exception as e:
            logger.error(f"Conversation error: {e}")
            error_msg = "I'm sorry, I encountered an error. Let's try again later!"
            await self.ultra_tts.speak_ultra_natural(error_msg, "calm")
    
    async def test_ultra_natural_voice(self):
        """Test ultra-natural voice capabilities"""
        logger.info("🧪 Testing ultra-natural voice system...")
        
        # Test different emotions with ultra-natural voice
        emotions_to_test = [
            ("happy", "I'm so happy to meet you! This is my ultra-natural happy voice!"),
            ("excited", "This is incredible! I'm so excited to show you this amazing voice quality!"),
            ("calm", "This is my calm, peaceful voice. Notice how natural and soothing it sounds."),
            ("conversational", "Hi there! This is my conversational voice - perfect for natural dialogue."),
            ("thoughtful", "Hmm, this is how I sound when I'm thinking deeply about something important.")
        ]
        
        for emotion, text in emotions_to_test:
            logger.info(f"Testing {emotion} ultra-natural voice...")
            await self.ultra_tts.speak_ultra_natural(text, emotion)
            await asyncio.sleep(1)
        
        # Test microphone
        logger.info("Testing microphone...")
        test_speech = self.listen_for_speech(duration=3)
        if test_speech:
            response = f"Perfect! I heard you say: {test_speech}. Notice how natural my voice sounds!"
            await self.ultra_tts.speak_ultra_natural(response, "excited")
            logger.info("✓ All ultra-natural systems working perfectly!")
        else:
            logger.warning("⚠️ Microphone test - no speech detected")
    
    def get_voice_info(self):
        """Get detailed ultra-natural voice system information"""
        if self.ultra_tts:
            return self.ultra_tts.get_voice_info()
        return {"error": "Ultra-natural TTS not initialized"}


async def main():
    """Main function"""
    try:
        agent = UltraNaturalVoiceAgent()
        
        if not agent.ultra_tts or not agent.ultra_tts.current_engine:
            print("\n❌ Ultra-natural voice system failed to initialize")
            print("\n🎭 PREMIUM VOICE OPTIONS:")
            print("1. ElevenLabs (ULTRA-NATURAL): Set ELEVENLABS_API_KEY environment variable")
            print("2. OpenAI TTS (VERY NATURAL): Set OPENAI_API_KEY environment variable")
            print("3. Enhanced Edge TTS (NATURAL): Available as fallback")
            print("\n💡 For the most natural voice, get API keys from:")
            print("   - ElevenLabs: https://elevenlabs.io/")
            print("   - OpenAI: https://platform.openai.com/")
            return
        
        if len(sys.argv) > 1:
            if sys.argv[1].lower() == "test":
                await agent.test_ultra_natural_voice()
            elif sys.argv[1].lower() == "info":
                voice_info = agent.get_voice_info()
                print("\n🎭 Ultra-Natural Voice System Information:")
                print(f"Current Engine: {voice_info.get('current_engine', 'None')}")
                print(f"Naturalness Level: {voice_info.get('naturalness_level', 'natural').upper()}")
                print("\nAvailable Engines:")
                for engine, info in voice_info.get('available_engines', {}).items():
                    print(f"  {engine}: {info['name']} ({info['quality']} quality)")
                    print(f"    {info['description']}")
            elif sys.argv[1].lower() == "chat":
                await agent.ultra_natural_conversation_loop()
            else:
                print("Usage: python ultra_natural_voice_chat.py [test|info|chat]")
        else:
            # Default: start ultra-natural conversation
            await agent.ultra_natural_conversation_loop()
            
    except Exception as e:
        logger.error(f"Ultra-natural voice system error: {e}")
        print("\n❌ Ultra-natural voice system failed to start")
        print("Try: python ultra_natural_voice_chat.py test")


if __name__ == "__main__":
    asyncio.run(main())

"""
Full Voice-to-Voice Conversation System
Real-time speech input -> AI processing -> speech output
Like talking to a real person!
"""

import asyncio
import logging
import sys
import threading
import time
import wave
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VoiceConversationSystem:
    """Full voice-to-voice conversation system"""
    
    def __init__(self):
        self.is_listening = False
        self.is_speaking = False
        self.conversation_active = True
        
        # Initialize components
        self.whisper_model = None
        self.tts_engine = None
        self.setup_components()
    
    def setup_components(self):
        """Initialize all voice components"""
        try:
            # Initialize Whisper for STT
            import whisper
            logger.info("Loading Whisper model...")
            self.whisper_model = whisper.load_model("base")
            logger.info("✓ Whisper STT ready")
            
            # Initialize TTS
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 200)
            self.tts_engine.setProperty('volume', 0.9)
            logger.info("✓ TTS engine ready")
            
            logger.info("✓ All voice components initialized!")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    def speak(self, text: str):
        """Speak text using TTS"""
        try:
            self.is_speaking = True
            logger.info(f"🗣️ Josie: {text}")
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()
            self.is_speaking = False
        except Exception as e:
            logger.error(f"TTS error: {e}")
            self.is_speaking = False
    
    def listen_for_speech(self, duration: int = 5) -> Optional[str]:
        """Listen for speech input and convert to text"""
        try:
            import pyaudio
            import numpy as np
            
            # Audio settings
            CHUNK = 1024
            FORMAT = pyaudio.paInt16
            CHANNELS = 1
            RATE = 16000
            
            logger.info(f"🎤 Listening for {duration} seconds... (speak now)")
            
            # Initialize PyAudio
            audio = pyaudio.PyAudio()
            
            # Open stream
            stream = audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK
            )
            
            frames = []
            
            # Record audio
            for _ in range(0, int(RATE / CHUNK * duration)):
                data = stream.read(CHUNK)
                frames.append(data)
            
            # Stop recording
            stream.stop_stream()
            stream.close()
            audio.terminate()
            
            # Convert to numpy array
            audio_data = b''.join(frames)
            audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Use Whisper to transcribe
            logger.info("🔄 Processing speech...")
            result = self.whisper_model.transcribe(audio_np)
            text = result["text"].strip()
            
            if text:
                logger.info(f"👤 You said: {text}")
                return text
            else:
                logger.info("🔇 No speech detected")
                return None
                
        except ImportError:
            logger.error("PyAudio not installed. Install with: pip install pyaudio")
            return None
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return None
    
    async def get_ai_response(self, user_input: str) -> str:
        """Get AI response from Ollama"""
        try:
            import httpx
            
            async with httpx.AsyncClient() as client:
                chat_data = {
                    "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                    "messages": [
                        {"role": "system", "content": "You are Josie, a helpful AI assistant. Keep responses conversational and under 100 words for voice chat."},
                        {"role": "user", "content": user_input}
                    ],
                    "stream": False
                }
                
                response = await client.post(
                    "http://localhost:11434/v1/chat/completions",
                    json=chat_data,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        message = result["choices"][0]["message"]["content"]
                        # Clean up response
                        if "<think>" in message:
                            message = message.split("</think>")[-1].strip()
                        return message
                
                return "I'm sorry, I couldn't process that request."
                
        except Exception as e:
            logger.error(f"AI response error: {e}")
            return "I'm having trouble thinking right now. Could you try again?"
    
    async def conversation_loop(self):
        """Main conversation loop"""
        logger.info("🎙️ Starting voice conversation with Josie...")
        
        # Greeting
        greeting = "Hello! I'm Josie, your AI voice assistant. I can hear you and speak back. Just start talking!"
        self.speak(greeting)
        
        logger.info("\n" + "="*60)
        logger.info("VOICE CONVERSATION ACTIVE")
        logger.info("="*60)
        logger.info("Instructions:")
        logger.info("- Speak naturally when prompted")
        logger.info("- Wait for Josie to finish speaking")
        logger.info("- Say 'goodbye' or 'quit' to end")
        logger.info("- Press Ctrl+C to force quit")
        logger.info("="*60)
        
        try:
            while self.conversation_active:
                # Listen for user input
                user_speech = self.listen_for_speech(duration=6)
                
                if user_speech:
                    # Check for exit commands
                    if any(word in user_speech.lower() for word in ['goodbye', 'quit', 'exit', 'stop', 'bye']):
                        farewell = "Goodbye! It was great talking with you!"
                        self.speak(farewell)
                        break
                    
                    # Get AI response
                    logger.info("🤖 Josie is thinking...")
                    ai_response = await self.get_ai_response(user_speech)
                    
                    # Speak response
                    self.speak(ai_response)
                    
                    # Small pause before next listening cycle
                    await asyncio.sleep(1)
                else:
                    # No speech detected, prompt user
                    prompt = "I'm listening. Please speak."
                    self.speak(prompt)
                    await asyncio.sleep(0.5)
                    
        except KeyboardInterrupt:
            logger.info("\n🛑 Conversation interrupted by user")
            farewell = "Goodbye!"
            self.speak(farewell)
        except Exception as e:
            logger.error(f"Conversation error: {e}")
            error_msg = "I'm sorry, I encountered an error. Goodbye!"
            self.speak(error_msg)
    
    async def test_microphone(self):
        """Test microphone functionality"""
        logger.info("🎤 Testing microphone...")
        
        try:
            import pyaudio
            
            # Test PyAudio
            audio = pyaudio.PyAudio()
            
            # List audio devices
            logger.info("Available audio devices:")
            for i in range(audio.get_device_count()):
                info = audio.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:
                    logger.info(f"  {i}: {info['name']} (inputs: {info['maxInputChannels']})")
            
            audio.terminate()
            
            # Test recording
            test_speech = self.listen_for_speech(duration=3)
            if test_speech:
                logger.info("✓ Microphone test successful!")
                response = f"I heard you say: {test_speech}"
                self.speak(response)
                return True
            else:
                logger.warning("⚠️ No speech detected during test")
                return False
                
        except ImportError:
            logger.error("❌ PyAudio not installed")
            logger.info("Install with: pip install pyaudio")
            return False
        except Exception as e:
            logger.error(f"❌ Microphone test failed: {e}")
            return False


async def main():
    """Main function"""
    try:
        system = VoiceConversationSystem()
        
        if len(sys.argv) > 1:
            if sys.argv[1].lower() == "test":
                # Test microphone
                await system.test_microphone()
            elif sys.argv[1].lower() == "chat":
                # Start voice conversation
                await system.conversation_loop()
            else:
                print("Usage: python full_voice_chat.py [test|chat]")
        else:
            # Default: start conversation
            await system.conversation_loop()
            
    except Exception as e:
        logger.error(f"System error: {e}")
        print("\n❌ Voice system failed to start")
        print("Try: python full_voice_chat.py test")


if __name__ == "__main__":
    asyncio.run(main())

"""
🚀 SIMPLE FAST VOICE SYSTEM
- Type to talk, AI speaks back
- No downloads, no delays
- Uses your Qwen 2.5VL
- Instant startup
"""

import asyncio
import logging
import sys
import time
import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleVoiceSystem:
    """Simple fast voice system - type input, voice output"""
    
    def __init__(self):
        # Core components
        self.ollama_client = None
        self.qwen_model_name = None
        self.conversation_active = False
        self.is_speaking = False
        
        # Voice components
        self.tts_engine = None
        
        # Conversation memory
        self.conversation_history = []
        self.response_times = []
        
    async def initialize(self):
        """Fast initialization"""
        logger.info("🚀 Initializing SIMPLE VOICE SYSTEM...")
        
        try:
            # 1. Connect to Qwen (fast)
            if not await self._connect_to_qwen():
                return False
            
            # 2. Initialize TTS (fast)
            self._initialize_tts()
            
            logger.info("✅ SIMPLE VOICE SYSTEM READY!")
            logger.info(f"🧠 AI: {self.qwen_model_name}")
            logger.info(f"🗣️ TTS: Fast pyttsx3")
            logger.info("⚡ No downloads, instant startup!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            return False
    
    async def _connect_to_qwen(self):
        """Connect to Qwen 2.5VL via Ollama"""
        try:
            self.ollama_client = httpx.AsyncClient(
                base_url="http://localhost:11434",
                timeout=30.0
            )
            
            # Get available models
            response = await self.ollama_client.get("/api/tags")
            if response.status_code != 200:
                logger.error("❌ Ollama not running. Start with: ollama serve")
                return False
            
            models = response.json().get("models", [])
            qwen_models = [m for m in models if "qwen" in m["name"].lower()]
            
            if not qwen_models:
                logger.error("❌ No Qwen models found. Install with: ollama pull qwen2.5-vl")
                return False
            
            # Prefer VL model
            vl_models = [m for m in qwen_models if "vl" in m["name"].lower()]
            self.qwen_model_name = vl_models[0]["name"] if vl_models else qwen_models[0]["name"]
            
            logger.info(f"✅ Connected to: {self.qwen_model_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Qwen connection failed: {e}")
            return False
    
    def _initialize_tts(self):
        """Initialize fast TTS"""
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            
            # Optimize for speed and quality
            self.tts_engine.setProperty('rate', 180)  # Good speed
            self.tts_engine.setProperty('volume', 0.9)
            
            # Try to set a better voice
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Look for a female voice
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            logger.info("✅ Fast TTS ready")
        except Exception as e:
            logger.error(f"❌ TTS failed: {e}")
    
    async def get_ai_response(self, user_text: str) -> str:
        """Get response from Qwen 2.5VL (optimized for speed)"""
        try:
            start_time = time.time()
            
            # Build context (keep minimal for speed)
            context = "\n".join(self.conversation_history[-4:]) if self.conversation_history else ""
            
            # Optimized prompt
            system_prompt = """You are Josie, a helpful and friendly AI assistant. 

Keep responses conversational and under 25 words. Be warm, natural, and engaging."""
            
            full_prompt = f"{system_prompt}\n\nContext: {context}\n\nUser: {user_text}\n\nJosie:"
            
            # Send to Qwen with speed optimizations
            response = await self.ollama_client.post(
                "/api/generate",
                json={
                    "model": self.qwen_model_name,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "num_predict": 40,  # Reasonable length
                        "top_k": 30,
                        "top_p": 0.9
                    }
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result["response"].strip()
                
                # Clean response
                if "Josie:" in ai_response:
                    ai_response = ai_response.split("Josie:")[-1].strip()
                
                # Remove artifacts
                ai_response = ai_response.replace("User:", "").replace("Assistant:", "").strip()
                
                # Limit length
                if len(ai_response) > 120:
                    ai_response = ai_response[:120] + "..."
                
                # Update conversation
                self.conversation_history.append(f"User: {user_text}")
                self.conversation_history.append(f"Josie: {ai_response}")
                
                # Keep recent history
                if len(self.conversation_history) > 12:
                    self.conversation_history = self.conversation_history[-12:]
                
                # Track performance
                response_time = time.time() - start_time
                self.response_times.append(response_time)
                
                logger.info(f"⚡ Response in {response_time:.1f}s")
                return ai_response
            else:
                return "I'm having trouble thinking. Can you repeat that?"
                
        except Exception as e:
            logger.error(f"❌ AI response error: {e}")
            return "Sorry, I didn't catch that. Could you say it again?"
    
    async def speak(self, text: str):
        """Speak with TTS"""
        try:
            self.is_speaking = True
            logger.info(f"🗣️ Josie: {text}")
            
            if self.tts_engine:
                # Speak with TTS
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            else:
                # Fallback to text
                print(f"🗣️ Josie: {text}")
                
        except Exception as e:
            logger.error(f"❌ Speech error: {e}")
            print(f"🗣️ Josie: {text}")
        finally:
            self.is_speaking = False
    
    def get_user_input(self):
        """Get user input via keyboard"""
        try:
            user_text = input("👤 You: ").strip()
            return user_text if user_text else None
        except (EOFError, KeyboardInterrupt):
            return "quit"
    
    async def start_conversation(self):
        """Start the conversation"""
        logger.info("🚀 Starting SIMPLE VOICE CONVERSATION...")
        logger.info("🧠 Powered by your Qwen 2.5VL model")
        logger.info("💬 Type your message, AI will speak back!")
        logger.info("📝 Type 'quit', 'exit', or 'bye' to stop")
        
        self.conversation_active = True
        
        # Welcome message
        await self.speak("Hello! I'm Josie, powered by your Qwen 2.5VL model. What would you like to talk about?")
        
        try:
            while self.conversation_active:
                # Get user input
                user_text = self.get_user_input()
                
                if not user_text:
                    continue
                
                # Check for exit commands
                if any(word in user_text.lower() for word in ['quit', 'exit', 'bye', 'goodbye', 'stop']):
                    await self.speak("It's been wonderful talking with you! Goodbye!")
                    break
                
                # Get AI response
                ai_response = await self.get_ai_response(user_text)
                
                # Speak response
                await self.speak(ai_response)
                    
        except KeyboardInterrupt:
            logger.info("🛑 Conversation stopped by user")
        except Exception as e:
            logger.error(f"❌ Conversation error: {e}")
        finally:
            self.conversation_active = False
            await self._show_stats()
            logger.info("👋 Simple voice conversation ended")
    
    async def _show_stats(self):
        """Show conversation statistics"""
        if self.response_times:
            avg_time = sum(self.response_times) / len(self.response_times)
            logger.info(f"📊 Stats: {len(self.response_times)} exchanges, avg {avg_time:.1f}s response time")


async def main():
    """Main function"""
    try:
        # Create and initialize system
        system = SimpleVoiceSystem()
        
        if await system.initialize():
            # Start conversation
            await system.start_conversation()
        else:
            logger.error("❌ Failed to initialize voice system")
            print("\n💡 Troubleshooting:")
            print("1. Start Ollama: ollama serve")
            print("2. Install pyttsx3: pip install pyttsx3")
            
    except Exception as e:
        logger.error(f"❌ Main error: {e}")


if __name__ == "__main__":
    print("🚀 SIMPLE FAST VOICE SYSTEM")
    print("=" * 40)
    print("🧠 Qwen 2.5VL: Your AI model")
    print("🗣️ Voice output: AI speaks to you")
    print("⌨️ Text input: You type messages")
    print("⚡ Instant startup: No downloads")
    print("🚀 Optimized for SPEED")
    print("=" * 40)
    
    asyncio.run(main())

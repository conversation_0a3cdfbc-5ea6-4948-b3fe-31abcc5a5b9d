"""
NEXT-GENERATION ULTRA-NATURAL VOICE CHAT
- Ultra-low latency streaming conversation
- Real-time interruption support  
- Natural back-and-forth dialogue
- Human-like conversational flow
- State-of-the-art local models
"""

import asyncio
import logging
import sys
import os
import time
import httpx
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.next_gen_voice_system import NextGenVoiceSystem
from core.conversation_engine import ConversationEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class NextGenVoiceAgent:
    """Next-generation ultra-natural voice agent with streaming conversation"""
    
    def __init__(self):
        self.voice_system = None
        self.conversation_engine = None
        self.ollama_client = None
        self.conversation_active = False
        
    async def initialize(self):
        """Initialize next-generation voice agent"""
        logger.info("🚀 Initializing NEXT-GENERATION voice agent...")
        
        try:
            # Initialize voice system
            self.voice_system = NextGenVoiceSystem()
            
            # Initialize conversation engine
            self.conversation_engine = ConversationEngine()
            
            # Initialize Ollama client
            self.ollama_client = httpx.AsyncClient(
                base_url="http://localhost:11434",
                timeout=30.0
            )
            
            # Test Ollama connection
            await self._test_ollama_connection()
            
            logger.info("✅ NEXT-GENERATION voice agent ready!")
            return True
            
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            return False
    
    async def _test_ollama_connection(self):
        """Test Ollama connection"""
        try:
            response = await self.ollama_client.get("/api/tags")
            if response.status_code == 200:
                models = response.json().get("models", [])
                if models:
                    logger.info(f"✓ Ollama connected - {len(models)} models available")
                else:
                    logger.warning("⚠️ Ollama connected but no models found")
            else:
                raise Exception(f"Ollama not responding: {response.status_code}")
        except Exception as e:
            logger.error(f"Ollama connection failed: {e}")
            raise
    
    async def get_ai_response(self, user_text: str) -> str:
        """Get AI response with ultra-low latency"""
        try:
            start_time = time.time()

            # Get conversation context
            context = self.conversation_engine.get_conversation_context_for_ai()

            # Prepare optimized prompt for speed
            prompt = f"""You are Josie, a friendly AI assistant. Respond naturally and conversationally to: "{user_text}"

Keep your response under 30 words. Be warm and engaging. Don't show thinking process - just respond directly.

Recent context: {context}"""

            # Send to Ollama with optimized settings for speed
            response = await self.ollama_client.post(
                "/api/generate",
                json={
                    "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "num_predict": 100  # Shorter for faster response
                    }
                }
            )

            if response.status_code == 200:
                result = response.json()
                ai_response = result["response"].strip()

                # Add to conversation memory using correct method
                self.conversation_engine.add_to_conversation_history(user_text, ai_response)

                response_time = time.time() - start_time
                logger.info(f"⚡ AI response generated in {response_time:.1f}s")

                return ai_response
            else:
                logger.error(f"Ollama API error: {response.status_code}")
                return "I'm having trouble thinking right now. Can you repeat that?"

        except Exception as e:
            logger.error(f"AI response error: {e}")
            return "Sorry, I didn't catch that. Could you say it again?"
    
    async def start_next_gen_conversation(self):
        """Start next-generation streaming conversation"""
        logger.info("🎭 Starting NEXT-GENERATION streaming conversation...")
        
        # Display system info
        system_info = self.voice_system.get_system_info()
        
        print("\n" + "="*80)
        print("🚀 NEXT-GENERATION ULTRA-NATURAL VOICE CONVERSATION")
        print("="*80)
        print("Next-Gen Features:")
        print("  🏠 100% LOCAL processing - no external APIs")
        print("  ⚡ Ultra-low latency streaming voice")
        print("  🗣️ Real-time interruption support")
        print("  💬 Natural back-and-forth conversation")
        print("  🧠 Optimized AI processing")
        print("  🎭 Human-like conversational flow")
        print("  🔒 Complete privacy - everything local")
        print()
        print("Voice System:")
        print(f"  Engine: {system_info.get('engine', 'Unknown')}")
        print(f"  Quality: {system_info.get('quality', 'Unknown')}")
        print(f"  Latency: {system_info.get('latency', 'Unknown')}")
        print(f"  Streaming: {system_info.get('streaming', False)}")
        print(f"  Interruption: {system_info.get('interruption', False)}")
        print(f"  Description: {system_info.get('description', 'Next-gen voice')}")
        print()
        print("Instructions:")
        print("  - Speak naturally - the system is always listening")
        print("  - You can interrupt Josie while she's speaking")
        print("  - Experience ultra-low latency responses")
        print("  - Say 'stop conversation' to end")
        print("="*80)
        
        # Start the streaming conversation
        self.conversation_active = True
        
        try:
            # Give initial greeting
            await self._give_initial_greeting()
            
            # Start streaming conversation loop
            await self.voice_system.start_conversation_stream(self.get_ai_response)
            
        except KeyboardInterrupt:
            logger.info("🛑 Conversation interrupted by user")
        except Exception as e:
            logger.error(f"Conversation error: {e}")
        finally:
            await self._cleanup()
    
    async def _give_initial_greeting(self):
        """Give initial greeting"""
        greeting = "Hey there! I'm Josie with my new ultra-natural voice system. I can now respond much faster and you can even interrupt me while I'm talking! How are you doing today?"
        
        logger.info(f"🗣️ Josie: {greeting}")
        
        # Stream the greeting
        await self.voice_system._stream_tts_response(greeting)
    
    async def _cleanup(self):
        """Cleanup resources"""
        logger.info("🧹 Cleaning up...")
        
        self.conversation_active = False
        
        if self.voice_system:
            self.voice_system.cleanup()
        
        if self.ollama_client:
            await self.ollama_client.aclose()


async def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python next_gen_voice_chat.py [chat|test]")
        return
    
    command = sys.argv[1].lower()
    
    try:
        agent = NextGenVoiceAgent()
        
        if not await agent.initialize():
            logger.error("❌ Failed to initialize next-generation voice agent")
            return
        
        if command == "chat":
            await agent.start_next_gen_conversation()
        elif command == "test":
            await test_next_gen_system(agent)
        else:
            print("Unknown command. Use 'chat' or 'test'")
            
    except Exception as e:
        logger.error(f"Application error: {e}")


async def test_next_gen_system(agent):
    """Test next-generation voice system"""
    logger.info("🧪 Testing NEXT-GENERATION voice system...")
    
    # Test system info
    system_info = agent.voice_system.get_system_info()
    print(f"✓ Voice Engine: {system_info.get('engine')}")
    print(f"✓ Quality: {system_info.get('quality')}")
    print(f"✓ Latency: {system_info.get('latency')}")
    print(f"✓ Streaming: {system_info.get('streaming')}")
    print(f"✓ Interruption: {system_info.get('interruption')}")
    
    # Test AI response
    test_response = await agent.get_ai_response("Hello, how are you?")
    print(f"✓ AI Response: {test_response}")
    
    # Test TTS
    await agent.voice_system._stream_tts_response("This is a test of the next-generation voice system with ultra-low latency and natural conversation flow!")
    
    print("✅ NEXT-GENERATION system test complete!")


if __name__ == "__main__":
    asyncio.run(main())

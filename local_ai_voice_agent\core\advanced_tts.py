"""
Advanced Text-to-Speech with Human-like Voice and Emotions
Creates natural, expressive speech with personality
"""

import asyncio
import logging
import re
import time
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import random

logger = logging.getLogger(__name__)


class VoicePersonality:
    """Manages voice personality and emotional expression"""
    
    def __init__(self):
        # Voice characteristics for <PERSON>
        self.base_settings = {
            "rate": 180,        # Slightly slower for natural conversation
            "volume": 0.85,     # Comfortable volume
            "pitch": "medium",  # Natural pitch
            "voice_id": "female_friendly"
        }
        
        # Emotional voice modulations
        self.emotional_settings = {
            "happy": {"rate": 200, "volume": 0.9, "pitch_mod": "+10%"},
            "excited": {"rate": 220, "volume": 0.95, "pitch_mod": "+15%"},
            "sad": {"rate": 150, "volume": 0.7, "pitch_mod": "-10%"},
            "thoughtful": {"rate": 160, "volume": 0.8, "pitch_mod": "-5%"},
            "surprised": {"rate": 190, "volume": 0.9, "pitch_mod": "+20%"},
            "calm": {"rate": 170, "volume": 0.8, "pitch_mod": "0%"},
            "friendly": {"rate": 185, "volume": 0.85, "pitch_mod": "+5%"},
            "empathetic": {"rate": 165, "volume": 0.8, "pitch_mod": "-3%"}
        }
        
        # Natural speech patterns
        self.speech_patterns = {
            "thinking_sounds": ["um", "uh", "hmm", "well", "you know"],
            "agreement_sounds": ["mm-hmm", "yeah", "right", "exactly"],
            "transition_words": ["so", "anyway", "actually", "by the way"],
            "emphasis_words": ["really", "definitely", "absolutely", "totally"]
        }


class NaturalSpeechProcessor:
    """Processes text to make it sound more natural and human-like"""
    
    def __init__(self):
        self.personality = VoicePersonality()
        
        # Patterns for adding natural speech elements
        self.natural_patterns = {
            # Add pauses for natural flow
            "pause_after": [",", ".", "!", "?", ":", ";"],
            "long_pause_after": [".", "!", "?"],
            
            # Emphasis patterns
            "emphasis_words": ["really", "very", "so", "quite", "absolutely"],
            
            # Breathing patterns
            "breath_points": ["Well,", "So,", "Actually,", "You know,"]
        }
    
    def detect_emotion_from_text(self, text: str) -> str:
        """Detect emotion from text content"""
        text_lower = text.lower()
        
        # Emotion detection patterns
        emotions = {
            "excited": ["amazing", "awesome", "fantastic", "incredible", "wow", "!"],
            "happy": ["great", "wonderful", "good", "nice", "love", "glad"],
            "sad": ["sorry", "unfortunately", "sad", "difficult", "tough"],
            "surprised": ["really?", "no way", "seriously?", "wow", "oh my"],
            "thoughtful": ["hmm", "interesting", "i think", "perhaps", "maybe"],
            "empathetic": ["understand", "feel", "sorry to hear", "that's tough"]
        }
        
        for emotion, keywords in emotions.items():
            if any(keyword in text_lower for keyword in keywords):
                return emotion
        
        return "friendly"  # Default emotion
    
    def add_natural_pauses(self, text: str) -> str:
        """Add natural pauses and breathing to text"""
        # Add short pauses after commas
        text = re.sub(r',', ', <pause:0.3>', text)
        
        # Add longer pauses after sentences
        text = re.sub(r'\.', '. <pause:0.6>', text)
        text = re.sub(r'!', '! <pause:0.5>', text)
        text = re.sub(r'\?', '? <pause:0.5>', text)
        
        # Add breathing before certain phrases
        breath_patterns = [
            (r'\bWell,', '<breath> Well,'),
            (r'\bSo,', '<breath> So,'),
            (r'\bActually,', '<breath> Actually,'),
            (r'\bYou know,', '<breath> You know,')
        ]
        
        for pattern, replacement in breath_patterns:
            text = re.sub(pattern, replacement, text)
        
        return text
    
    def add_emphasis(self, text: str) -> str:
        """Add emphasis to important words"""
        emphasis_words = ["really", "very", "so", "quite", "absolutely", "definitely"]
        
        for word in emphasis_words:
            pattern = r'\b' + word + r'\b'
            replacement = f'<emphasis>{word}</emphasis>'
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        return text
    
    def add_conversational_elements(self, text: str, emotion: str = "friendly") -> str:
        """Add conversational elements based on emotion"""
        # Sometimes add thinking sounds at the beginning
        if random.random() < 0.2:  # 20% chance
            thinking_sounds = ["Um,", "Well,", "You know,", "So,"]
            text = random.choice(thinking_sounds) + " " + text
        
        # Add agreement sounds for positive responses
        if emotion in ["happy", "excited", "friendly"] and random.random() < 0.15:
            agreement_sounds = ["Yeah,", "Right,", "Exactly,"]
            text = random.choice(agreement_sounds) + " " + text
        
        return text
    
    def process_for_natural_speech(self, text: str, emotion: str = None) -> Tuple[str, str]:
        """Process text for natural, human-like speech"""
        # Detect emotion if not provided
        if emotion is None:
            emotion = self.detect_emotion_from_text(text)
        
        # Add conversational elements
        text = self.add_conversational_elements(text, emotion)
        
        # Add natural pauses
        text = self.add_natural_pauses(text)
        
        # Add emphasis
        text = self.add_emphasis(text)
        
        return text, emotion


class AdvancedTTS:
    """Advanced TTS with emotional expression and natural speech"""
    
    def __init__(self):
        self.speech_processor = NaturalSpeechProcessor()
        self.personality = VoicePersonality()
        self.tts_engine = None
        self.initialize_engine()
    
    def initialize_engine(self):
        """Initialize the TTS engine"""
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            
            # Set base voice properties
            self.apply_voice_settings("friendly")
            
            logger.info("✓ Advanced TTS engine initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize TTS engine: {e}")
            raise
    
    def apply_voice_settings(self, emotion: str = "friendly"):
        """Apply voice settings based on emotion"""
        try:
            base = self.personality.base_settings
            emotional = self.personality.emotional_settings.get(emotion, {})
            
            # Apply rate
            rate = emotional.get("rate", base["rate"])
            self.tts_engine.setProperty('rate', rate)
            
            # Apply volume
            volume = emotional.get("volume", base["volume"])
            self.tts_engine.setProperty('volume', volume)
            
            # Note: Pitch modification would require more advanced TTS
            # For now, we'll use rate and volume changes
            
        except Exception as e:
            logger.warning(f"Could not apply voice settings: {e}")
    
    def process_speech_markup(self, text: str) -> str:
        """Process custom speech markup tags"""
        # Remove or process custom tags
        text = re.sub(r'<pause:[\d.]+>', '', text)  # Remove pause tags for basic TTS
        text = re.sub(r'<breath>', '', text)        # Remove breath tags
        text = re.sub(r'<emphasis>(.*?)</emphasis>', r'\1', text)  # Remove emphasis tags
        
        return text.strip()
    
    async def speak_with_emotion(self, text: str, emotion: str = None, user_context: Dict = None):
        """Speak text with emotional expression"""
        try:
            # Process text for natural speech
            processed_text, detected_emotion = self.speech_processor.process_for_natural_speech(text, emotion)
            
            # Apply emotional voice settings
            self.apply_voice_settings(detected_emotion)
            
            # Clean text for TTS engine
            clean_text = self.process_speech_markup(processed_text)
            
            # Log what Josie is saying
            logger.info(f"🗣️ Josie ({detected_emotion}): {clean_text}")
            
            # Speak the text
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            
            return True
            
        except Exception as e:
            logger.error(f"TTS error: {e}")
            return False
    
    def speak_sync(self, text: str, emotion: str = "friendly"):
        """Synchronous speak method"""
        try:
            # Quick processing for sync calls
            _, detected_emotion = self.speech_processor.process_for_natural_speech(text, emotion)
            self.apply_voice_settings(detected_emotion)
            
            clean_text = self.process_speech_markup(text)
            logger.info(f"🗣️ Josie: {clean_text}")
            
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            
        except Exception as e:
            logger.error(f"Sync TTS error: {e}")
    
    def get_voice_info(self) -> Dict:
        """Get information about available voices"""
        try:
            voices = self.tts_engine.getProperty('voices')
            voice_info = []
            
            for voice in voices:
                voice_info.append({
                    "id": voice.id,
                    "name": voice.name,
                    "gender": getattr(voice, 'gender', 'unknown'),
                    "age": getattr(voice, 'age', 'unknown')
                })
            
            return {
                "available_voices": voice_info,
                "current_settings": self.personality.base_settings,
                "emotions_supported": list(self.personality.emotional_settings.keys())
            }
            
        except Exception as e:
            logger.error(f"Could not get voice info: {e}")
            return {}
    
    def set_voice_by_preference(self, preference: str = "female"):
        """Set voice based on user preference"""
        try:
            voices = self.tts_engine.getProperty('voices')
            
            # Try to find a suitable voice
            for voice in voices:
                voice_name = voice.name.lower()
                if preference.lower() in voice_name or "female" in voice_name:
                    self.tts_engine.setProperty('voice', voice.id)
                    logger.info(f"Set voice to: {voice.name}")
                    return True
            
            # Fallback to first available voice
            if voices:
                self.tts_engine.setProperty('voice', voices[0].id)
                logger.info(f"Using default voice: {voices[0].name}")
                return True
                
        except Exception as e:
            logger.warning(f"Could not set voice preference: {e}")
        
        return False


# Convenience functions for easy use
async def speak_naturally(text: str, emotion: str = "friendly", tts_engine: AdvancedTTS = None):
    """Convenience function to speak with natural voice"""
    if tts_engine is None:
        tts_engine = AdvancedTTS()
    
    await tts_engine.speak_with_emotion(text, emotion)


def create_advanced_tts() -> AdvancedTTS:
    """Factory function to create advanced TTS instance"""
    return AdvancedTTS()

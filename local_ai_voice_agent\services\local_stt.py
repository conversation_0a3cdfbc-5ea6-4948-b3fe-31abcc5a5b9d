"""
Local Speech-to-Text implementation using Whisper
Provides offline speech recognition without external APIs
"""

import asyncio
import io
import logging
import tempfile
import wave
from typing import Optional, Union

import numpy as np
import torch
from livekit import rtc
from livekit.agents import stt
from livekit.agents.utils import AudioBuffer

logger = logging.getLogger(__name__)


class LocalWhisperSTT(stt.STT):
    """Local Whisper STT implementation"""
    
    def __init__(
        self,
        *,
        model: str = "large-v3",
        language: Optional[str] = "en",
        device: str = "auto",
        compute_type: str = "float16",
        model_path: Optional[str] = None,
        temperature: float = 0.0,
        beam_size: int = 5,
        best_of: int = 5,
        patience: float = 1.0,
        suppress_tokens: Optional[list] = None,
        initial_prompt: Optional[str] = None,
        condition_on_previous_text: bool = True,
        fp16: bool = True,
        compression_ratio_threshold: float = 2.4,
        logprob_threshold: float = -1.0,
        no_speech_threshold: float = 0.6,
    ):
        """
        Initialize Local Whisper STT
        
        Args:
            model: Whisper model size ("tiny", "base", "small", "medium", "large", "large-v2", "large-v3")
            language: Language code (None for auto-detection)
            device: Device to use ("auto", "cpu", "cuda")
            compute_type: Computation type ("float16", "float32", "int8")
            model_path: Custom model path
            temperature: Sampling temperature
            beam_size: Beam search size
            best_of: Number of candidates to consider
            patience: Beam search patience
            suppress_tokens: Tokens to suppress
            initial_prompt: Initial prompt for the model
            condition_on_previous_text: Whether to condition on previous text
            fp16: Use FP16 precision
            compression_ratio_threshold: Compression ratio threshold
            logprob_threshold: Log probability threshold
            no_speech_threshold: No speech threshold
        """
        super().__init__(
            capabilities=stt.STTCapabilities(streaming=False, interim_results=False)
        )
        
        self._model_name = model
        self._language = language
        self._device = self._get_device(device)
        self._compute_type = compute_type
        self._model_path = model_path
        
        # Whisper parameters
        self._temperature = temperature
        self._beam_size = beam_size
        self._best_of = best_of
        self._patience = patience
        self._suppress_tokens = suppress_tokens or [-1]
        self._initial_prompt = initial_prompt
        self._condition_on_previous_text = condition_on_previous_text
        self._fp16 = fp16
        self._compression_ratio_threshold = compression_ratio_threshold
        self._logprob_threshold = logprob_threshold
        self._no_speech_threshold = no_speech_threshold
        
        self._model = None
        self._load_model()
    
    def _get_device(self, device: str) -> str:
        """Determine the best device to use"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        return device
    
    def _load_model(self):
        """Load the Whisper model"""
        try:
            import whisper
            
            if self._model_path:
                self._model = whisper.load_model(self._model_path, device=self._device)
            else:
                self._model = whisper.load_model(self._model_name, device=self._device)
            
            logger.info(f"Loaded Whisper model '{self._model_name}' on device '{self._device}'")
            
        except ImportError:
            raise RuntimeError("Whisper not installed. Install with: pip install openai-whisper")
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            raise
    
    async def _recognize_impl(
        self,
        buffer: AudioBuffer,
        *,
        language: Optional[str] = None,
    ) -> stt.SpeechEvent:
        """Recognize speech from audio buffer"""
        try:
            # Use provided language or default
            lang = language or self._language
            
            # Convert audio buffer to numpy array
            audio_data = self._buffer_to_numpy(buffer)
            
            # Run Whisper inference in thread pool
            result = await asyncio.get_event_loop().run_in_executor(
                None, self._transcribe_audio, audio_data, lang
            )
            
            # Extract transcription
            text = result.get("text", "").strip()
            detected_language = result.get("language", lang or "en")
            
            # Check if speech was detected
            if not text or len(text) < 2:
                return stt.SpeechEvent(
                    type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                    alternatives=[
                        stt.SpeechData(text="", language=detected_language)
                    ]
                )
            
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text=text, language=detected_language)
                ]
            )
            
        except Exception as e:
            logger.error(f"Speech recognition failed: {e}")
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text="", language=language or "en")
                ]
            )
    
    def _buffer_to_numpy(self, buffer: AudioBuffer) -> np.ndarray:
        """Convert AudioBuffer to numpy array for Whisper"""
        try:
            # Combine all frames in the buffer
            combined_frame = rtc.combine_audio_frames(buffer)
            
            # Convert to numpy array
            audio_data = np.frombuffer(combined_frame.data, dtype=np.int16)
            
            # Convert to float32 and normalize
            audio_data = audio_data.astype(np.float32) / 32768.0
            
            # Ensure mono audio
            if combined_frame.num_channels > 1:
                audio_data = audio_data.reshape(-1, combined_frame.num_channels)
                audio_data = np.mean(audio_data, axis=1)
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Failed to convert audio buffer: {e}")
            return np.array([], dtype=np.float32)
    
    def _transcribe_audio(self, audio_data: np.ndarray, language: Optional[str]) -> dict:
        """Transcribe audio using Whisper model"""
        try:
            # Prepare transcription options
            options = {
                "language": language,
                "temperature": self._temperature,
                "beam_size": self._beam_size,
                "best_of": self._best_of,
                "patience": self._patience,
                "suppress_tokens": self._suppress_tokens,
                "initial_prompt": self._initial_prompt,
                "condition_on_previous_text": self._condition_on_previous_text,
                "fp16": self._fp16,
                "compression_ratio_threshold": self._compression_ratio_threshold,
                "logprob_threshold": self._logprob_threshold,
                "no_speech_threshold": self._no_speech_threshold,
            }
            
            # Remove None values
            options = {k: v for k, v in options.items() if v is not None}
            
            # Transcribe
            result = self._model.transcribe(audio_data, **options)
            
            return result
            
        except Exception as e:
            logger.error(f"Whisper transcription failed: {e}")
            return {"text": "", "language": language or "en"}


class LocalWhisperStreamSTT(stt.STT):
    """Streaming version of Local Whisper STT using VAD"""
    
    def __init__(
        self,
        *,
        whisper_stt: LocalWhisperSTT,
        vad_model,
        chunk_duration: float = 1.0,
        min_speech_duration: float = 0.5,
        max_speech_duration: float = 30.0,
    ):
        """
        Initialize streaming Whisper STT with VAD
        
        Args:
            whisper_stt: LocalWhisperSTT instance
            vad_model: Voice Activity Detection model
            chunk_duration: Duration of audio chunks in seconds
            min_speech_duration: Minimum speech duration to process
            max_speech_duration: Maximum speech duration to process
        """
        super().__init__(
            capabilities=stt.STTCapabilities(streaming=True, interim_results=False)
        )
        
        self._whisper_stt = whisper_stt
        self._vad = vad_model
        self._chunk_duration = chunk_duration
        self._min_speech_duration = min_speech_duration
        self._max_speech_duration = max_speech_duration
        
        self._audio_buffer = []
        self._speech_started = False
        self._speech_start_time = 0
    
    async def _recognize_impl(
        self,
        buffer: AudioBuffer,
        *,
        language: Optional[str] = None,
    ) -> stt.SpeechEvent:
        """Process streaming audio with VAD"""
        try:
            # Add new audio to buffer
            self._audio_buffer.extend(buffer)
            
            # Check for speech activity
            speech_detected = await self._detect_speech_activity(buffer)
            
            if speech_detected and not self._speech_started:
                # Speech started
                self._speech_started = True
                self._speech_start_time = len(self._audio_buffer)
                
            elif not speech_detected and self._speech_started:
                # Speech ended, process accumulated audio
                self._speech_started = False
                
                # Extract speech segment
                speech_buffer = self._audio_buffer[self._speech_start_time:]
                
                if len(speech_buffer) > 0:
                    # Transcribe the speech segment
                    result = await self._whisper_stt._recognize_impl(
                        speech_buffer, language=language
                    )
                    
                    # Clear processed audio
                    self._audio_buffer = []
                    
                    return result
            
            # Return empty result for non-speech or ongoing speech
            return stt.SpeechEvent(
                type=stt.SpeechEventType.INTERIM_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text="", language=language or "en")
                ]
            )
            
        except Exception as e:
            logger.error(f"Streaming speech recognition failed: {e}")
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(text="", language=language or "en")
                ]
            )
    
    async def _detect_speech_activity(self, buffer: AudioBuffer) -> bool:
        """Detect speech activity using VAD"""
        try:
            # Convert buffer to format expected by VAD
            combined_frame = rtc.combine_audio_frames(buffer)
            audio_data = np.frombuffer(combined_frame.data, dtype=np.int16)
            
            # Run VAD detection
            speech_prob = await asyncio.get_event_loop().run_in_executor(
                None, self._vad, audio_data, combined_frame.sample_rate
            )
            
            # Return True if speech probability is above threshold
            return speech_prob > 0.5
            
        except Exception as e:
            logger.error(f"VAD detection failed: {e}")
            return False

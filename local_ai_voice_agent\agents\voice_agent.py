"""
Main Local Voice Agent implementation
Combines STT, LLM, TTS, and Vision capabilities for multimodal conversations
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    JobProcess,
    RunContext,
    WorkerOptions,
    cli,
    llm,
    metrics,
)
from livekit.agents.llm import function_tool

from ..services.local_tts import LocalTTS
from ..services.local_stt import LocalWhisperSTT
from ..services.ollama_llm import OllamaLL<PERSON>, QwenVisionLLM
from ..services.vision_service import VisionService
from ..utils.model_utils import ModelManager
from ..utils.audio_utils import AudioProcessor

logger = logging.getLogger(__name__)


class LocalVoiceAgent(Agent):
    """Local AI Voice Agent with multimodal capabilities"""
    
    def __init__(
        self,
        *,
        instructions: Optional[str] = None,
        model_config: Optional[Dict[str, Any]] = None,
        enable_vision: bool = True,
        enable_function_calling: bool = True,
    ):
        """
        Initialize Local Voice Agent
        
        Args:
            instructions: Agent instructions/personality
            model_config: Model configuration dictionary
            enable_vision: Whether to enable vision capabilities
            enable_function_calling: Whether to enable function calling
        """
        default_instructions = (
            "You are a helpful AI voice assistant with vision capabilities. "
            "You can see images, hear speech, and respond naturally through voice. "
            "Keep your responses concise and conversational for voice interactions. "
            "When processing images, describe what you see clearly and answer questions about the visual content. "
            "You are friendly, curious, and helpful."
        )
        
        super().__init__(
            instructions=instructions or default_instructions,
        )
        
        self._model_config = model_config or {}
        self._enable_vision = enable_vision
        self._enable_function_calling = enable_function_calling
        
        # Initialize services
        self._model_manager = ModelManager()
        self._audio_processor = AudioProcessor()
        self._vision_service = None
        
        # Conversation state
        self._conversation_history = []
        self._current_image = None
        self._session_metrics = {}
    
    async def on_enter(self):
        """Called when agent enters the session"""
        logger.info("Local Voice Agent entering session")
        
        # Initialize vision service if enabled
        if self._enable_vision:
            try:
                vision_llm = QwenVisionLLM()
                self._vision_service = VisionService(vision_llm=vision_llm)
                logger.info("Vision capabilities enabled")
            except Exception as e:
                logger.warning(f"Failed to initialize vision service: {e}")
                self._enable_vision = False
        
        # Generate initial greeting
        await self.session.generate_reply(
            instructions="Greet the user warmly and let them know about your capabilities. "
                        "Mention that you can see images if vision is enabled, and that you're ready to help."
        )
    
    async def on_user_turn_completed(self, chat_ctx: llm.ChatContext, new_message: llm.ChatMessage):
        """Handle completed user turn"""
        try:
            # Check if the message contains an image
            if hasattr(new_message, 'images') and new_message.images and self._enable_vision:
                await self._handle_image_message(chat_ctx, new_message)
            else:
                # Regular text conversation
                await self._handle_text_message(chat_ctx, new_message)
                
        except Exception as e:
            logger.error(f"Error handling user turn: {e}")
            await self.session.generate_reply(
                instructions="Apologize for the error and ask the user to try again."
            )
    
    async def _handle_image_message(self, chat_ctx: llm.ChatContext, message: llm.ChatMessage):
        """Handle messages containing images"""
        if not self._vision_service:
            await self.session.generate_reply(
                instructions="Explain that vision capabilities are not available right now."
            )
            return
        
        try:
            # Get the image data
            image_data = message.images[0]  # Take the first image
            self._current_image = image_data
            
            # Analyze the image
            user_text = message.text_content or "What do you see in this image?"
            analysis = await self._vision_service.analyze_image(
                image_data,
                user_text,
                chat_ctx=chat_ctx
            )
            
            # Generate response based on analysis
            response_instructions = (
                f"The user has shared an image and asked: '{user_text}'. "
                f"Based on your vision analysis: {analysis}. "
                f"Respond naturally and conversationally about what you see."
            )
            
            await self.session.generate_reply(instructions=response_instructions)
            
        except Exception as e:
            logger.error(f"Error handling image message: {e}")
            await self.session.generate_reply(
                instructions="Apologize that you couldn't process the image and ask them to try again."
            )
    
    async def _handle_text_message(self, chat_ctx: llm.ChatContext, message: llm.ChatMessage):
        """Handle text-only messages"""
        user_text = message.text_content.lower()
        
        # Check for special commands or requests
        if "show" in user_text and "image" in user_text and self._current_image:
            # User wants to discuss the current image
            await self._discuss_current_image(chat_ctx, message.text_content)
        elif any(word in user_text for word in ["see", "look", "image", "picture", "photo"]):
            # User is asking about vision capabilities
            if self._enable_vision:
                await self.session.generate_reply(
                    instructions="Explain that you can see and analyze images. "
                               "Ask the user to share an image if they'd like you to look at something."
                )
            else:
                await self.session.generate_reply(
                    instructions="Explain that vision capabilities are not currently available."
                )
        else:
            # Regular conversation - let the LLM handle it naturally
            pass  # The session will handle this automatically
    
    async def _discuss_current_image(self, chat_ctx: llm.ChatContext, user_question: str):
        """Discuss the currently loaded image"""
        if not self._current_image or not self._vision_service:
            await self.session.generate_reply(
                instructions="Explain that there's no image currently loaded to discuss."
            )
            return
        
        try:
            # Analyze the current image with the user's question
            analysis = await self._vision_service.answer_visual_question(
                self._current_image,
                user_question,
                chat_ctx=chat_ctx
            )
            
            response_instructions = (
                f"The user asked about the current image: '{user_question}'. "
                f"Based on your analysis: {analysis}. "
                f"Respond naturally and helpfully."
            )
            
            await self.session.generate_reply(instructions=response_instructions)
            
        except Exception as e:
            logger.error(f"Error discussing current image: {e}")
            await self.session.generate_reply(
                instructions="Apologize that you couldn't analyze the image right now."
            )
    
    # Function tools for enhanced capabilities
    @function_tool
    async def analyze_image_detail(
        self,
        context: RunContext,
        detail_level: str = "medium",
        focus_areas: str = "",
    ):
        """
        Analyze the current image with specific detail level and focus areas.
        
        Args:
            detail_level: Level of detail ("brief", "medium", "detailed")
            focus_areas: Comma-separated areas to focus on (e.g., "people, objects, colors")
        """
        if not self._current_image or not self._vision_service:
            return "No image is currently loaded for analysis."
        
        try:
            focus_list = [area.strip() for area in focus_areas.split(",")] if focus_areas else None
            
            result = await self._vision_service.describe_scene(
                self._current_image,
                detail_level=detail_level,
                focus_areas=focus_list
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            return "I couldn't analyze the image due to an error."
    
    @function_tool
    async def read_text_from_image(self, context: RunContext):
        """
        Read and extract any text visible in the current image.
        """
        if not self._current_image or not self._vision_service:
            return "No image is currently loaded for text extraction."
        
        try:
            result = await self._vision_service.read_text(self._current_image)
            return result
            
        except Exception as e:
            logger.error(f"Text extraction failed: {e}")
            return "I couldn't extract text from the image."
    
    @function_tool
    async def detect_objects_in_image(
        self,
        context: RunContext,
        object_types: str = "",
    ):
        """
        Detect and identify objects in the current image.
        
        Args:
            object_types: Comma-separated list of specific objects to look for
        """
        if not self._current_image or not self._vision_service:
            return "No image is currently loaded for object detection."
        
        try:
            object_list = [obj.strip() for obj in object_types.split(",")] if object_types else None
            
            result = await self._vision_service.detect_objects(
                self._current_image,
                object_types=object_list
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Object detection failed: {e}")
            return "I couldn't detect objects in the image."
    
    @function_tool
    async def get_conversation_summary(self, context: RunContext):
        """
        Get a summary of the current conversation.
        """
        if not self._conversation_history:
            return "This is the beginning of our conversation."
        
        # Create a simple summary of recent messages
        recent_messages = self._conversation_history[-10:]  # Last 10 messages
        summary = "Recent conversation topics: "
        
        topics = []
        for msg in recent_messages:
            if len(msg) > 20:
                topics.append(msg[:50] + "...")
        
        if topics:
            summary += "; ".join(topics)
        else:
            summary = "We've been having a brief conversation."
        
        return summary
    
    async def on_metrics_collected(self, metrics_data: Dict[str, Any]):
        """Handle metrics collection"""
        self._session_metrics.update(metrics_data)
        logger.debug(f"Metrics collected: {metrics_data}")


def prewarm(proc: JobProcess):
    """Prewarm function to load models before session starts"""
    try:
        # Load VAD model
        proc.userdata["vad"] = silero.VAD.load()
        logger.info("VAD model preloaded")
        
        # Preload other models if needed
        # This could include warming up Ollama models
        
    except Exception as e:
        logger.error(f"Prewarm failed: {e}")


async def create_agent_session(ctx: JobContext) -> AgentSession:
    """Create and configure the agent session"""
    try:
        # Initialize local services
        local_tts = LocalTTS(engine="auto", rate=200, volume=0.9)
        local_stt = LocalWhisperSTT(model="large-v3", language="en")
        ollama_llm = OllamaLLM(model="goekdenizguelmez/JOSIEFIED-Qwen3:14b")
        
        # Create agent session
        session = AgentSession(
            vad=ctx.proc.userdata["vad"],
            stt=local_stt,
            llm=ollama_llm,
            tts=local_tts,
            turn_detection=MultilingualModel(),
        )
        
        # Set up metrics collection
        usage_collector = metrics.UsageCollector()
        
        @session.on("metrics_collected")
        def _on_metrics_collected(ev: MetricsCollectedEvent):
            metrics.log_metrics(ev.metrics)
            usage_collector.collect(ev.metrics)
        
        async def log_usage():
            summary = usage_collector.get_summary()
            logger.info(f"Session usage: {summary}")
        
        ctx.add_shutdown_callback(log_usage)
        
        return session
        
    except Exception as e:
        logger.error(f"Failed to create agent session: {e}")
        raise

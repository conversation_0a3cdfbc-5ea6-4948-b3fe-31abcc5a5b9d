"""
Vision capabilities demo for Local AI Voice Agent
Demonstrates image analysis and multimodal interactions
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from local_ai_voice_agent.services.ollama_llm import QwenVisionLLM
from local_ai_voice_agent.services.vision_service import VisionService
from local_ai_voice_agent.utils.model_utils import ModelManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VisionDemo:
    """Vision capabilities demonstration"""
    
    def __init__(self):
        self.model_manager = ModelManager()
        self.vision_service = None
        self.vision_llm = None
    
    async def initialize(self):
        """Initialize vision services"""
        try:
            logger.info("Initializing Vision Demo...")
            
            # Initialize Vision LLM
            logger.info("Loading Qwen 2.5-VL model...")
            self.vision_llm = QwenVisionLLM()
            
            # Initialize Vision Service
            logger.info("Loading Vision Service...")
            self.vision_service = VisionService(vision_llm=self.vision_llm)
            
            logger.info("✓ Vision services initialized successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize vision services: {e}")
            return False
    
    async def test_image_description(self, image_path: str):
        """Test basic image description"""
        logger.info(f"Testing image description with: {image_path}")
        
        try:
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return False
            
            # Analyze image
            result = await self.vision_service.describe_scene(
                image_path,
                detail_level="detailed"
            )
            
            logger.info("Image Description:")
            logger.info(f"  {result}")
            
            return True
            
        except Exception as e:
            logger.error(f"Image description test failed: {e}")
            return False
    
    async def test_visual_question_answering(self, image_path: str, question: str):
        """Test visual question answering"""
        logger.info(f"Testing VQA with question: {question}")
        
        try:
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return False
            
            # Answer question about image
            result = await self.vision_service.answer_visual_question(
                image_path,
                question
            )
            
            logger.info(f"Question: {question}")
            logger.info(f"Answer: {result}")
            
            return True
            
        except Exception as e:
            logger.error(f"VQA test failed: {e}")
            return False
    
    async def test_object_detection(self, image_path: str, objects: list = None):
        """Test object detection"""
        logger.info("Testing object detection...")
        
        try:
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return False
            
            # Detect objects
            result = await self.vision_service.detect_objects(
                image_path,
                object_types=objects
            )
            
            logger.info("Detected Objects:")
            logger.info(f"  {result}")
            
            return True
            
        except Exception as e:
            logger.error(f"Object detection test failed: {e}")
            return False
    
    async def test_text_extraction(self, image_path: str):
        """Test text extraction from image"""
        logger.info("Testing text extraction...")
        
        try:
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return False
            
            # Extract text
            result = await self.vision_service.read_text(image_path)
            
            logger.info("Extracted Text:")
            logger.info(f"  {result}")
            
            return True
            
        except Exception as e:
            logger.error(f"Text extraction test failed: {e}")
            return False
    
    async def interactive_vision_demo(self):
        """Run interactive vision demo"""
        logger.info("Starting interactive vision demo...")
        logger.info("Commands:")
        logger.info("  load <image_path> - Load an image")
        logger.info("  describe - Describe the current image")
        logger.info("  ask <question> - Ask a question about the image")
        logger.info("  objects [object_types] - Detect objects")
        logger.info("  text - Extract text from image")
        logger.info("  info - Get image information")
        logger.info("  quit - Exit demo")
        
        current_image = None
        
        while True:
            try:
                user_input = input("\nVision> ").strip()
                
                if not user_input:
                    continue
                
                parts = user_input.split(' ', 1)
                command = parts[0].lower()
                args = parts[1] if len(parts) > 1 else ""
                
                if command == 'quit':
                    print("Goodbye!")
                    break
                
                elif command == 'load':
                    if not args:
                        print("Please provide an image path: load <image_path>")
                        continue
                    
                    image_path = args.strip()
                    if os.path.exists(image_path):
                        current_image = image_path
                        print(f"✓ Loaded image: {image_path}")
                        
                        # Show basic info
                        info = self.vision_service.get_image_info(image_path)
                        print(f"  Size: {info.get('size', 'Unknown')}")
                        print(f"  Format: {info.get('format', 'Unknown')}")
                    else:
                        print(f"✗ Image not found: {image_path}")
                
                elif command == 'describe':
                    if not current_image:
                        print("Please load an image first")
                        continue
                    
                    print("Analyzing image...")
                    result = await self.vision_service.describe_scene(
                        current_image,
                        detail_level="detailed"
                    )
                    print(f"Description: {result}")
                
                elif command == 'ask':
                    if not current_image:
                        print("Please load an image first")
                        continue
                    
                    if not args:
                        print("Please provide a question: ask <question>")
                        continue
                    
                    print("Analyzing image...")
                    result = await self.vision_service.answer_visual_question(
                        current_image,
                        args
                    )
                    print(f"Answer: {result}")
                
                elif command == 'objects':
                    if not current_image:
                        print("Please load an image first")
                        continue
                    
                    object_types = args.split(',') if args else None
                    if object_types:
                        object_types = [obj.strip() for obj in object_types]
                    
                    print("Detecting objects...")
                    result = await self.vision_service.detect_objects(
                        current_image,
                        object_types=object_types
                    )
                    print(f"Objects: {result}")
                
                elif command == 'text':
                    if not current_image:
                        print("Please load an image first")
                        continue
                    
                    print("Extracting text...")
                    result = await self.vision_service.read_text(current_image)
                    print(f"Text: {result}")
                
                elif command == 'info':
                    if not current_image:
                        print("Please load an image first")
                        continue
                    
                    info = self.vision_service.get_image_info(current_image)
                    print("Image Information:")
                    for key, value in info.items():
                        print(f"  {key}: {value}")
                
                else:
                    print(f"Unknown command: {command}")
                    print("Type 'quit' to exit or see commands above")
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                logger.error(f"Error in interactive demo: {e}")
                print("An error occurred. Please try again.")
    
    async def run_sample_tests(self):
        """Run tests with sample images if available"""
        logger.info("Looking for sample images...")
        
        # Look for common image files in current directory
        sample_images = []
        for ext in ['jpg', 'jpeg', 'png', 'bmp', 'webp']:
            for file in Path('.').glob(f'*.{ext}'):
                sample_images.append(str(file))
        
        if not sample_images:
            logger.info("No sample images found in current directory")
            logger.info("You can:")
            logger.info("1. Place some images in the current directory")
            logger.info("2. Use the interactive demo to load specific images")
            return False
        
        logger.info(f"Found {len(sample_images)} sample images")
        
        # Test with first image
        test_image = sample_images[0]
        logger.info(f"Testing with: {test_image}")
        
        tests = [
            ("Image Description", self.test_image_description, [test_image]),
            ("Visual Q&A", self.test_visual_question_answering, [test_image, "What do you see in this image?"]),
            ("Object Detection", self.test_object_detection, [test_image]),
            ("Text Extraction", self.test_text_extraction, [test_image]),
        ]
        
        results = {}
        for test_name, test_func, args in tests:
            logger.info(f"\n--- {test_name} Test ---")
            results[test_name] = await test_func(*args)
        
        # Summary
        logger.info("\n--- Test Summary ---")
        for test_name, passed in results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            logger.info(f"{test_name}: {status}")
        
        return all(results.values())


async def main():
    """Main demo function"""
    demo = VisionDemo()
    
    # Initialize services
    if not await demo.initialize():
        logger.error("Failed to initialize vision demo. Exiting.")
        return
    
    # Check command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "test":
            await demo.run_sample_tests()
        elif command == "interactive":
            await demo.interactive_vision_demo()
        elif command == "describe" and len(sys.argv) > 2:
            await demo.test_image_description(sys.argv[2])
        elif command == "ask" and len(sys.argv) > 3:
            await demo.test_visual_question_answering(sys.argv[2], sys.argv[3])
        else:
            print(f"Unknown command: {command}")
            print("Available commands:")
            print("  test - Run tests with sample images")
            print("  interactive - Start interactive demo")
            print("  describe <image_path> - Describe an image")
            print("  ask <image_path> <question> - Ask about an image")
    else:
        # Default: try sample tests, then interactive
        logger.info("Running vision demo...")
        
        if await demo.run_sample_tests():
            print("\nSample tests completed. Starting interactive demo...")
        else:
            print("\nStarting interactive demo...")
        
        await demo.interactive_vision_demo()


if __name__ == "__main__":
    asyncio.run(main())

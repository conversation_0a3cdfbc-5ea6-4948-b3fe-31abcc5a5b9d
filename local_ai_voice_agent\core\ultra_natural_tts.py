"""
Ultra-Natural Text-to-Speech System
The most human-like, conversational AI voices available
"""

import asyncio
import logging
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading
import queue

logger = logging.getLogger(__name__)


class UltraNaturalTTS:
    """Ultra-natural TTS with the most human-like voices"""
    
    def __init__(self):
        self.current_engine = None
        self.available_engines = {}
        self.voice_settings = {
            "voice_id": "bella_natural",  # Ultra-natural female voice
            "stability": 0.75,
            "similarity_boost": 0.85,
            "style": 0.65,
            "use_speaker_boost": True
        }
        
        # Initialize the best available engine
        self.initialize_ultra_natural_engine()
    
    def initialize_ultra_natural_engine(self):
        """Initialize the most natural TTS engine available"""
        logger.info("🎭 Initializing ultra-natural voice system...")
        
        # Try engines in order of naturalness
        engines_to_try = [
            ("elevenlabs", self._init_elevenlabs),
            ("openai_tts", self._init_openai_tts),
            ("tortoise_tts", self._init_tortoise_tts),
            ("xtts_v2", self._init_xtts_v2),
            ("edge_tts_enhanced", self._init_edge_tts_enhanced)
        ]
        
        for engine_name, init_func in engines_to_try:
            try:
                logger.info(f"Trying {engine_name}...")
                if init_func():
                    self.current_engine = engine_name
                    logger.info(f"✓ Successfully initialized {engine_name}")
                    return True
            except Exception as e:
                logger.warning(f"Failed to initialize {engine_name}: {e}")
                continue
        
        logger.error("❌ No ultra-natural TTS engines could be initialized")
        return False
    
    def _init_elevenlabs(self) -> bool:
        """Initialize ElevenLabs (most natural)"""
        try:
            from elevenlabs import generate, set_api_key, voices
            
            # Check for API key
            api_key = os.getenv('ELEVENLABS_API_KEY')
            if not api_key:
                logger.info("ElevenLabs API key not found. Set ELEVENLABS_API_KEY environment variable")
                return False
            
            set_api_key(api_key)
            
            # Test API connection
            available_voices = voices()
            
            # Premium voice options
            self.elevenlabs_voices = {
                "bella_natural": "EXAVITQu4vr4xnSDxMaL",  # Bella - very natural
                "rachel_conversational": "21m00Tcm4TlvDq8ikWAM",  # Rachel - conversational
                "domi_friendly": "AZnzlk1XvdvUeBnXmlld",  # Domi - friendly
                "elli_calm": "MF3mGyEYCl7XYWbV9V6O",  # Elli - calm
                "josh_natural": "TxGEqnHWrfWFTfGW9XjX",  # Josh - natural male
                "antoni_warm": "ErXwobaYiN019PkySvjV",  # Antoni - warm male
                "arnold_deep": "VR6AewLTigWG4xSOukaG",  # Arnold - deep male
                "adam_natural": "pNInz6obpgDQGcFmaJgB",  # Adam - natural male
                "sam_serious": "yoZ06aMxZJJ28mfd3POQ"   # Sam - serious male
            }
            
            self.available_engines["elevenlabs"] = {
                "name": "ElevenLabs AI",
                "quality": "ultra_natural",
                "description": "Most human-like AI voices with emotional expression"
            }
            
            logger.info("✓ ElevenLabs initialized successfully")
            return True
            
        except ImportError:
            logger.info("ElevenLabs not installed. Install with: pip install elevenlabs")
            return False
        except Exception as e:
            logger.warning(f"ElevenLabs initialization failed: {e}")
            return False
    
    def _init_openai_tts(self) -> bool:
        """Initialize OpenAI TTS (very natural)"""
        try:
            import openai
            
            # Check for API key
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                logger.info("OpenAI API key not found. Set OPENAI_API_KEY environment variable")
                return False
            
            self.openai_client = openai.OpenAI(api_key=api_key)
            
            # Premium voice options
            self.openai_voices = {
                "alloy_natural": "alloy",      # Balanced, natural
                "echo_conversational": "echo", # Conversational male
                "fable_expressive": "fable",   # Expressive, warm
                "onyx_deep": "onyx",          # Deep, authoritative
                "nova_friendly": "nova",       # Friendly female
                "shimmer_gentle": "shimmer"    # Gentle, soft
            }
            
            self.available_engines["openai_tts"] = {
                "name": "OpenAI TTS",
                "quality": "very_natural",
                "description": "GPT-4 level voice synthesis with natural speech patterns"
            }
            
            logger.info("✓ OpenAI TTS initialized successfully")
            return True
            
        except ImportError:
            logger.info("OpenAI not installed. Install with: pip install openai")
            return False
        except Exception as e:
            logger.warning(f"OpenAI TTS initialization failed: {e}")
            return False
    
    def _init_tortoise_tts(self) -> bool:
        """Initialize Tortoise TTS (ultra-realistic)"""
        try:
            # This would require tortoise-tts installation
            # from tortoise.api import TextToSpeech
            
            logger.info("Tortoise TTS requires manual installation and setup")
            return False
            
        except ImportError:
            logger.info("Tortoise TTS not available")
            return False
        except Exception as e:
            logger.warning(f"Tortoise TTS initialization failed: {e}")
            return False
    
    def _init_xtts_v2(self) -> bool:
        """Initialize XTTS v2 (real-time voice cloning)"""
        try:
            # This would require XTTS v2 installation
            logger.info("XTTS v2 requires manual installation and setup")
            return False
            
        except ImportError:
            logger.info("XTTS v2 not available")
            return False
        except Exception as e:
            logger.warning(f"XTTS v2 initialization failed: {e}")
            return False
    
    def _init_edge_tts_enhanced(self) -> bool:
        """Initialize enhanced Edge TTS as fallback"""
        try:
            import edge_tts
            
            # Enhanced voice selection with most natural options
            self.edge_voices_enhanced = {
                "aria_natural": "en-US-AriaNeural",
                "jenny_conversational": "en-US-JennyNeural",
                "michelle_calm": "en-US-MichelleNeural",
                "ana_warm": "en-US-AnaNeural",
                "guy_natural": "en-US-GuyNeural",
                "brian_friendly": "en-US-BrianNeural",
                "christopher_deep": "en-US-ChristopherNeural",
                "eric_confident": "en-US-EricNeural"
            }
            
            self.available_engines["edge_tts_enhanced"] = {
                "name": "Enhanced Edge TTS",
                "quality": "natural",
                "description": "Microsoft's best neural voices with enhanced settings"
            }
            
            logger.info("✓ Enhanced Edge TTS initialized successfully")
            return True
            
        except ImportError:
            logger.info("Edge TTS not available")
            return False
        except Exception as e:
            logger.warning(f"Enhanced Edge TTS initialization failed: {e}")
            return False
    
    async def speak_ultra_natural(self, text: str, emotion: str = "conversational", voice_id: str = None) -> bool:
        """Speak with ultra-natural voice"""
        try:
            if not self.current_engine:
                logger.error("No ultra-natural TTS engine available")
                return False
            
            # Prepare text for ultra-natural speech
            enhanced_text = self._enhance_text_for_natural_speech(text, emotion)
            
            # Get emotional voice parameters
            voice_params = self._get_ultra_natural_voice_params(emotion)
            
            # Use the current engine
            if self.current_engine == "elevenlabs":
                return await self._speak_elevenlabs(enhanced_text, voice_params, voice_id)
            elif self.current_engine == "openai_tts":
                return await self._speak_openai(enhanced_text, voice_params, voice_id)
            elif self.current_engine == "edge_tts_enhanced":
                return await self._speak_edge_enhanced(enhanced_text, voice_params, voice_id)
            
            return False
            
        except Exception as e:
            logger.error(f"Ultra-natural TTS error: {e}")
            return False
    
    def _enhance_text_for_natural_speech(self, text: str, emotion: str) -> str:
        """Enhance text for ultra-natural speech synthesis"""
        import re
        
        # Remove markdown and code
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
        text = re.sub(r'`.*?`', '', text)
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
        text = re.sub(r'\*([^*]+)\*', r'\1', text)
        
        # Add natural speech patterns based on emotion
        if emotion == "happy":
            text = text.replace("!", "! ")
            text = re.sub(r'\.', '. ', text)
        elif emotion == "excited":
            text = text.replace("!", "!! ")
            text = re.sub(r'([.!?])', r'\1 ', text)
        elif emotion == "thoughtful":
            text = re.sub(r'\.', '... ', text)
            text = "Hmm, " + text if not text.startswith(("Hmm", "Well", "You know")) else text
        elif emotion == "calm":
            text = re.sub(r'\.', '. ', text)
            text = re.sub(r'!', '. ', text)
        elif emotion == "friendly":
            if not any(greeting in text.lower() for greeting in ["hi", "hello", "hey"]):
                text = "Hey! " + text
        
        # Add natural pauses and breathing
        text = re.sub(r',', ', ', text)
        text = re.sub(r';', '; ', text)
        text = re.sub(r':', ': ', text)
        
        # Clean up extra spaces
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _get_ultra_natural_voice_params(self, emotion: str) -> Dict[str, Any]:
        """Get ultra-natural voice parameters for emotions"""
        emotional_params = {
            "happy": {
                "stability": 0.8, "similarity_boost": 0.9, "style": 0.8,
                "rate": "medium", "pitch": "+5%", "volume": "loud"
            },
            "excited": {
                "stability": 0.7, "similarity_boost": 0.85, "style": 0.9,
                "rate": "fast", "pitch": "+10%", "volume": "loud"
            },
            "calm": {
                "stability": 0.9, "similarity_boost": 0.8, "style": 0.4,
                "rate": "slow", "pitch": "-2%", "volume": "medium"
            },
            "thoughtful": {
                "stability": 0.85, "similarity_boost": 0.75, "style": 0.3,
                "rate": "slow", "pitch": "-5%", "volume": "medium"
            },
            "friendly": {
                "stability": 0.8, "similarity_boost": 0.85, "style": 0.7,
                "rate": "medium", "pitch": "+3%", "volume": "medium"
            },
            "conversational": {
                "stability": 0.75, "similarity_boost": 0.85, "style": 0.65,
                "rate": "medium", "pitch": "0%", "volume": "medium"
            }
        }
        
        return emotional_params.get(emotion, emotional_params["conversational"])
    
    async def _speak_elevenlabs(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using ElevenLabs ultra-natural voice"""
        try:
            from elevenlabs import generate, play
            
            # Select voice
            voice = voice_id or self.elevenlabs_voices.get(self.voice_settings["voice_id"], "EXAVITQu4vr4xnSDxMaL")
            
            # Generate ultra-natural audio
            audio = generate(
                text=text,
                voice=voice,
                model="eleven_multilingual_v2",
                stability=voice_params.get("stability", 0.75),
                similarity_boost=voice_params.get("similarity_boost", 0.85),
                style=voice_params.get("style", 0.65),
                use_speaker_boost=True
            )
            
            # Play audio
            play(audio)
            
            logger.info(f"🗣️ Josie (ElevenLabs Ultra): {text}")
            return True
            
        except Exception as e:
            logger.error(f"ElevenLabs error: {e}")
            return False
    
    async def _speak_openai(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using OpenAI TTS"""
        try:
            # Select voice
            voice = voice_id or self.openai_voices.get(self.voice_settings["voice_id"], "nova")
            
            # Generate speech
            response = self.openai_client.audio.speech.create(
                model="tts-1-hd",  # High quality model
                voice=voice,
                input=text,
                speed=1.0
            )
            
            # Save and play
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                tmp_file.write(response.content)
                tmp_file_path = tmp_file.name
            
            await self._play_audio_file(tmp_file_path)
            
            # Cleanup
            os.unlink(tmp_file_path)
            
            logger.info(f"🗣️ Josie (OpenAI Ultra): {text}")
            return True
            
        except Exception as e:
            logger.error(f"OpenAI TTS error: {e}")
            return False
    
    async def _speak_edge_enhanced(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using enhanced Edge TTS"""
        try:
            import edge_tts
            
            # Select enhanced voice
            voice = voice_id or self.edge_voices_enhanced.get(self.voice_settings["voice_id"], "en-US-AriaNeural")
            
            # Add SSML for enhanced naturalness
            ssml_text = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
                <voice name="{voice}">
                    <prosody rate="{voice_params.get('rate', 'medium')}" 
                             pitch="{voice_params.get('pitch', '0%')}" 
                             volume="{voice_params.get('volume', 'medium')}">
                        {text}
                    </prosody>
                </voice>
            </speak>
            """
            
            # Generate speech
            communicate = edge_tts.Communicate(ssml_text, voice)
            
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                tmp_file_path = tmp_file.name
            
            await communicate.save(tmp_file_path)
            await self._play_audio_file(tmp_file_path)
            
            # Cleanup
            os.unlink(tmp_file_path)
            
            logger.info(f"🗣️ Josie (Enhanced Edge): {text}")
            return True
            
        except Exception as e:
            logger.error(f"Enhanced Edge TTS error: {e}")
            return False
    
    async def _play_audio_file(self, file_path: str):
        """Play audio file with enhanced quality"""
        try:
            import pygame
            pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=512)
            pygame.mixer.init()
            
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.05)
            
            pygame.mixer.quit()
            
        except Exception as e:
            logger.error(f"Audio playback error: {e}")
    
    def get_voice_info(self) -> Dict[str, Any]:
        """Get ultra-natural voice system information"""
        return {
            "current_engine": self.current_engine,
            "available_engines": self.available_engines,
            "voice_settings": self.voice_settings,
            "naturalness_level": "ultra_natural" if self.current_engine in ["elevenlabs", "openai_tts"] else "natural"
        }


# Convenience function
async def create_ultra_natural_tts() -> UltraNaturalTTS:
    """Create and initialize ultra-natural TTS engine"""
    engine = UltraNaturalTTS()
    return engine

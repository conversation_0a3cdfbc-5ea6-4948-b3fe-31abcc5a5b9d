"""
Environment setup script for Local AI Voice Agent
Checks dependencies, models, and system requirements
"""

import asyncio
import logging
import os
import platform
import subprocess
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnvironmentSetup:
    """Environment setup and validation"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        self.issues = []
        self.warnings = []
    
    def check_python_version(self):
        """Check Python version compatibility"""
        logger.info("Checking Python version...")
        
        if self.python_version < (3, 8):
            self.issues.append(f"Python 3.8+ required, found {sys.version}")
            return False
        elif self.python_version >= (3, 12):
            self.warnings.append(f"Python {sys.version} may have compatibility issues")
        
        logger.info(f"✓ Python {sys.version} is compatible")
        return True
    
    def check_system_dependencies(self):
        """Check system-level dependencies"""
        logger.info("Checking system dependencies...")
        
        dependencies = []
        
        if self.system == "windows":
            # Windows-specific checks
            try:
                import win32com.client
                logger.info("✓ Windows SAPI available")
            except ImportError:
                self.warnings.append("Windows SAPI not available - install pywin32")
                dependencies.append("pywin32")
        
        elif self.system == "linux":
            # Linux-specific checks
            try:
                result = subprocess.run(["espeak", "--version"], 
                                      capture_output=True, check=True)
                logger.info("✓ eSpeak available")
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.warnings.append("eSpeak not found - install espeak package")
                dependencies.append("espeak")
        
        # Check for common audio libraries
        try:
            import pyaudio
            logger.info("✓ PyAudio available")
        except ImportError:
            self.warnings.append("PyAudio not available - may need system audio libraries")
            dependencies.append("pyaudio")
        
        return dependencies
    
    def check_python_packages(self):
        """Check Python package dependencies"""
        logger.info("Checking Python packages...")
        
        required_packages = [
            "livekit-agents",
            "torch",
            "torchvision", 
            "whisper",
            "opencv-python",
            "pillow",
            "numpy",
            "scipy",
            "httpx",
            "pyyaml",
        ]
        
        optional_packages = [
            "pyttsx3",
            "pywin32",  # Windows only
            "pytest",
        ]
        
        missing_required = []
        missing_optional = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                logger.info(f"✓ {package}")
            except ImportError:
                logger.warning(f"✗ {package} (required)")
                missing_required.append(package)
        
        for package in optional_packages:
            if package == "pywin32" and self.system != "windows":
                continue
                
            try:
                __import__(package.replace("-", "_"))
                logger.info(f"✓ {package}")
            except ImportError:
                logger.info(f"- {package} (optional)")
                missing_optional.append(package)
        
        if missing_required:
            self.issues.extend([f"Missing required package: {pkg}" for pkg in missing_required])
        
        if missing_optional:
            self.warnings.extend([f"Missing optional package: {pkg}" for pkg in missing_optional])
        
        return missing_required, missing_optional
    
    async def check_ollama_connection(self):
        """Check Ollama server connection"""
        logger.info("Checking Ollama connection...")
        
        try:
            import httpx
            
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    logger.info(f"✓ Ollama connected - {len(models)} models available")
                    
                    # Check for required models
                    model_names = [model.get("name", "") for model in models]
                    
                    required_models = [
                        "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                        "qwen2.5vl:32b"
                    ]
                    
                    for model in required_models:
                        if any(model in name for name in model_names):
                            logger.info(f"✓ Model available: {model}")
                        else:
                            self.warnings.append(f"Model not found: {model}")
                    
                    return True
                else:
                    self.issues.append(f"Ollama server error: {response.status_code}")
                    return False
                    
        except Exception as e:
            self.issues.append(f"Cannot connect to Ollama: {e}")
            return False
    
    def check_gpu_availability(self):
        """Check GPU availability for acceleration"""
        logger.info("Checking GPU availability...")
        
        try:
            import torch
            
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"✓ CUDA GPU available: {gpu_name} ({gpu_count} devices)")
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                logger.info("✓ Apple Metal GPU available")
                return "mps"
            else:
                logger.info("- No GPU acceleration available (CPU only)")
                self.warnings.append("No GPU acceleration - performance may be limited")
                return "cpu"
                
        except ImportError:
            self.warnings.append("PyTorch not available - cannot check GPU")
            return "unknown"
    
    def check_disk_space(self):
        """Check available disk space"""
        logger.info("Checking disk space...")
        
        try:
            import shutil
            
            total, used, free = shutil.disk_usage(".")
            free_gb = free // (1024**3)
            
            if free_gb < 5:
                self.issues.append(f"Low disk space: {free_gb}GB free (5GB+ recommended)")
                return False
            elif free_gb < 20:
                self.warnings.append(f"Limited disk space: {free_gb}GB free (20GB+ recommended)")
            else:
                logger.info(f"✓ Sufficient disk space: {free_gb}GB free")
            
            return True
            
        except Exception as e:
            self.warnings.append(f"Could not check disk space: {e}")
            return True
    
    def check_memory(self):
        """Check available memory"""
        logger.info("Checking system memory...")
        
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            total_gb = memory.total // (1024**3)
            available_gb = memory.available // (1024**3)
            
            if total_gb < 8:
                self.warnings.append(f"Limited RAM: {total_gb}GB (16GB+ recommended)")
            elif total_gb < 16:
                self.warnings.append(f"Moderate RAM: {total_gb}GB (16GB+ recommended for best performance)")
            else:
                logger.info(f"✓ Sufficient RAM: {total_gb}GB total")
            
            if available_gb < 4:
                self.warnings.append(f"Low available RAM: {available_gb}GB")
            
            return True
            
        except ImportError:
            self.warnings.append("psutil not available - cannot check memory")
            return True
        except Exception as e:
            self.warnings.append(f"Could not check memory: {e}")
            return True
    
    def create_config_files(self):
        """Create default configuration files if they don't exist"""
        logger.info("Checking configuration files...")
        
        config_dir = Path("local_ai_voice_agent/config")
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # Check if models.yaml exists
        models_config = config_dir / "models.yaml"
        if not models_config.exists():
            logger.info("Creating default models.yaml...")
            
            from local_ai_voice_agent.utils.model_utils import ModelManager
            manager = ModelManager()  # This will create the default config
            logger.info("✓ Created default models.yaml")
        else:
            logger.info("✓ models.yaml exists")
        
        # Check if environment.env exists
        env_config = config_dir / "environment.env"
        if not env_config.exists():
            logger.info("Creating default environment.env...")
            
            env_content = """# Local AI Voice Agent Environment Configuration

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_TIMEOUT=60

# Audio Configuration
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_CHUNK_SIZE=1024

# Vision Configuration
VISION_MAX_IMAGE_SIZE=1024
VISION_AUTO_ENHANCE=true

# Performance Configuration
MAX_CONCURRENT_REQUESTS=3
MEMORY_LIMIT_GB=16
REQUEST_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=local_voice_agent.log
"""
            
            with open(env_config, 'w') as f:
                f.write(env_content)
            
            logger.info("✓ Created default environment.env")
        else:
            logger.info("✓ environment.env exists")
    
    async def run_full_check(self):
        """Run complete environment check"""
        logger.info("=" * 50)
        logger.info("Local AI Voice Agent - Environment Check")
        logger.info("=" * 50)
        
        # Basic checks
        self.check_python_version()
        self.check_system_dependencies()
        missing_req, missing_opt = self.check_python_packages()
        
        # System resources
        self.check_gpu_availability()
        self.check_disk_space()
        self.check_memory()
        
        # External services
        await self.check_ollama_connection()
        
        # Configuration
        self.create_config_files()
        
        # Summary
        logger.info("\n" + "=" * 50)
        logger.info("ENVIRONMENT CHECK SUMMARY")
        logger.info("=" * 50)
        
        if self.issues:
            logger.error("CRITICAL ISSUES:")
            for issue in self.issues:
                logger.error(f"  ✗ {issue}")
        
        if self.warnings:
            logger.warning("WARNINGS:")
            for warning in self.warnings:
                logger.warning(f"  ⚠ {warning}")
        
        if not self.issues and not self.warnings:
            logger.info("✓ All checks passed! Environment is ready.")
        elif not self.issues:
            logger.info("✓ Environment is functional with minor warnings.")
        else:
            logger.error("✗ Environment has critical issues that need to be resolved.")
        
        # Installation suggestions
        if missing_req:
            logger.info("\nTo install missing required packages:")
            logger.info(f"  pip install {' '.join(missing_req)}")
        
        if missing_opt:
            logger.info("\nTo install missing optional packages:")
            logger.info(f"  pip install {' '.join(missing_opt)}")
        
        return len(self.issues) == 0


async def main():
    """Main setup function"""
    setup = EnvironmentSetup()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--fix":
        logger.info("Running setup with auto-fix enabled...")
        # Could add auto-installation logic here
    
    success = await setup.run_full_check()
    
    if success:
        logger.info("\n🎉 Environment setup complete! You can now run the voice agent.")
        logger.info("\nNext steps:")
        logger.info("  1. Start Ollama: ollama serve")
        logger.info("  2. Pull required models: ollama pull goekdenizguelmez/JOSIEFIED-Qwen3:14b")
        logger.info("  3. Run demo: python -m local_ai_voice_agent.examples.simple_demo")
    else:
        logger.error("\n❌ Environment setup incomplete. Please resolve the issues above.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

"""
Performance Optimization System
Adaptive performance tuning for ultra-low latency voice processing
"""

import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import threading
import statistics

logger = logging.getLogger(__name__)


class OptimizationTarget(Enum):
    """Performance optimization targets"""
    LATENCY = "latency"
    QUALITY = "quality"
    BALANCED = "balanced"
    THROUGHPUT = "throughput"


@dataclass
class PerformanceMetrics:
    """Performance metrics for optimization"""
    latency_ms: float = 0.0
    throughput_fps: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    error_rate: float = 0.0
    queue_utilization: float = 0.0
    
    def __post_init__(self):
        # Ensure all metrics are valid
        self.latency_ms = max(0.0, self.latency_ms)
        self.throughput_fps = max(0.0, self.throughput_fps)
        self.cpu_usage = max(0.0, min(100.0, self.cpu_usage))
        self.memory_usage = max(0.0, min(100.0, self.memory_usage))
        self.error_rate = max(0.0, min(1.0, self.error_rate))
        self.queue_utilization = max(0.0, min(1.0, self.queue_utilization))


class AdaptivePerformanceOptimizer:
    """Adaptive performance optimizer for voice processing pipeline"""
    
    def __init__(self, target: OptimizationTarget = OptimizationTarget.LATENCY):
        self.target = target
        self.metrics_history: List[PerformanceMetrics] = []
        self.optimization_history: List[Dict[str, Any]] = []
        
        # Optimization parameters
        self.learning_rate = 0.1
        self.stability_threshold = 0.05  # 5% change threshold
        self.min_samples = 10
        self.max_history = 100
        
        # Current optimization state
        self.current_params = self._get_default_params()
        self.last_optimization_time = 0
        self.optimization_interval = 5.0  # seconds
        
        # Thread safety
        self._lock = threading.RLock()
        
        logger.info(f"OPTIMIZER: Initialized for {target.value} optimization")
    
    def _get_default_params(self) -> Dict[str, Any]:
        """Get default optimization parameters"""
        return {
            'chunk_size': 1024,
            'buffer_size': 0.5,
            'processing_threads': 6,
            'queue_sizes': {
                'audio_to_vad': 1000,
                'vad_to_stt': 100,
                'stt_to_ai': 50,
                'ai_to_tts': 50,
                'tts_to_output': 200
            },
            'ai_params': {
                'max_tokens': 150,
                'temperature': 0.9
            },
            'audio_params': {
                'sample_rate': 48000,
                'channels': 1
            }
        }
    
    def add_metrics(self, metrics: PerformanceMetrics):
        """Add performance metrics for optimization"""
        with self._lock:
            self.metrics_history.append(metrics)
            
            # Keep only recent history
            if len(self.metrics_history) > self.max_history:
                self.metrics_history = self.metrics_history[-self.max_history:]
            
            # Check if optimization is needed
            current_time = time.time()
            if (current_time - self.last_optimization_time >= self.optimization_interval and
                len(self.metrics_history) >= self.min_samples):
                self._optimize_parameters()
                self.last_optimization_time = current_time
    
    def _optimize_parameters(self):
        """Optimize parameters based on current metrics"""
        try:
            # Calculate recent performance trends
            recent_metrics = self.metrics_history[-self.min_samples:]
            avg_metrics = self._calculate_average_metrics(recent_metrics)
            
            # Determine optimization strategy based on target
            if self.target == OptimizationTarget.LATENCY:
                self._optimize_for_latency(avg_metrics)
            elif self.target == OptimizationTarget.QUALITY:
                self._optimize_for_quality(avg_metrics)
            elif self.target == OptimizationTarget.THROUGHPUT:
                self._optimize_for_throughput(avg_metrics)
            else:  # BALANCED
                self._optimize_balanced(avg_metrics)
            
            # Record optimization
            self.optimization_history.append({
                'timestamp': time.time(),
                'target': self.target.value,
                'metrics': avg_metrics,
                'params': self.current_params.copy()
            })
            
            logger.debug(f"OPTIMIZE: Parameters optimized for {self.target.value}")
            
        except Exception as e:
            logger.error(f"ERROR: Optimization failed: {e}")
    
    def _calculate_average_metrics(self, metrics_list: List[PerformanceMetrics]) -> PerformanceMetrics:
        """Calculate average metrics from list"""
        if not metrics_list:
            return PerformanceMetrics()
        
        return PerformanceMetrics(
            latency_ms=statistics.mean(m.latency_ms for m in metrics_list),
            throughput_fps=statistics.mean(m.throughput_fps for m in metrics_list),
            cpu_usage=statistics.mean(m.cpu_usage for m in metrics_list),
            memory_usage=statistics.mean(m.memory_usage for m in metrics_list),
            error_rate=statistics.mean(m.error_rate for m in metrics_list),
            queue_utilization=statistics.mean(m.queue_utilization for m in metrics_list)
        )
    
    def _optimize_for_latency(self, metrics: PerformanceMetrics):
        """Optimize parameters for minimum latency"""
        # Reduce chunk sizes if latency is high
        if metrics.latency_ms > 800:  # Target latency
            self.current_params['chunk_size'] = max(512, self.current_params['chunk_size'] - 128)
            self.current_params['buffer_size'] = max(0.2, self.current_params['buffer_size'] - 0.1)
            
            # Reduce AI response length
            self.current_params['ai_params']['max_tokens'] = max(50, 
                self.current_params['ai_params']['max_tokens'] - 20)
        
        # Increase queue sizes if utilization is high
        if metrics.queue_utilization > 0.8:
            for queue_name in self.current_params['queue_sizes']:
                self.current_params['queue_sizes'][queue_name] = int(
                    self.current_params['queue_sizes'][queue_name] * 1.2)
    
    def _optimize_for_quality(self, metrics: PerformanceMetrics):
        """Optimize parameters for maximum quality"""
        # Increase chunk sizes for better quality (if CPU allows)
        if metrics.cpu_usage < 70:
            self.current_params['chunk_size'] = min(2048, self.current_params['chunk_size'] + 128)
            self.current_params['buffer_size'] = min(1.0, self.current_params['buffer_size'] + 0.1)
            
            # Allow longer AI responses
            self.current_params['ai_params']['max_tokens'] = min(300,
                self.current_params['ai_params']['max_tokens'] + 20)
    
    def _optimize_for_throughput(self, metrics: PerformanceMetrics):
        """Optimize parameters for maximum throughput"""
        # Balance chunk sizes for optimal throughput
        if metrics.throughput_fps < 30:  # Target FPS
            # Adjust based on bottleneck
            if metrics.cpu_usage > 80:
                # CPU bound - reduce processing load
                self.current_params['chunk_size'] = max(512, self.current_params['chunk_size'] - 64)
            else:
                # Not CPU bound - increase batch sizes
                self.current_params['chunk_size'] = min(1536, self.current_params['chunk_size'] + 64)
    
    def _optimize_balanced(self, metrics: PerformanceMetrics):
        """Optimize for balanced performance"""
        # Balance between latency and quality
        target_latency = 800  # ms
        target_cpu = 60      # %
        
        if metrics.latency_ms > target_latency * 1.2:
            # Prioritize latency
            self._optimize_for_latency(metrics)
        elif metrics.cpu_usage < target_cpu * 0.8:
            # CPU headroom available - improve quality
            self._optimize_for_quality(metrics)
        else:
            # Maintain current balance
            pass
    
    def get_current_params(self) -> Dict[str, Any]:
        """Get current optimization parameters"""
        with self._lock:
            return self.current_params.copy()
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization statistics"""
        with self._lock:
            if not self.metrics_history:
                return {'status': 'no_data'}
            
            recent_metrics = self.metrics_history[-10:] if len(self.metrics_history) >= 10 else self.metrics_history
            avg_metrics = self._calculate_average_metrics(recent_metrics)
            
            return {
                'target': self.target.value,
                'samples_collected': len(self.metrics_history),
                'optimizations_performed': len(self.optimization_history),
                'current_metrics': {
                    'latency_ms': avg_metrics.latency_ms,
                    'throughput_fps': avg_metrics.throughput_fps,
                    'cpu_usage': avg_metrics.cpu_usage,
                    'error_rate': avg_metrics.error_rate
                },
                'current_params': self.current_params
            }


class NLPOptimizer:
    """NLP-specific performance optimizer"""
    
    def __init__(self):
        self.response_times = []
        self.token_counts = []
        self.quality_scores = []
        
        # Optimization parameters
        self.target_response_time = 2.0  # seconds
        self.max_tokens = 150
        self.temperature = 0.9
        
        logger.info("NLP: NLP optimizer initialized")
    
    def add_inference_result(self, response_time: float, token_count: int, quality_score: float = 1.0):
        """Add NLP inference result for optimization"""
        self.response_times.append(response_time)
        self.token_counts.append(token_count)
        self.quality_scores.append(quality_score)
        
        # Keep only recent history
        max_history = 50
        if len(self.response_times) > max_history:
            self.response_times = self.response_times[-max_history:]
            self.token_counts = self.token_counts[-max_history:]
            self.quality_scores = self.quality_scores[-max_history:]
        
        # Optimize if we have enough data
        if len(self.response_times) >= 10:
            self._optimize_nlp_params()
    
    def _optimize_nlp_params(self):
        """Optimize NLP parameters"""
        try:
            avg_response_time = statistics.mean(self.response_times[-10:])
            avg_quality = statistics.mean(self.quality_scores[-10:])
            
            # Adjust max tokens based on response time
            if avg_response_time > self.target_response_time * 1.2:
                # Too slow - reduce tokens
                self.max_tokens = max(50, self.max_tokens - 10)
                logger.debug(f"NLP: Reduced max_tokens to {self.max_tokens}")
            elif avg_response_time < self.target_response_time * 0.8 and avg_quality > 0.8:
                # Fast enough and good quality - can increase tokens
                self.max_tokens = min(200, self.max_tokens + 10)
                logger.debug(f"NLP: Increased max_tokens to {self.max_tokens}")
            
            # Adjust temperature based on quality
            if avg_quality < 0.7:
                # Low quality - reduce temperature for more consistency
                self.temperature = max(0.5, self.temperature - 0.1)
                logger.debug(f"NLP: Reduced temperature to {self.temperature}")
            elif avg_quality > 0.9:
                # High quality - can increase creativity
                self.temperature = min(1.2, self.temperature + 0.05)
                logger.debug(f"NLP: Increased temperature to {self.temperature}")
                
        except Exception as e:
            logger.error(f"ERROR: NLP optimization failed: {e}")
    
    def get_optimal_params(self) -> Dict[str, Any]:
        """Get current optimal NLP parameters"""
        return {
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'target_response_time': self.target_response_time
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get NLP optimization statistics"""
        if not self.response_times:
            return {'status': 'no_data'}
        
        return {
            'avg_response_time': statistics.mean(self.response_times[-10:]) if len(self.response_times) >= 10 else 0,
            'avg_quality': statistics.mean(self.quality_scores[-10:]) if len(self.quality_scores) >= 10 else 0,
            'current_max_tokens': self.max_tokens,
            'current_temperature': self.temperature,
            'samples': len(self.response_times)
        }

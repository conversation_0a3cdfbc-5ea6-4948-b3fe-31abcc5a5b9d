"""
Performance Optimization Module for Ultra Voice Agent
Advanced optimizations for NLP processing, AI response accuracy, and voice latency
"""

import time
import threading
import psutil
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import deque
import logging

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    latency_ms: float
    throughput_ops_per_sec: float
    cpu_usage_percent: float
    memory_usage_mb: float
    gpu_usage_percent: float = 0.0
    accuracy_score: float = 1.0
    timestamp: float = 0.0

@dataclass
class OptimizationTarget:
    """Performance optimization targets"""
    max_latency_ms: float = 800.0
    min_throughput_ops: float = 10.0
    max_cpu_usage: float = 80.0
    max_memory_mb: float = 2048.0
    min_accuracy: float = 0.85

class AdaptivePerformanceOptimizer:
    """
    Adaptive performance optimizer that dynamically adjusts system parameters
    based on real-time performance metrics and user requirements
    """
    
    def __init__(self, targets: Optional[OptimizationTarget] = None):
        self.targets = targets or OptimizationTarget()
        
        # Performance history
        self.metrics_history = deque(maxlen=100)
        self.optimization_history = deque(maxlen=50)
        
        # Current optimization state
        self.current_optimizations = {}
        self.optimization_lock = threading.Lock()
        
        # Adaptive parameters
        self.adaptive_params = {
            'stt_batch_size': 1,
            'ai_temperature': 0.7,
            'ai_max_tokens': 2048,
            'vad_sensitivity': 0.5,
            'audio_chunk_size': 1024,
            'processing_threads': 6,
            'model_precision': 'float16'  # float32, float16, int8
        }
        
        # Performance monitoring
        self.monitoring_active = False
        self.monitor_thread = None
        self.monitor_interval = 1.0  # seconds
        
        logger.info("Adaptive Performance Optimizer initialized")
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="PerformanceMonitor"
        )
        self.monitor_thread.start()
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect current metrics
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Analyze performance and optimize if needed
                if len(self.metrics_history) >= 5:  # Need some history
                    self._analyze_and_optimize()
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                time.sleep(self.monitor_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current system performance metrics"""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory_info = psutil.virtual_memory()
            memory_mb = memory_info.used / (1024 * 1024)
            
            # GPU metrics (if available)
            gpu_usage = 0.0
            try:
                import pynvml
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                gpu_info = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_usage = gpu_info.gpu
            except:
                pass  # GPU monitoring not available
            
            # Calculate average latency from recent history
            avg_latency = 0.0
            if len(self.metrics_history) > 0:
                recent_latencies = [m.latency_ms for m in list(self.metrics_history)[-10:]]
                avg_latency = sum(recent_latencies) / len(recent_latencies)
            
            return PerformanceMetrics(
                latency_ms=avg_latency,
                throughput_ops_per_sec=1000.0 / max(avg_latency, 1.0),
                cpu_usage_percent=cpu_percent,
                memory_usage_mb=memory_mb,
                gpu_usage_percent=gpu_usage,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Metrics collection error: {e}")
            return PerformanceMetrics(0, 0, 0, 0, 0, 1.0, time.time())
    
    def _analyze_and_optimize(self):
        """Analyze performance and apply optimizations"""
        if len(self.metrics_history) < 5:
            return
        
        recent_metrics = list(self.metrics_history)[-5:]
        avg_metrics = self._calculate_average_metrics(recent_metrics)
        
        optimizations_applied = []
        
        with self.optimization_lock:
            # Latency optimization
            if avg_metrics.latency_ms > self.targets.max_latency_ms:
                optimizations_applied.extend(self._optimize_latency(avg_metrics))
            
            # CPU optimization
            if avg_metrics.cpu_usage_percent > self.targets.max_cpu_usage:
                optimizations_applied.extend(self._optimize_cpu_usage(avg_metrics))
            
            # Memory optimization
            if avg_metrics.memory_usage_mb > self.targets.max_memory_mb:
                optimizations_applied.extend(self._optimize_memory_usage(avg_metrics))
            
            # Throughput optimization
            if avg_metrics.throughput_ops_per_sec < self.targets.min_throughput_ops:
                optimizations_applied.extend(self._optimize_throughput(avg_metrics))
        
        if optimizations_applied:
            logger.info(f"Applied optimizations: {optimizations_applied}")
            self.optimization_history.append({
                'timestamp': time.time(),
                'metrics': avg_metrics,
                'optimizations': optimizations_applied
            })
    
    def _calculate_average_metrics(self, metrics_list: List[PerformanceMetrics]) -> PerformanceMetrics:
        """Calculate average metrics from a list"""
        if not metrics_list:
            return PerformanceMetrics(0, 0, 0, 0, 0, 1.0, time.time())
        
        return PerformanceMetrics(
            latency_ms=sum(m.latency_ms for m in metrics_list) / len(metrics_list),
            throughput_ops_per_sec=sum(m.throughput_ops_per_sec for m in metrics_list) / len(metrics_list),
            cpu_usage_percent=sum(m.cpu_usage_percent for m in metrics_list) / len(metrics_list),
            memory_usage_mb=sum(m.memory_usage_mb for m in metrics_list) / len(metrics_list),
            gpu_usage_percent=sum(m.gpu_usage_percent for m in metrics_list) / len(metrics_list),
            accuracy_score=sum(m.accuracy_score for m in metrics_list) / len(metrics_list),
            timestamp=time.time()
        )
    
    def _optimize_latency(self, metrics: PerformanceMetrics) -> List[str]:
        """Apply latency optimizations"""
        optimizations = []
        
        # Reduce AI response length for faster generation
        if self.adaptive_params['ai_max_tokens'] > 512:
            self.adaptive_params['ai_max_tokens'] = max(512, self.adaptive_params['ai_max_tokens'] - 256)
            optimizations.append(f"Reduced AI max tokens to {self.adaptive_params['ai_max_tokens']}")
        
        # Increase VAD sensitivity to reduce processing time
        if self.adaptive_params['vad_sensitivity'] < 0.8:
            self.adaptive_params['vad_sensitivity'] = min(0.8, self.adaptive_params['vad_sensitivity'] + 0.1)
            optimizations.append(f"Increased VAD sensitivity to {self.adaptive_params['vad_sensitivity']}")
        
        # Reduce audio chunk size for lower latency
        if self.adaptive_params['audio_chunk_size'] > 512:
            self.adaptive_params['audio_chunk_size'] = max(512, self.adaptive_params['audio_chunk_size'] - 256)
            optimizations.append(f"Reduced audio chunk size to {self.adaptive_params['audio_chunk_size']}")
        
        # Switch to lower precision for faster processing
        if self.adaptive_params['model_precision'] == 'float32':
            self.adaptive_params['model_precision'] = 'float16'
            optimizations.append("Switched to float16 precision")
        
        return optimizations
    
    def _optimize_cpu_usage(self, metrics: PerformanceMetrics) -> List[str]:
        """Apply CPU usage optimizations"""
        optimizations = []
        
        # Reduce processing threads if CPU usage is high
        if self.adaptive_params['processing_threads'] > 4:
            self.adaptive_params['processing_threads'] -= 1
            optimizations.append(f"Reduced processing threads to {self.adaptive_params['processing_threads']}")
        
        # Increase audio chunk size to reduce processing frequency
        if self.adaptive_params['audio_chunk_size'] < 2048:
            self.adaptive_params['audio_chunk_size'] = min(2048, self.adaptive_params['audio_chunk_size'] + 256)
            optimizations.append(f"Increased audio chunk size to {self.adaptive_params['audio_chunk_size']}")
        
        return optimizations
    
    def _optimize_memory_usage(self, metrics: PerformanceMetrics) -> List[str]:
        """Apply memory usage optimizations"""
        optimizations = []
        
        # Switch to lower precision to save memory
        if self.adaptive_params['model_precision'] == 'float32':
            self.adaptive_params['model_precision'] = 'float16'
            optimizations.append("Switched to float16 precision for memory savings")
        elif self.adaptive_params['model_precision'] == 'float16':
            self.adaptive_params['model_precision'] = 'int8'
            optimizations.append("Switched to int8 precision for memory savings")
        
        # Reduce batch size
        if self.adaptive_params['stt_batch_size'] > 1:
            self.adaptive_params['stt_batch_size'] = 1
            optimizations.append("Reduced STT batch size to 1")
        
        return optimizations
    
    def _optimize_throughput(self, metrics: PerformanceMetrics) -> List[str]:
        """Apply throughput optimizations"""
        optimizations = []
        
        # Increase processing threads if resources allow
        if (self.adaptive_params['processing_threads'] < 8 and 
            metrics.cpu_usage_percent < 70):
            self.adaptive_params['processing_threads'] += 1
            optimizations.append(f"Increased processing threads to {self.adaptive_params['processing_threads']}")
        
        # Increase batch size for better throughput
        if (self.adaptive_params['stt_batch_size'] < 4 and 
            metrics.memory_usage_mb < self.targets.max_memory_mb * 0.8):
            self.adaptive_params['stt_batch_size'] += 1
            optimizations.append(f"Increased STT batch size to {self.adaptive_params['stt_batch_size']}")
        
        return optimizations
    
    def get_current_parameters(self) -> Dict[str, Any]:
        """Get current adaptive parameters"""
        with self.optimization_lock:
            return self.adaptive_params.copy()
    
    def update_latency_measurement(self, component: str, latency_ms: float):
        """Update latency measurement for a specific component"""
        # This would be called by individual components to report their latency
        pass
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        if not self.metrics_history:
            return {"status": "No metrics available"}
        
        recent_metrics = list(self.metrics_history)[-10:]
        avg_metrics = self._calculate_average_metrics(recent_metrics)
        
        return {
            "current_metrics": {
                "latency_ms": avg_metrics.latency_ms,
                "throughput_ops_per_sec": avg_metrics.throughput_ops_per_sec,
                "cpu_usage_percent": avg_metrics.cpu_usage_percent,
                "memory_usage_mb": avg_metrics.memory_usage_mb,
                "gpu_usage_percent": avg_metrics.gpu_usage_percent
            },
            "targets": {
                "max_latency_ms": self.targets.max_latency_ms,
                "min_throughput_ops": self.targets.min_throughput_ops,
                "max_cpu_usage": self.targets.max_cpu_usage,
                "max_memory_mb": self.targets.max_memory_mb
            },
            "adaptive_parameters": self.get_current_parameters(),
            "optimization_history": list(self.optimization_history)[-5:],
            "performance_status": self._get_performance_status(avg_metrics)
        }
    
    def _get_performance_status(self, metrics: PerformanceMetrics) -> str:
        """Get overall performance status"""
        issues = []
        
        if metrics.latency_ms > self.targets.max_latency_ms:
            issues.append("High latency")
        if metrics.cpu_usage_percent > self.targets.max_cpu_usage:
            issues.append("High CPU usage")
        if metrics.memory_usage_mb > self.targets.max_memory_mb:
            issues.append("High memory usage")
        if metrics.throughput_ops_per_sec < self.targets.min_throughput_ops:
            issues.append("Low throughput")
        
        if not issues:
            return "Optimal"
        elif len(issues) == 1:
            return f"Warning: {issues[0]}"
        else:
            return f"Issues: {', '.join(issues)}"


class NLPOptimizer:
    """
    Specialized optimizer for NLP processing components
    Focuses on speech recognition and AI inference optimization
    """
    
    def __init__(self):
        self.stt_optimizations = {
            'beam_size': 1,
            'best_of': 1,
            'temperature': 0.0,
            'compression_ratio_threshold': 2.4,
            'log_prob_threshold': -1.0,
            'no_speech_threshold': 0.6
        }
        
        self.ai_optimizations = {
            'temperature': 0.7,
            'top_k': 40,
            'top_p': 0.9,
            'repeat_penalty': 1.1,
            'max_tokens': 2048
        }
        
        logger.info("NLP Optimizer initialized")
    
    def optimize_stt_parameters(self, target_latency_ms: float) -> Dict[str, Any]:
        """Optimize STT parameters for target latency"""
        if target_latency_ms < 200:
            # Ultra-fast mode
            return {
                'beam_size': 1,
                'best_of': 1,
                'temperature': 0.0,
                'word_timestamps': False,
                'condition_on_previous_text': False
            }
        elif target_latency_ms < 500:
            # Fast mode
            return {
                'beam_size': 2,
                'best_of': 1,
                'temperature': 0.0,
                'word_timestamps': False,
                'condition_on_previous_text': True
            }
        else:
            # Balanced mode
            return {
                'beam_size': 3,
                'best_of': 2,
                'temperature': 0.2,
                'word_timestamps': True,
                'condition_on_previous_text': True
            }
    
    def optimize_ai_parameters(self, target_quality: str = "balanced") -> Dict[str, Any]:
        """Optimize AI parameters for target quality"""
        if target_quality == "speed":
            return {
                'temperature': 0.3,
                'top_k': 20,
                'top_p': 0.8,
                'max_tokens': 512
            }
        elif target_quality == "quality":
            return {
                'temperature': 0.8,
                'top_k': 50,
                'top_p': 0.95,
                'max_tokens': 4096
            }
        else:  # balanced
            return {
                'temperature': 0.7,
                'top_k': 40,
                'top_p': 0.9,
                'max_tokens': 2048
            }

"""
Local Text-to-Speech implementation for LiveKit Agents
Supports Windows SAPI, pyttsx3, and other local TTS engines
"""

import asyncio
import io
import logging
import platform
import tempfile
import wave
from typing import AsyncIterator, Optional

import numpy as np
from livekit import rtc
from livekit.agents import tts, utils
from livekit.agents.utils import AudioBuffer

# Windows COM initialization
if platform.system() == "Windows":
    import pythoncom

logger = logging.getLogger(__name__)


class LocalTTS(tts.TTS):
    """Local TTS implementation supporting multiple engines"""
    
    def __init__(
        self,
        *,
        engine: str = "auto",
        voice: Optional[str] = None,
        rate: int = 200,
        volume: float = 0.9,
        sample_rate: int = 16000,
        language: str = "en",
    ):
        """
        Initialize Local TTS
        
        Args:
            engine: TTS engine ("sapi", "pyttsx3", "espeak", "auto")
            voice: Voice name (engine-specific)
            rate: Speech rate (words per minute)
            volume: Volume level (0.0 to 1.0)
            sample_rate: Audio sample rate
            language: Language code
        """
        super().__init__(
            capabilities=tts.TTSCapabilities(streaming=False),
            sample_rate=sample_rate,
            num_channels=1,
        )
        
        self._engine_name = engine
        self._voice = voice
        self._rate = rate
        self._volume = volume
        self._language = language
        self._engine = None
        
        # Auto-detect best available engine
        if engine == "auto":
            self._engine_name = self._detect_best_engine()
            
        self._initialize_engine()
    
    def _detect_best_engine(self) -> str:
        """Detect the best available TTS engine for the platform"""
        system = platform.system().lower()
        
        if system == "windows":
            try:
                import win32com.client
                return "sapi"
            except ImportError:
                pass
        
        try:
            import pyttsx3
            return "pyttsx3"
        except ImportError:
            pass
            
        if system in ["linux", "darwin"]:
            try:
                import subprocess
                subprocess.run(["espeak", "--version"], 
                             capture_output=True, check=True)
                return "espeak"
            except (subprocess.CalledProcessError, FileNotFoundError):
                pass
        
        raise RuntimeError("No suitable TTS engine found")
    
    def _initialize_engine(self):
        """Initialize the selected TTS engine"""
        if self._engine_name == "sapi":
            self._engine = SAPIEngine(
                voice=self._voice,
                rate=self._rate,
                volume=self._volume
            )
        elif self._engine_name == "pyttsx3":
            self._engine = Pyttsx3Engine(
                voice=self._voice,
                rate=self._rate,
                volume=self._volume
            )
        elif self._engine_name == "espeak":
            self._engine = EspeakEngine(
                voice=self._voice or self._language,
                rate=self._rate,
                volume=self._volume
            )
        else:
            raise ValueError(f"Unsupported TTS engine: {self._engine_name}")
    
    def synthesize(self, text: str) -> "LocalTTSStream":
        """Synthesize text to speech"""
        return LocalTTSStream(
            tts=self,
            text=text,
            engine=self._engine
        )


class LocalTTSStream:
    """Stream for local TTS synthesis"""
    
    def __init__(self, tts: LocalTTS, text: str, engine):
        self._tts = tts
        self._text = text
        self._engine = engine
    
    async def __aiter__(self) -> AsyncIterator[tts.SynthesizedAudio]:
        """Generate synthesized audio"""
        try:
            # Run synthesis in thread pool to avoid blocking
            audio_data = await asyncio.get_event_loop().run_in_executor(
                None, self._engine.synthesize, self._text
            )
            
            if audio_data:
                # Convert to AudioFrame
                frame = rtc.AudioFrame(
                    data=audio_data,
                    sample_rate=self._tts.sample_rate,
                    num_channels=self._tts.num_channels,
                    samples_per_channel=len(audio_data) // self._tts.num_channels
                )
                
                yield tts.SynthesizedAudio(
                    request_id=utils.shortuuid(),
                    frame=frame
                )
        except Exception as e:
            logger.error(f"TTS synthesis failed: {e}")
            raise


class SAPIEngine:
    """Windows SAPI TTS Engine"""
    
    def __init__(self, voice: Optional[str] = None, rate: int = 200, volume: float = 0.9):
        try:
            import win32com.client
            # Initialize COM
            pythoncom.CoInitialize()
            self._sapi = win32com.client.Dispatch("SAPI.SpVoice")
            
            if voice:
                voices = self._sapi.GetVoices()
                for i in range(voices.Count):
                    if voice.lower() in voices.Item(i).GetDescription().lower():
                        self._sapi.Voice = voices.Item(i)
                        break
            
            self._sapi.Rate = max(-10, min(10, (rate - 200) // 20))
            self._sapi.Volume = int(volume * 100)
            
        except ImportError:
            raise RuntimeError("Windows SAPI not available")
    
    def synthesize(self, text: str) -> bytes:
        """Synthesize text using SAPI"""
        try:
            import win32com.client
            # Ensure COM is initialized for this thread
            pythoncom.CoInitialize()
            
            # Create file stream
            file_stream = win32com.client.Dispatch("SAPI.SpFileStream")
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                file_stream.Open(tmp_file.name, 3)  # SSFMCreateForWrite
                self._sapi.AudioOutputStream = file_stream
                self._sapi.Speak(text)
                file_stream.Close()
                
                # Read the generated audio file
                with open(tmp_file.name, "rb") as f:
                    wav_data = f.read()
                
                # Extract audio data from WAV file
                return self._extract_audio_data(wav_data)
                
        except Exception as e:
            logger.error(f"SAPI synthesis failed: {e}")
            return b""
    
    def _extract_audio_data(self, wav_data: bytes) -> bytes:
        """Extract raw audio data from WAV file"""
        try:
            with wave.open(io.BytesIO(wav_data), 'rb') as wav_file:
                frames = wav_file.readframes(wav_file.getnframes())
                return frames
        except Exception as e:
            logger.error(f"Failed to extract audio data: {e}")
            return b""


class Pyttsx3Engine:
    """pyttsx3 TTS Engine"""
    
    def __init__(self, voice: Optional[str] = None, rate: int = 200, volume: float = 0.9):
        try:
            import pyttsx3
            self._engine = pyttsx3.init()
            
            if voice:
                voices = self._engine.getProperty('voices')
                for v in voices:
                    if voice.lower() in v.name.lower():
                        self._engine.setProperty('voice', v.id)
                        break
            
            self._engine.setProperty('rate', rate)
            self._engine.setProperty('volume', volume)
            
        except ImportError:
            raise RuntimeError("pyttsx3 not available")
    
    def synthesize(self, text: str) -> bytes:
        """Synthesize text using pyttsx3"""
        try:
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                self._engine.save_to_file(text, tmp_file.name)
                self._engine.runAndWait()
                
                # Read the generated audio file
                with open(tmp_file.name, "rb") as f:
                    wav_data = f.read()
                
                return self._extract_audio_data(wav_data)
                
        except Exception as e:
            logger.error(f"pyttsx3 synthesis failed: {e}")
            return b""
    
    def _extract_audio_data(self, wav_data: bytes) -> bytes:
        """Extract raw audio data from WAV file"""
        try:
            with wave.open(io.BytesIO(wav_data), 'rb') as wav_file:
                frames = wav_file.readframes(wav_file.getnframes())
                return frames
        except Exception as e:
            logger.error(f"Failed to extract audio data: {e}")
            return b""


class EspeakEngine:
    """eSpeak TTS Engine (Linux/macOS)"""
    
    def __init__(self, voice: str = "en", rate: int = 200, volume: float = 0.9):
        self._voice = voice
        self._rate = rate
        self._volume = int(volume * 100)
        
        try:
            import subprocess
            subprocess.run(["espeak", "--version"], 
                         capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise RuntimeError("eSpeak not available")
    
    def synthesize(self, text: str) -> bytes:
        """Synthesize text using eSpeak"""
        try:
            import subprocess
            
            cmd = [
                "espeak",
                "-v", self._voice,
                "-s", str(self._rate),
                "-a", str(self._volume),
                "--stdout",
                text
            ]
            
            result = subprocess.run(cmd, capture_output=True, check=True)
            return result.stdout
            
        except Exception as e:
            logger.error(f"eSpeak synthesis failed: {e}")
            return b""

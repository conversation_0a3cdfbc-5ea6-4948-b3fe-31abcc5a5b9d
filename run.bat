@echo off
echo 🚀 Starting AI Voice Agent...
echo ================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

REM Install dependencies if needed
echo 📦 Checking dependencies...
pip install -q pyttsx3 httpx

REM Check if Ollama is running
echo 🔍 Checking Ollama connection...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama not running. Starting Ollama...
    echo Please run: ollama serve
    echo Then run this script again.
    pause
    exit /b 1
)

REM Run the voice system
echo ✅ All checks passed! Starting voice system...
echo.
python SIMPLE_VOICE_SYSTEM.py

pause

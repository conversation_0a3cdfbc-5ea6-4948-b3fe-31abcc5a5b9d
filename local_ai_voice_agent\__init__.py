"""
Local AI Voice Agent
A comprehensive local AI voice agent with vision, speech, and conversation capabilities
"""

__version__ = "0.1.0"
__author__ = "Local AI Voice Agent"

from .agents import LocalVoiceAgent, create_agent_session, prewarm
from .services import (
    LocalTTS,
    LocalWhisperSTT,
    OllamaLLM,
    QwenVisionLLM,
    VisionService,
)
from .utils import ModelManager, AudioProcessor

__all__ = [
    "LocalVoiceAgent",
    "create_agent_session", 
    "prewarm",
    "LocalTTS",
    "LocalWhisperSTT",
    "OllamaLLM",
    "QwenVisionLLM",
    "VisionService",
    "ModelManager",
    "AudioProcessor",
]

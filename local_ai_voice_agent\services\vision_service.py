"""
Vision processing service for multimodal AI interactions
Handles image processing, analysis, and integration with Qwen 2.5-VL
"""

import asyncio
import base64
import io
import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageOps
from livekit.agents import llm

from .ollama_llm import QwenVisionLLM

logger = logging.getLogger(__name__)


class VisionService:
    """Service for processing and analyzing images"""
    
    def __init__(
        self,
        *,
        vision_llm: Optional[QwenVisionLLM] = None,
        max_image_size: Tuple[int, int] = (1024, 1024),
        supported_formats: List[str] = None,
        auto_enhance: bool = True,
    ):
        """
        Initialize Vision Service
        
        Args:
            vision_llm: Qwen Vision LLM instance
            max_image_size: Maximum image dimensions (width, height)
            supported_formats: Supported image formats
            auto_enhance: Whether to auto-enhance images
        """
        self._vision_llm = vision_llm or QwenVisionLLM()
        self._max_image_size = max_image_size
        self._supported_formats = supported_formats or [
            'jpg', 'jpeg', 'png', 'bmp', 'webp', 'tiff'
        ]
        self._auto_enhance = auto_enhance
    
    async def analyze_image(
        self,
        image_data: Union[bytes, str, Image.Image, np.ndarray],
        prompt: str = "Describe what you see in this image in detail.",
        *,
        chat_ctx: Optional[llm.ChatContext] = None,
        preprocess: bool = True,
    ) -> str:
        """
        Analyze an image using the vision model
        
        Args:
            image_data: Image data in various formats
            prompt: Analysis prompt
            chat_ctx: Chat context for conversation
            preprocess: Whether to preprocess the image
            
        Returns:
            Analysis result as text
        """
        try:
            # Convert and preprocess image
            processed_image = await self._preprocess_image(image_data, preprocess)
            
            # Convert to base64 for model input
            image_b64 = self._image_to_base64(processed_image)
            
            # Analyze with vision model
            result = await self._vision_llm.analyze_image(
                image_b64,
                prompt,
                chat_ctx=chat_ctx
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            return "I'm sorry, I couldn't analyze the image due to an error."
    
    async def describe_scene(
        self,
        image_data: Union[bytes, str, Image.Image, np.ndarray],
        *,
        detail_level: str = "medium",
        focus_areas: Optional[List[str]] = None,
    ) -> str:
        """
        Describe a scene in an image
        
        Args:
            image_data: Image data
            detail_level: Level of detail ("brief", "medium", "detailed")
            focus_areas: Specific areas to focus on
            
        Returns:
            Scene description
        """
        prompts = {
            "brief": "Briefly describe what you see in this image.",
            "medium": "Describe the scene in this image, including the main objects, people, and setting.",
            "detailed": "Provide a detailed description of this image, including objects, people, activities, colors, lighting, composition, and any notable details."
        }
        
        prompt = prompts.get(detail_level, prompts["medium"])
        
        if focus_areas:
            focus_text = ", ".join(focus_areas)
            prompt += f" Pay special attention to: {focus_text}."
        
        return await self.analyze_image(image_data, prompt)
    
    async def answer_visual_question(
        self,
        image_data: Union[bytes, str, Image.Image, np.ndarray],
        question: str,
        *,
        chat_ctx: Optional[llm.ChatContext] = None,
    ) -> str:
        """
        Answer a question about an image
        
        Args:
            image_data: Image data
            question: Question about the image
            chat_ctx: Chat context
            
        Returns:
            Answer to the question
        """
        prompt = f"Look at this image and answer the following question: {question}"
        return await self.analyze_image(image_data, prompt, chat_ctx=chat_ctx)
    
    async def detect_objects(
        self,
        image_data: Union[bytes, str, Image.Image, np.ndarray],
        *,
        object_types: Optional[List[str]] = None,
    ) -> str:
        """
        Detect and list objects in an image
        
        Args:
            image_data: Image data
            object_types: Specific object types to look for
            
        Returns:
            List of detected objects
        """
        if object_types:
            object_list = ", ".join(object_types)
            prompt = f"Identify and list any of these objects in the image: {object_list}. Also mention any other notable objects you see."
        else:
            prompt = "Identify and list all the objects you can see in this image. Be specific about what each object is."
        
        return await self.analyze_image(image_data, prompt)
    
    async def read_text(
        self,
        image_data: Union[bytes, str, Image.Image, np.ndarray],
    ) -> str:
        """
        Extract and read text from an image
        
        Args:
            image_data: Image data
            
        Returns:
            Extracted text
        """
        prompt = "Read and transcribe all the text visible in this image. If there's no text, say 'No text found'."
        return await self.analyze_image(image_data, prompt)
    
    async def _preprocess_image(
        self,
        image_data: Union[bytes, str, Image.Image, np.ndarray],
        preprocess: bool = True,
    ) -> Image.Image:
        """
        Preprocess image data into PIL Image
        
        Args:
            image_data: Input image data
            preprocess: Whether to apply preprocessing
            
        Returns:
            Processed PIL Image
        """
        try:
            # Convert to PIL Image
            if isinstance(image_data, bytes):
                image = Image.open(io.BytesIO(image_data))
            elif isinstance(image_data, str):
                # Assume it's a file path or base64
                if image_data.startswith('data:image'):
                    # Base64 data URL
                    header, data = image_data.split(',', 1)
                    image_bytes = base64.b64decode(data)
                    image = Image.open(io.BytesIO(image_bytes))
                else:
                    # File path
                    image = Image.open(image_data)
            elif isinstance(image_data, np.ndarray):
                # Convert numpy array to PIL
                if image_data.dtype != np.uint8:
                    image_data = (image_data * 255).astype(np.uint8)
                image = Image.fromarray(image_data)
            elif isinstance(image_data, Image.Image):
                image = image_data.copy()
            else:
                raise ValueError(f"Unsupported image data type: {type(image_data)}")
            
            if not preprocess:
                return image
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize if too large
            if image.size[0] > self._max_image_size[0] or image.size[1] > self._max_image_size[1]:
                image.thumbnail(self._max_image_size, Image.Resampling.LANCZOS)
            
            # Auto-enhance if enabled
            if self._auto_enhance:
                image = self._enhance_image(image)
            
            return image
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            raise
    
    def _enhance_image(self, image: Image.Image) -> Image.Image:
        """
        Apply automatic image enhancement
        
        Args:
            image: Input PIL Image
            
        Returns:
            Enhanced PIL Image
        """
        try:
            # Auto-contrast
            image = ImageOps.autocontrast(image, cutoff=1)
            
            # Slight sharpening
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # Slight color enhancement
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.05)
            
            return image
            
        except Exception as e:
            logger.warning(f"Image enhancement failed: {e}")
            return image
    
    def _image_to_base64(self, image: Image.Image, format: str = "JPEG") -> bytes:
        """
        Convert PIL Image to base64 bytes
        
        Args:
            image: PIL Image
            format: Output format
            
        Returns:
            Base64 encoded image bytes
        """
        try:
            buffer = io.BytesIO()
            image.save(buffer, format=format, quality=85, optimize=True)
            image_bytes = buffer.getvalue()
            return image_bytes
            
        except Exception as e:
            logger.error(f"Image to base64 conversion failed: {e}")
            raise
    
    async def process_video_frame(
        self,
        frame: np.ndarray,
        prompt: str = "Describe what's happening in this video frame.",
    ) -> str:
        """
        Process a single video frame
        
        Args:
            frame: Video frame as numpy array
            prompt: Analysis prompt
            
        Returns:
            Frame analysis result
        """
        return await self.analyze_image(frame, prompt)
    
    async def compare_images(
        self,
        image1: Union[bytes, str, Image.Image, np.ndarray],
        image2: Union[bytes, str, Image.Image, np.ndarray],
        *,
        comparison_type: str = "differences",
    ) -> str:
        """
        Compare two images
        
        Args:
            image1: First image
            image2: Second image
            comparison_type: Type of comparison ("differences", "similarities", "changes")
            
        Returns:
            Comparison result
        """
        # For now, analyze each image separately and compare
        # In a more advanced implementation, you could create a side-by-side image
        
        analysis1 = await self.analyze_image(image1, "Describe this image in detail.")
        analysis2 = await self.analyze_image(image2, "Describe this image in detail.")
        
        comparison_prompt = f"""
        Compare these two image descriptions and identify the {comparison_type}:
        
        Image 1: {analysis1}
        Image 2: {analysis2}
        
        Focus on the {comparison_type} between the two images.
        """
        
        # Use text-only LLM for comparison
        chat_ctx = llm.ChatContext()
        chat_ctx.messages.append(
            llm.ChatMessage.create(text=comparison_prompt, role="user")
        )
        
        stream = await self._vision_llm.chat(chat_ctx=chat_ctx)
        result = ""
        
        async for chunk in stream:
            if isinstance(chunk, llm.ChatResponse):
                if chunk.choices and chunk.choices[0].message.content:
                    result = chunk.choices[0].message.content
                    break
        
        return result
    
    def get_image_info(self, image_data: Union[bytes, str, Image.Image, np.ndarray]) -> Dict[str, Any]:
        """
        Get basic information about an image
        
        Args:
            image_data: Image data
            
        Returns:
            Image information dictionary
        """
        try:
            image = self._preprocess_image(image_data, preprocess=False)
            
            return {
                "size": image.size,
                "mode": image.mode,
                "format": getattr(image, 'format', 'Unknown'),
                "has_transparency": image.mode in ('RGBA', 'LA') or 'transparency' in image.info,
                "estimated_size_kb": len(self._image_to_base64(image)) / 1024,
            }
            
        except Exception as e:
            logger.error(f"Failed to get image info: {e}")
            return {"error": str(e)}

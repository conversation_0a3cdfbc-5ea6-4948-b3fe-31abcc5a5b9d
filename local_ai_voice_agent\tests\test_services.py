"""
Unit tests for Local AI Voice Agent services
"""

import asyncio
import pytest
import numpy as np
from unittest.mock import Mock, patch, AsyncMock

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from local_ai_voice_agent.services.local_tts import LocalTTS
from local_ai_voice_agent.services.local_stt import LocalWhisperSTT
from local_ai_voice_agent.services.ollama_llm import OllamaLLM
from local_ai_voice_agent.services.vision_service import VisionService
from local_ai_voice_agent.utils.model_utils import ModelManager
from local_ai_voice_agent.utils.audio_utils import AudioProcessor


class TestLocalTTS:
    """Test Local TTS service"""
    
    def test_engine_detection(self):
        """Test TTS engine auto-detection"""
        with patch('platform.system') as mock_system:
            mock_system.return_value = 'Windows'
            
            with patch('win32com.client') as mock_win32:
                tts = LocalTTS(engine="auto")
                assert tts._engine_name == "sapi"
    
    def test_tts_initialization(self):
        """Test TTS initialization"""
        with patch('local_ai_voice_agent.services.local_tts.Pyttsx3Engine'):
            tts = LocalTTS(engine="pyttsx3", rate=200, volume=0.9)
            assert tts._rate == 200
            assert tts._volume == 0.9
    
    @pytest.mark.asyncio
    async def test_tts_synthesis(self):
        """Test TTS synthesis"""
        with patch('local_ai_voice_agent.services.local_tts.Pyttsx3Engine') as mock_engine:
            mock_engine_instance = Mock()
            mock_engine_instance.synthesize.return_value = b"fake_audio_data"
            mock_engine.return_value = mock_engine_instance
            
            tts = LocalTTS(engine="pyttsx3")
            stream = tts.synthesize("Hello world")
            
            audio_chunks = []
            async for chunk in stream:
                audio_chunks.append(chunk)
            
            assert len(audio_chunks) > 0


class TestLocalWhisperSTT:
    """Test Local Whisper STT service"""
    
    def test_stt_initialization(self):
        """Test STT initialization"""
        with patch('whisper.load_model') as mock_load:
            mock_load.return_value = Mock()
            
            stt = LocalWhisperSTT(model="base", language="en")
            assert stt._model_name == "base"
            assert stt._language == "en"
    
    def test_device_selection(self):
        """Test device selection logic"""
        with patch('torch.cuda.is_available', return_value=True):
            with patch('whisper.load_model') as mock_load:
                mock_load.return_value = Mock()
                
                stt = LocalWhisperSTT(device="auto")
                assert stt._device == "cuda"
    
    @pytest.mark.asyncio
    async def test_stt_recognition(self):
        """Test STT recognition"""
        with patch('whisper.load_model') as mock_load:
            mock_model = Mock()
            mock_model.transcribe.return_value = {
                "text": "Hello world",
                "language": "en"
            }
            mock_load.return_value = mock_model
            
            stt = LocalWhisperSTT(model="base")
            
            # Create mock audio buffer
            mock_buffer = Mock()
            
            with patch.object(stt, '_buffer_to_numpy') as mock_convert:
                mock_convert.return_value = np.array([0.1, 0.2, 0.3])
                
                result = await stt._recognize_impl(mock_buffer)
                
                assert result.alternatives[0].text == "Hello world"
                assert result.alternatives[0].language == "en"


class TestOllamaLLM:
    """Test Ollama LLM service"""
    
    def test_llm_initialization(self):
        """Test LLM initialization"""
        with patch('local_ai_voice_agent.services.ollama_llm.OpenAILLM') as mock_openai:
            llm = OllamaLLM(
                model="test-model",
                base_url="http://localhost:11434/v1",
                temperature=0.7
            )
            
            assert llm._model == "test-model"
            assert llm._temperature == 0.7
    
    @pytest.mark.asyncio
    async def test_llm_chat(self):
        """Test LLM chat functionality"""
        with patch('local_ai_voice_agent.services.ollama_llm.OpenAILLM') as mock_openai:
            mock_openai_instance = Mock()
            mock_openai.with_ollama.return_value = mock_openai_instance
            
            llm = OllamaLLM(model="test-model")
            
            from livekit.agents import llm as lk_llm
            chat_ctx = lk_llm.ChatContext()
            
            stream = await llm.chat(chat_ctx=chat_ctx)
            assert stream is not None
    
    def test_response_cleaning(self):
        """Test response cleaning functionality"""
        with patch('local_ai_voice_agent.services.ollama_llm.OpenAILLM'):
            llm = OllamaLLM(model="test-model", clean_response=True)
            
            dirty_text = "<think>This is thinking</think>Hello world"
            clean_text = llm._clean_model_response(dirty_text)
            
            assert clean_text == "Hello world"


class TestVisionService:
    """Test Vision service"""
    
    def test_vision_initialization(self):
        """Test Vision service initialization"""
        with patch('local_ai_voice_agent.services.vision_service.QwenVisionLLM'):
            vision = VisionService(max_image_size=(512, 512))
            assert vision._max_image_size == (512, 512)
    
    @pytest.mark.asyncio
    async def test_image_analysis(self):
        """Test image analysis"""
        with patch('local_ai_voice_agent.services.vision_service.QwenVisionLLM') as mock_llm:
            mock_llm_instance = Mock()
            mock_llm_instance.analyze_image = AsyncMock(return_value="A test image")
            mock_llm.return_value = mock_llm_instance
            
            vision = VisionService()
            
            # Create fake image data
            fake_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            
            with patch.object(vision, '_preprocess_image') as mock_preprocess:
                from PIL import Image
                mock_preprocess.return_value = Image.fromarray(fake_image)
                
                result = await vision.analyze_image(fake_image, "Describe this image")
                assert result == "A test image"
    
    def test_image_preprocessing(self):
        """Test image preprocessing"""
        with patch('local_ai_voice_agent.services.vision_service.QwenVisionLLM'):
            vision = VisionService(max_image_size=(100, 100))
            
            # Create large image
            large_image = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
            
            from PIL import Image
            pil_image = Image.fromarray(large_image)
            
            processed = asyncio.run(vision._preprocess_image(pil_image))
            
            # Should be resized
            assert max(processed.size) <= 100


class TestModelManager:
    """Test Model Manager"""
    
    def test_model_manager_initialization(self):
        """Test Model Manager initialization"""
        with patch('os.path.exists', return_value=False):
            with patch.object(ModelManager, '_create_default_config') as mock_create:
                mock_create.return_value = "config/models.yaml"
                
                manager = ModelManager()
                assert manager._config_path == "config/models.yaml"
    
    def test_config_loading(self):
        """Test configuration loading"""
        mock_config = {
            "llm": {
                "primary": {
                    "model_id": "test-model",
                    "endpoint": "http://localhost:11434/v1"
                }
            }
        }
        
        with patch('builtins.open', create=True) as mock_open:
            with patch('yaml.safe_load', return_value=mock_config):
                manager = ModelManager()
                manager._load_config()
                
                llm_config = manager.get_llm_config()
                assert llm_config["model_id"] == "test-model"
    
    @pytest.mark.asyncio
    async def test_ollama_model_check(self):
        """Test Ollama model availability check"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "models": [{"name": "test-model:latest"}]
            }
            
            mock_client_instance = Mock()
            mock_client_instance.get = AsyncMock(return_value=mock_response)
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_client_instance)
            mock_client.return_value.__aexit__ = AsyncMock(return_value=None)
            
            manager = ModelManager()
            manager._config = {
                "llm": {
                    "primary": {"model_id": "test-model"}
                }
            }
            
            results = await manager.check_ollama_models()
            assert "test-model" in results


class TestAudioProcessor:
    """Test Audio Processor"""
    
    def test_audio_processor_initialization(self):
        """Test Audio Processor initialization"""
        processor = AudioProcessor(sample_rate=16000, channels=1)
        assert processor._sample_rate == 16000
        assert processor._channels == 1
    
    def test_sample_rate_conversion(self):
        """Test sample rate conversion"""
        processor = AudioProcessor()
        
        # Create test audio
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 8000))
        
        converted = processor.convert_sample_rate(audio_data, 8000, 16000)
        
        # Should be approximately double the length
        assert len(converted) > len(audio_data) * 1.8
        assert len(converted) < len(audio_data) * 2.2
    
    def test_audio_normalization(self):
        """Test audio normalization"""
        processor = AudioProcessor()
        
        # Create test audio with known amplitude
        audio_data = np.array([0.1, -0.1, 0.2, -0.2])
        
        normalized = processor.normalize_audio(audio_data, target_level=0.5)
        
        # Check that max amplitude is close to target
        max_amplitude = np.max(np.abs(normalized))
        assert abs(max_amplitude - 0.5) < 0.1
    
    def test_mono_conversion(self):
        """Test stereo to mono conversion"""
        processor = AudioProcessor()
        
        # Create stereo audio
        stereo_audio = np.array([[0.1, 0.2], [0.3, 0.4], [0.5, 0.6]])
        
        mono_audio = processor.convert_to_mono(stereo_audio)
        
        assert mono_audio.ndim == 1
        assert len(mono_audio) == 3
        assert mono_audio[0] == 0.15  # Average of 0.1 and 0.2


# Test runner
if __name__ == "__main__":
    pytest.main([__file__, "-v"])

"""
Qwen VL Processor Thread
Vision-Language AI inference with Ollama integration
"""

import asyncio
import httpx
import json
import time
import logging
from typing import Optional, Dict, Any, List
import base64
from io import BytesIO
from PIL import Image

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)
from .circuit_breaker import CircuitBreaker

logger = logging.getLogger(__name__)


class QwenVLThread(HighPriorityThread):
    """Qwen Vision-Language processing thread with Ollama integration"""
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 ollama_host: str = "localhost",
                 ollama_port: int = 11434,
                 model_name: str = "qwen2.5vl:32b",
                 max_response_tokens: int = 150,
                 temperature: float = 0.9,
                 context_memory_size: int = 20):
        
        super().__init__("QwenVL", priority="high", target_fps=10)
        
        # Queue management
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # Ollama configuration
        self.ollama_host = ollama_host
        self.ollama_port = ollama_port
        self.ollama_base_url = f"http://{ollama_host}:{ollama_port}"
        self.model_name = model_name
        
        # AI parameters
        self.max_response_tokens = max_response_tokens
        self.temperature = temperature
        self.context_memory_size = context_memory_size
        
        # HTTP client
        self.client = None
        
        # Circuit breaker for fault tolerance
        self.circuit_breaker = CircuitBreaker(
            name="QwenVL",
            failure_threshold=5,
            recovery_timeout=30.0
        )
        
        # Conversation context
        self.conversation_history = []
        
        # Performance tracking
        self.inference_times = []
        self.total_requests = 0
        self.successful_requests = 0
        
        logger.info(f"QwenVL processor initialized: {model_name}")

    def initialize(self) -> bool:
        """Initialize QwenVL processor (required by HighPriorityThread)"""
        try:
            logger.info("QwenVL: Initializing processor...")
            # Async initialization will be done in the processing loop
            return True
        except Exception as e:
            logger.error(f"QwenVL: Initialization failed: {e}")
            return False

    async def initialize_async_components(self):
        """Initialize async HTTP client"""
        try:
            self.client = httpx.AsyncClient(
                base_url=self.ollama_base_url,
                timeout=30.0,
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
            
            # Test connection
            response = await self.client.get("/api/tags")
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [m["name"] for m in models]
                if self.model_name in model_names:
                    logger.info(f"CONNECT: Qwen VL model {self.model_name} available")
                    return True
                else:
                    logger.error(f"ERROR: Model {self.model_name} not found. Available: {model_names}")
                    return False
            else:
                logger.error(f"ERROR: Ollama not responding: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"ERROR: Failed to initialize Qwen VL client: {e}")
            return False
    
    def run_processing_loop(self):
        """Main processing loop"""
        # Initialize async components
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Initialize async client
            if not loop.run_until_complete(self.initialize_async_components()):
                logger.error("FATAL: Failed to initialize Qwen VL - shutting down")
                return
            
            logger.info("START: Qwen VL processor running")

            # Run the async processing loop
            loop.run_until_complete(self._async_processing_loop())

        except Exception as e:
            logger.error(f"FATAL: Qwen VL processing loop failed: {e}")
        finally:
            if self.client:
                loop.run_until_complete(self.client.aclose())
            loop.close()

    async def _async_processing_loop(self):
        """Async processing loop"""
        while not self.should_stop:
            try:
                # Get input from queue (FIXED: using get_nowait instead of get)
                stt_result = self.input_queue.get_nowait()
                if stt_result is None:
                    await asyncio.sleep(0.01)  # Small delay to prevent busy waiting
                    continue

                # DEBUG: Log when we receive STT input
                logger.info(f"QWEN DEBUG: Received STT input for processing")

                # Process the input
                start_time = time.time()
                response = await self.process_input(stt_result)
                processing_time = (time.time() - start_time) * 1000

                if response:
                    # DEBUG: Log AI response
                    logger.info(f"QWEN DEBUG: Generated AI response, sending to TTS")

                    # Send to output queue (FIXED: using put_nowait instead of put)
                    success = self.output_queue.put_nowait(response)
                    if success:
                        self.successful_requests += 1
                        self.inference_times.append(processing_time)
                        logger.debug(f"AI: Response generated in {processing_time:.1f}ms")
                    else:
                        logger.warning("WARNING: Output queue full - dropping AI response")

                self.total_requests += 1

            except Exception as e:
                logger.error(f"ERROR: Error in Qwen VL processing loop: {e}")
                await asyncio.sleep(0.1)

    async def process_input(self, stt_result: ProcessingResult) -> Optional[ProcessingResult]:
        """Process STT input and generate AI response"""
        try:
            if not self.circuit_breaker.can_execute():
                logger.warning("CIRCUIT: Qwen VL circuit breaker open - skipping request")
                return None
            
            # Extract text from STT result
            if not stt_result.data or 'text' not in stt_result.data:
                logger.warning("WARNING: Invalid STT result - no text found")
                return None
            
            user_text = stt_result.data['text'].strip()
            if not user_text:
                logger.debug("DEBUG: Empty text from STT - skipping")
                return None
            
            # Generate AI response
            ai_response = await self.generate_response(user_text)
            
            if ai_response:
                self.circuit_breaker.record_success()
                
                # Create response result
                response_result = ProcessingResult(
                    data={
                        'text': ai_response,
                        'model': self.model_name,
                        'timestamp': time.time(),
                        'processing_time_ms': 0  # Will be set by caller
                    },
                    timestamp=time.time(),
                    processing_time_ms=0,
                    metadata={
                        'source': 'qwen_vl',
                        'user_input': user_text
                    }
                )
                
                return response_result
            else:
                self.circuit_breaker.record_failure()
                return None
                
        except Exception as e:
            logger.error(f"ERROR: Error processing Qwen VL input: {e}")
            self.circuit_breaker.record_failure()
            return None
    
    async def generate_response(self, user_text: str, image_data: Optional[bytes] = None) -> Optional[str]:
        """Generate AI response using Qwen VL model"""
        try:
            # Build conversation context
            context = self._build_context()
            
            # Create system prompt
            system_prompt = """You are Josie, a helpful AI voice assistant. You have vision capabilities and can see images when provided.

Key traits:
- Warm, friendly, and conversational
- Keep responses concise (under 30 words for voice)
- Be natural and engaging
- If you see an image, describe what you observe

Respond naturally as if having a real conversation."""
            
            # Build prompt
            if context:
                full_prompt = f"{system_prompt}\n\nContext: {context}\n\nUser: {user_text}\n\nJosie:"
            else:
                full_prompt = f"{system_prompt}\n\nUser: {user_text}\n\nJosie:"
            
            # Prepare request payload
            request_data = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_response_tokens,
                    "top_k": 30,
                    "top_p": 0.9
                }
            }
            
            # Add image if provided
            if image_data:
                # Convert image to base64
                image_b64 = base64.b64encode(image_data).decode('utf-8')
                request_data["images"] = [image_b64]
            
            # Send request to Ollama
            response = await self.client.post("/api/generate", json=request_data)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get("response", "").strip()
                
                if ai_response:
                    # Update conversation history
                    self._update_conversation_history(user_text, ai_response)
                    return ai_response
                else:
                    logger.warning("WARNING: Empty response from Qwen VL")
                    return None
            else:
                logger.error(f"ERROR: Qwen VL API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"ERROR: Error generating Qwen VL response: {e}")
            return None
    
    def _build_context(self) -> str:
        """Build conversation context from history"""
        if not self.conversation_history:
            return ""
        
        # Get recent conversation
        recent_history = self.conversation_history[-3:]  # Last 3 exchanges
        context_parts = []
        
        for exchange in recent_history:
            context_parts.append(f"User: {exchange['user']}")
            context_parts.append(f"Josie: {exchange['assistant']}")
        
        return "\n".join(context_parts)
    
    def _update_conversation_history(self, user_text: str, ai_response: str):
        """Update conversation history"""
        self.conversation_history.append({
            'user': user_text,
            'assistant': ai_response,
            'timestamp': time.time()
        })
        
        # Keep only recent history
        if len(self.conversation_history) > self.context_memory_size:
            self.conversation_history = self.conversation_history[-self.context_memory_size:]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        avg_inference_time = 0
        if self.inference_times:
            avg_inference_time = sum(self.inference_times) / len(self.inference_times)
        
        success_rate = 0
        if self.total_requests > 0:
            success_rate = self.successful_requests / self.total_requests
        
        return {
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'success_rate': success_rate,
            'avg_inference_time_ms': avg_inference_time,
            'circuit_breaker_state': self.circuit_breaker.state,
            'conversation_length': len(self.conversation_history)
        }

"""
Qwen 2.5VL Vision-Language Model Processor
High-performance AI inference with vision capabilities
"""

import asyncio
import time
import logging
import json
import base64
import queue
from typing import Optional, Dict, Any, List, Union
from pathlib import Path
import threading
from dataclasses import dataclass

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    print("httpx not available. Install with: pip install httpx")

try:
    import PIL.Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available. Install with: pip install Pillow")

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)
from .circuit_breaker import CircuitBreaker, CircuitBreakerError, CircuitBreakerConfig

logger = logging.getLogger(__name__)

@dataclass
class VisionInput:
    """Vision input data structure"""
    image_data: Optional[bytes] = None
    image_path: Optional[str] = None
    image_base64: Optional[str] = None
    description: Optional[str] = None

@dataclass
class QwenVLRequest:
    """Qwen VL processing request"""
    text: str
    vision_input: Optional[VisionInput] = None
    temperature: float = 0.7
    max_tokens: int = 2048
    stream: bool = False

class QwenVLProcessor:
    """
    Qwen 2.5VL Vision-Language Model Processor
    - Direct Ollama integration for local processing
    - Vision and text understanding
    - Optimized for conversation flow
    - GPU acceleration support
    """
    
    def __init__(self,
                 ollama_host: str = "localhost",
                 ollama_port: int = 11434,
                 model_name: str = "qwen2.5vl:32b",
                 max_context_length: int = 8192,
                 temperature: float = 0.7,
                 timeout: float = 30.0):
        
        self.ollama_host = ollama_host
        self.ollama_port = ollama_port
        self.model_name = model_name
        self.max_context_length = max_context_length
        self.temperature = temperature
        self.timeout = timeout
        
        # Build Ollama URL
        self.ollama_url = f"http://{ollama_host}:{ollama_port}"
        
        # HTTP client for Ollama API
        self.client = None
        if HTTPX_AVAILABLE:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(timeout),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
        
        # Circuit breaker for fault tolerance
        breaker_config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30.0,
            success_threshold=2,
            timeout=timeout
        )
        self.circuit_breaker = CircuitBreaker(
            name="qwen_vl",
            config=breaker_config
        )
        
        # Conversation context management
        self.conversation_history = []
        self.max_history_length = 10
        
        # Performance tracking
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.avg_response_time = 0.0
        self.vision_requests = 0
        
        # Model capabilities
        self.supports_vision = True
        self.supports_streaming = True
        self.max_image_size = 1024 * 1024 * 10  # 10MB max image size
        
        logger.info(f"Qwen VL Processor initialized: {model_name}")
        logger.info(f"Ollama endpoint: {self.ollama_url}")
        logger.info(f"Vision support: {self.supports_vision}")
    
    async def initialize(self) -> bool:
        """Initialize the Qwen VL processor"""
        try:
            # Test connection to Ollama
            if not await self._test_ollama_connection():
                logger.error("Failed to connect to Ollama")
                return False
            
            # Verify model availability
            if not await self._verify_model_availability():
                logger.error(f"Model {self.model_name} not available")
                return False
            
            logger.info("Qwen VL Processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Qwen VL initialization error: {e}")
            return False
    
    async def _test_ollama_connection(self) -> bool:
        """Test connection to Ollama server"""
        try:
            if not self.client:
                return False
                
            response = await self.client.get(f"{self.ollama_url}/api/tags")
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Ollama connection test failed: {e}")
            return False
    
    async def _verify_model_availability(self) -> bool:
        """Verify that the Qwen VL model is available"""
        try:
            if not self.client:
                return False
                
            response = await self.client.get(f"{self.ollama_url}/api/tags")
            if response.status_code != 200:
                return False
            
            models_data = response.json()
            available_models = [model["name"] for model in models_data.get("models", [])]
            
            model_available = any(self.model_name in model for model in available_models)
            if model_available:
                logger.info(f"Model {self.model_name} is available")
            else:
                logger.warning(f"Model {self.model_name} not found in: {available_models}")
            
            return model_available
            
        except Exception as e:
            logger.error(f"Model verification error: {e}")
            return False
    
    async def process_request(self, request: QwenVLRequest) -> Optional[Dict[str, Any]]:
        """Process a Qwen VL request with vision and text"""
        start_time = time.time()
        
        try:
            # Generate response with circuit breaker protection
            response = await self._generate_response(request)
            
            if response:
                # Update performance metrics
                processing_time = time.time() - start_time
                self.total_requests += 1
                self.successful_requests += 1
                self.avg_response_time = (
                    (self.avg_response_time * (self.successful_requests - 1) + processing_time) 
                    / self.successful_requests
                )
                
                if request.vision_input:
                    self.vision_requests += 1
                
                logger.debug(f"Qwen VL response generated in {processing_time:.3f}s")
                return response
            else:
                self.failed_requests += 1
                return None
                
        except CircuitBreakerError:
            logger.warning("Qwen VL circuit breaker open - service unavailable")
            self.failed_requests += 1
            return self._get_fallback_response(request.text)
        except Exception as e:
            logger.error(f"Qwen VL processing error: {e}")
            self.failed_requests += 1
            return None
    
    async def _generate_response(self, request: QwenVLRequest) -> Optional[Dict[str, Any]]:
        """Generate response using Qwen VL model"""
        try:
            if not self.client:
                logger.error("HTTP client not available")
                return None
            
            # Build the prompt
            prompt_data = await self._build_prompt(request)
            
            # Make request to Ollama
            ollama_request = {
                "model": self.model_name,
                "prompt": prompt_data["text"],
                "stream": False,
                "options": {
                    "temperature": request.temperature,
                    "num_predict": request.max_tokens,
                    "top_k": 40,
                    "top_p": 0.9,
                    "repeat_penalty": 1.1
                }
            }
            
            # Add images if present
            if prompt_data.get("images"):
                ollama_request["images"] = prompt_data["images"]
            
            response = await self.client.post(
                f"{self.ollama_url}/api/generate",
                json=ollama_request,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "").strip()
                
                if response_text:
                    # Update conversation history
                    self._update_conversation_history(request.text, response_text)
                    
                    return {
                        "text": response_text,
                        "model": self.model_name,
                        "confidence": 0.9,
                        "timestamp": time.time(),
                        "tokens": len(response_text.split()),
                        "has_vision": request.vision_input is not None,
                        "context_length": len(self.conversation_history)
                    }
            
            logger.warning(f"Qwen VL API error: {response.status_code}")
            return None
            
        except Exception as e:
            logger.error(f"Qwen VL generation error: {e}")
            return None
    
    async def _build_prompt(self, request: QwenVLRequest) -> Dict[str, Any]:
        """Build prompt with context and vision data"""
        prompt_data = {"text": "", "images": []}
        
        # Add conversation context
        context_prompt = self._build_context_prompt()
        
        # Build main prompt
        if request.vision_input and request.vision_input.image_base64:
            # Vision + text prompt
            prompt_text = f"{context_prompt}\n\nUser: {request.text}"
            if request.vision_input.description:
                prompt_text += f"\n[Image: {request.vision_input.description}]"
            prompt_text += "\n\nAssistant:"
            
            prompt_data["text"] = prompt_text
            prompt_data["images"] = [request.vision_input.image_base64]
            
        else:
            # Text-only prompt
            prompt_text = f"{context_prompt}\n\nUser: {request.text}\n\nAssistant:"
            prompt_data["text"] = prompt_text
        
        return prompt_data
    
    def _build_context_prompt(self) -> str:
        """Build conversation context prompt"""
        if not self.conversation_history:
            return "You are a helpful AI assistant with vision capabilities. Respond naturally and conversationally."
        
        context = "Previous conversation:\n"
        for exchange in self.conversation_history[-5:]:  # Last 5 exchanges
            context += f"User: {exchange['user']}\nAssistant: {exchange['assistant']}\n"
        
        return context
    
    def _update_conversation_history(self, user_text: str, assistant_text: str):
        """Update conversation history"""
        self.conversation_history.append({
            "user": user_text,
            "assistant": assistant_text,
            "timestamp": time.time()
        })
        
        # Trim history if too long
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[-self.max_history_length:]
    
    def _get_fallback_response(self, user_text: str) -> Dict[str, Any]:
        """Generate fallback response when main system fails"""
        fallback_responses = [
            "I'm having trouble processing that right now. Could you try again?",
            "Sorry, I'm experiencing some technical difficulties. Please repeat your question.",
            "I need a moment to process that. Could you rephrase your request?"
        ]
        
        import random
        response_text = random.choice(fallback_responses)
        
        return {
            "text": response_text,
            "model": "fallback",
            "confidence": 0.3,
            "timestamp": time.time(),
            "tokens": len(response_text.split()),
            "has_vision": False,
            "context_length": 0
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        success_rate = (self.successful_requests / max(self.total_requests, 1)) * 100
        
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": success_rate,
            "avg_response_time": self.avg_response_time,
            "vision_requests": self.vision_requests,
            "conversation_length": len(self.conversation_history),
            "model_name": self.model_name
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.client:
            await self.client.aclose()
        logger.info("Qwen VL Processor cleaned up")


class QwenVLThread(HighPriorityThread):
    """
    High-priority thread for Qwen VL processing
    Integrates with the existing threading infrastructure
    """
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 ollama_host: str = "localhost",
                 ollama_port: int = 11434,
                 model_name: str = "qwen2.5vl:32b"):
        
        super().__init__("QwenVLProcessor", priority="normal", target_fps=10.0)
        
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # Initialize Qwen VL processor
        self.qwen_processor = QwenVLProcessor(
            ollama_host=ollama_host,
            ollama_port=ollama_port,
            model_name=model_name
        )
        
        # Processing state
        self.processor_ready = False
        self.processing_active = True
        
        logger.info("Qwen VL Thread initialized")
    
    async def initialize_processor(self):
        """Initialize the Qwen VL processor"""
        self.processor_ready = await self.qwen_processor.initialize()
        return self.processor_ready
    
    def process_loop(self):
        """Main processing loop"""
        # Initialize processor
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Initialize processor
            if not loop.run_until_complete(self.initialize_processor()):
                logger.error("Failed to initialize Qwen VL processor")
                return
            
            logger.info("Qwen VL processor started and ready")
            
            # Main processing loop
            while not self.shutdown_event.is_set() and self.processing_active:
                try:
                    # Get input from queue
                    input_data = self.input_queue.get(timeout=0.1)
                    if input_data is None:
                        continue
                    
                    # Process the request
                    start_time = time.time()
                    
                    # Create Qwen VL request
                    if isinstance(input_data, dict):
                        text = input_data.get('text', '')
                        vision_input = input_data.get('vision_input')
                    else:
                        text = str(input_data)
                        vision_input = None
                    
                    request = QwenVLRequest(
                        text=text,
                        vision_input=vision_input,
                        temperature=0.7,
                        max_tokens=2048
                    )
                    
                    # Generate response
                    response = loop.run_until_complete(
                        self.qwen_processor.process_request(request)
                    )
                    
                    if response:
                        # Create processing result
                        processing_time = time.time() - start_time
                        result = ProcessingResult(
                            data=response,
                            timestamp=time.time(),
                            latency=processing_time * 1000,  # Convert to ms
                            confidence=response.get('confidence', 0.9),
                            stage="qwen_vl",
                            chunk_id=getattr(input_data, 'chunk_id', 0)
                        )
                        
                        # Send to output queue
                        self.output_queue.put_nowait(result)
                        
                        # Update performance stats
                        self.update_performance_stats(processing_time)
                        
                        logger.debug(f"Qwen VL processed in {processing_time:.3f}s")
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Qwen VL processing error: {e}")
                    self.performance_stats['errors'] += 1
        
        finally:
            # Cleanup
            loop.run_until_complete(self.qwen_processor.cleanup())
            loop.close()
            logger.info("Qwen VL Thread stopped")

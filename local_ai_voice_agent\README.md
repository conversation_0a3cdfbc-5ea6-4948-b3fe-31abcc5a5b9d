# 🤖 Local AI Voice Agent

A comprehensive AI voice agent with vision, voice, hearing, and conversation capabilities using local models only - no external APIs required.

## 🎯 Features

- **🗣️ Voice Conversation**: Natural voice interactions with local TTS/STT
- **👁️ Vision Understanding**: Image and video processing with <PERSON>wen 2.5-VL
- **🧠 Local Intelligence**: Powered by Ollama models (Qwen 2.5-VL, JOSIEFIED-Qwen3)
- **🔊 Speech Recognition**: Local Whisper implementation
- **🎵 Speech Synthesis**: Windows SAPI or local TTS systems
- **🚫 No External APIs**: Completely offline and private

## 🏗️ Architecture

```
local_ai_voice_agent/
├── agents/                 # Main agent implementations
│   ├── voice_agent.py     # Primary voice agent
│   ├── vision_agent.py    # Vision processing agent
│   └── multimodal_agent.py # Combined multimodal agent
├── services/              # Local service implementations
│   ├── local_tts.py      # Local TTS implementation
│   ├── local_stt.py      # Local Whisper STT
│   ├── ollama_llm.py     # Ollama LLM integration
│   └── vision_service.py  # Vision processing service
├── config/               # Configuration files
│   ├── models.yaml       # Model configurations
│   ├── agent_config.yaml # Agent settings
│   └── environment.env   # Environment variables
├── utils/               # Utility functions
│   ├── audio_utils.py   # Audio processing utilities
│   ├── image_utils.py   # Image processing utilities
│   └── model_utils.py   # Model management utilities
├── examples/            # Example applications
│   ├── basic_chat.py    # Basic voice chat
│   ├── vision_chat.py   # Vision-enabled chat
│   └── demo_app.py      # Full demo application
├── tests/              # Test files
│   ├── test_tts.py     # TTS tests
│   ├── test_stt.py     # STT tests
│   └── test_agent.py   # Agent tests
├── requirements.txt    # Python dependencies
└── setup.py           # Package setup
```

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Models**
   ```bash
   # Ensure Ollama is running with your models
   ollama list
   ```

3. **Run Basic Voice Agent**
   ```bash
   python examples/basic_chat.py
   ```

4. **Run Vision-Enabled Agent**
   ```bash
   python examples/vision_chat.py
   ```

## 🔧 Configuration

### Available Models
- **LLM**: Qwen 2.5-VL (32B), JOSIEFIED-Qwen3 (14B)
- **STT**: Whisper Large V3 (local)
- **TTS**: Windows SAPI, pyttsx3
- **VAD**: Silero VAD
- **Vision**: Qwen 2.5-VL multimodal

### Model Settings
Edit `config/models.yaml` to configure your preferred models and settings.

## 📋 Requirements

- Python 3.8+
- Ollama with Qwen models installed
- Windows (for SAPI TTS) or cross-platform TTS
- Sufficient RAM for local models (16GB+ recommended)
- Optional: GPU for faster inference

## 🎮 Usage Examples

### Basic Voice Chat
```python
from agents.voice_agent import LocalVoiceAgent

agent = LocalVoiceAgent()
await agent.start_conversation()
```

### Vision-Enabled Chat
```python
from agents.multimodal_agent import MultimodalAgent

agent = MultimodalAgent()
await agent.start_with_vision()
```

## 🔒 Privacy & Security

- **100% Local**: All processing happens on your machine
- **No Data Transmission**: No external API calls or data sharing
- **Private Conversations**: All interactions stay on your device
- **Offline Capable**: Works without internet connection

## 🛠️ Development

### Adding New TTS Engines
Extend the `LocalTTS` class in `services/local_tts.py`

### Adding New Models
Update `config/models.yaml` and model loading in `utils/model_utils.py`

### Custom Agents
Create new agent classes inheriting from the base agent framework

## 📊 Performance

- **Response Time**: 2-5 seconds (depending on model size)
- **Memory Usage**: 8-20GB RAM (model dependent)
- **CPU/GPU**: Optimized for both CPU and GPU inference

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

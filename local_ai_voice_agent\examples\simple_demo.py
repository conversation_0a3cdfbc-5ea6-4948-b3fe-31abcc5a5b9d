"""
Simple demo of the Local AI Voice Agent
Demonstrates basic voice conversation capabilities
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from local_ai_voice_agent.services.local_tts import LocalTTS
from local_ai_voice_agent.services.local_stt import LocalWhisperSTT
from local_ai_voice_agent.services.ollama_llm import OllamaLLM
from local_ai_voice_agent.utils.model_utils import ModelManager
from local_ai_voice_agent.utils.audio_utils import AudioProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleVoiceDemo:
    """Simple voice conversation demo"""
    
    def __init__(self):
        self.model_manager = ModelManager()
        self.audio_processor = AudioProcessor()
        
        # Initialize services
        self.tts = None
        self.stt = None
        self.llm = None
    
    async def initialize(self):
        """Initialize all services"""
        try:
            logger.info("Initializing Local AI Voice Agent Demo...")
            
            # Initialize TTS
            logger.info("Loading TTS...")
            self.tts = LocalTTS(engine="auto", rate=200, volume=0.9)
            
            # Initialize STT
            logger.info("Loading Whisper STT...")
            self.stt = LocalWhisperSTT(model="base", language="en")
            
            # Initialize LLM
            logger.info("Loading Ollama LLM...")
            llm_config = self.model_manager.get_llm_config()
            self.llm = OllamaLLM(
                model=llm_config.get("model_id", "goekdenizguelmez/JOSIEFIED-Qwen3:14b"),
                base_url=llm_config.get("endpoint", "http://localhost:11434/v1")
            )
            
            logger.info("✓ All services initialized successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    async def test_tts(self, text: str = "Hello! This is a test of the text-to-speech system."):
        """Test TTS functionality"""
        logger.info("Testing TTS...")
        
        try:
            # Synthesize speech
            stream = self.tts.synthesize(text)
            
            # Collect audio data
            audio_frames = []
            async for audio in stream:
                audio_frames.append(audio.frame)
            
            if audio_frames:
                logger.info("✓ TTS test successful!")
                
                # Optionally save to file for testing
                # combined_audio = combine_audio_frames(audio_frames)
                # self.audio_processor.save_audio_to_file(combined_audio, "tts_test.wav")
                
                return True
            else:
                logger.error("✗ TTS test failed - no audio generated")
                return False
                
        except Exception as e:
            logger.error(f"✗ TTS test failed: {e}")
            return False
    
    async def test_llm(self, prompt: str = "Hello! Please introduce yourself briefly."):
        """Test LLM functionality"""
        logger.info("Testing LLM...")
        
        try:
            from livekit.agents import llm
            
            # Create chat context
            chat_ctx = llm.ChatContext()
            chat_ctx.messages.append(
                llm.ChatMessage.create(text=prompt, role="user")
            )
            
            # Get response
            stream = await self.llm.chat(chat_ctx=chat_ctx)
            response_text = ""
            
            async for chunk in stream:
                if isinstance(chunk, llm.ChatResponse):
                    if chunk.choices and chunk.choices[0].message.content:
                        response_text = chunk.choices[0].message.content
                        break
            
            if response_text:
                logger.info(f"✓ LLM test successful!")
                logger.info(f"Response: {response_text[:100]}...")
                return True
            else:
                logger.error("✗ LLM test failed - no response")
                return False
                
        except Exception as e:
            logger.error(f"✗ LLM test failed: {e}")
            return False
    
    async def test_integration(self):
        """Test integration of TTS + LLM"""
        logger.info("Testing TTS + LLM integration...")
        
        try:
            from livekit.agents import llm
            
            # Get response from LLM
            chat_ctx = llm.ChatContext()
            chat_ctx.messages.append(
                llm.ChatMessage.create(
                    text="Say a short, friendly greeting for a voice assistant demo.",
                    role="user"
                )
            )
            
            stream = await self.llm.chat(chat_ctx=chat_ctx)
            response_text = ""
            
            async for chunk in stream:
                if isinstance(chunk, llm.ChatResponse):
                    if chunk.choices and chunk.choices[0].message.content:
                        response_text = chunk.choices[0].message.content
                        break
            
            if response_text:
                # Synthesize the response
                tts_stream = self.tts.synthesize(response_text)
                
                audio_frames = []
                async for audio in tts_stream:
                    audio_frames.append(audio.frame)
                
                if audio_frames:
                    logger.info("✓ Integration test successful!")
                    logger.info(f"Generated and synthesized: {response_text}")
                    return True
            
            logger.error("✗ Integration test failed")
            return False
            
        except Exception as e:
            logger.error(f"✗ Integration test failed: {e}")
            return False
    
    async def run_interactive_demo(self):
        """Run an interactive demo (text-based for now)"""
        logger.info("Starting interactive demo...")
        logger.info("Type 'quit' to exit, 'help' for commands")
        
        from livekit.agents import llm
        chat_ctx = llm.ChatContext()
        
        # Add system message
        chat_ctx.messages.append(
            llm.ChatMessage.create(
                text="You are a helpful AI voice assistant. Keep responses concise and friendly.",
                role="system"
            )
        )
        
        while True:
            try:
                user_input = input("\nYou: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("Assistant: Goodbye!")
                    break
                elif user_input.lower() == 'help':
                    print("Commands:")
                    print("  help - Show this help")
                    print("  quit/exit/bye - Exit the demo")
                    print("  Just type normally to chat!")
                    continue
                elif not user_input:
                    continue
                
                # Add user message
                chat_ctx.messages.append(
                    llm.ChatMessage.create(text=user_input, role="user")
                )
                
                # Get LLM response
                print("Assistant: ", end="", flush=True)
                stream = await self.llm.chat(chat_ctx=chat_ctx)
                response_text = ""
                
                async for chunk in stream:
                    if isinstance(chunk, llm.ChatResponse):
                        if chunk.choices and chunk.choices[0].message.content:
                            response_text = chunk.choices[0].message.content
                            print(response_text)
                            break
                
                # Add assistant response to context
                if response_text:
                    chat_ctx.messages.append(
                        llm.ChatMessage.create(text=response_text, role="assistant")
                    )
                    
                    # Optionally synthesize speech
                    try:
                        tts_stream = self.tts.synthesize(response_text)
                        async for audio in tts_stream:
                            pass  # Audio would be played here
                    except Exception as e:
                        logger.debug(f"TTS synthesis failed: {e}")
                
            except KeyboardInterrupt:
                print("\nAssistant: Goodbye!")
                break
            except Exception as e:
                logger.error(f"Error in interactive demo: {e}")
                print("Assistant: Sorry, I encountered an error. Please try again.")
    
    async def run_all_tests(self):
        """Run all tests"""
        logger.info("Running comprehensive tests...")
        
        tests = [
            ("TTS", self.test_tts),
            ("LLM", self.test_llm),
            ("Integration", self.test_integration),
        ]
        
        results = {}
        for test_name, test_func in tests:
            logger.info(f"\n--- {test_name} Test ---")
            results[test_name] = await test_func()
        
        # Summary
        logger.info("\n--- Test Summary ---")
        for test_name, passed in results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            logger.info(f"{test_name}: {status}")
        
        all_passed = all(results.values())
        if all_passed:
            logger.info("\n🎉 All tests passed! The voice agent is ready.")
        else:
            logger.warning("\n⚠️  Some tests failed. Check the logs above.")
        
        return all_passed


async def main():
    """Main demo function"""
    demo = SimpleVoiceDemo()
    
    # Initialize services
    if not await demo.initialize():
        logger.error("Failed to initialize demo. Exiting.")
        return
    
    # Check command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "test":
            await demo.run_all_tests()
        elif command == "tts":
            await demo.test_tts()
        elif command == "llm":
            await demo.test_llm()
        elif command == "integration":
            await demo.test_integration()
        elif command == "interactive":
            await demo.run_interactive_demo()
        else:
            print(f"Unknown command: {command}")
            print("Available commands: test, tts, llm, integration, interactive")
    else:
        # Default: run tests then interactive demo
        if await demo.run_all_tests():
            print("\nStarting interactive demo...")
            await demo.run_interactive_demo()


if __name__ == "__main__":
    asyncio.run(main())

"""
Install Next-Generation Voice System Dependencies
Ultra-high quality local voice models and streaming audio
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def install_package(package_name, description=""):
    """Install a package with error handling"""
    try:
        logger.info(f"📦 Installing {package_name} - {description}")
        subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                      check=True, capture_output=True)
        logger.info(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {package_name}: {e}")
        return False


def main():
    """Install all next-generation voice dependencies"""
    logger.info("🚀 Installing NEXT-GENERATION voice system dependencies...")
    
    # Core audio processing
    packages = [
        ("pyaudio", "Real-time audio streaming"),
        ("webrtcvad", "Voice Activity Detection"),
        ("numpy", "Audio processing"),
        ("scipy", "Signal processing"),
        
        # Enhanced TTS engines
        ("edge-tts", "Microsoft Edge TTS"),
        ("pyttsx3", "Cross-platform TTS"),
        
        # Try to install high-quality TTS (may fail on some systems)
        ("TTS", "Coqui TTS - Ultra-high quality"),
        
        # Audio utilities
        ("soundfile", "Audio file handling"),
        ("librosa", "Audio analysis"),
        ("resampy", "Audio resampling"),
        
        # Streaming and async
        ("asyncio", "Async programming"),
        ("aiofiles", "Async file operations"),
        
        # Additional dependencies
        ("torch", "PyTorch for AI models"),
        ("transformers", "Hugging Face models"),
        ("accelerate", "Model acceleration"),
    ]
    
    successful = 0
    failed = 0
    
    for package, description in packages:
        if install_package(package, description):
            successful += 1
        else:
            failed += 1
    
    logger.info(f"\n📊 Installation Summary:")
    logger.info(f"✅ Successful: {successful}")
    logger.info(f"❌ Failed: {failed}")
    
    if failed > 0:
        logger.warning("⚠️ Some packages failed to install. The system will use fallback options.")
    
    logger.info("🎉 Next-generation voice system dependencies installation complete!")
    
    # Additional instructions
    print("\n" + "="*60)
    print("🚀 NEXT-GENERATION VOICE SYSTEM READY!")
    print("="*60)
    print("To start the ultra-natural conversation:")
    print("  python local_ai_voice_agent/examples/next_gen_voice_chat.py chat")
    print()
    print("To test the system:")
    print("  python local_ai_voice_agent/examples/next_gen_voice_chat.py test")
    print()
    print("Features:")
    print("  ⚡ Ultra-low latency streaming")
    print("  🗣️ Real-time interruption support")
    print("  💬 Natural back-and-forth conversation")
    print("  🎭 Human-like voice quality")
    print("  🏠 100% local processing")
    print("="*60)


if __name__ == "__main__":
    main()

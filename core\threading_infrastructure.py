"""
🚀 Ultra-Performance Threading Infrastructure
Core threading components for real-time voice agent
"""

import threading
import queue
import time
import sys
import ctypes
import numpy as np
from typing import Optional, Any, Dict, List
from dataclasses import dataclass
from abc import ABC, abstractmethod
import psutil

@dataclass
class AudioChunk:
    """Standardized audio data structure for inter-thread communication"""
    data: np.ndarray
    timestamp: float
    sample_rate: int
    channels: int
    chunk_id: int
    confidence: float = 1.0

@dataclass
class ProcessingResult:
    """Results from processing stages with performance metrics"""
    data: Any
    timestamp: float
    latency: float
    confidence: float
    stage: str
    chunk_id: int = 0

class LockFreeQueue:
    """
    Lock-free queue optimized for ultra-low latency inter-thread communication
    - Non-blocking operations
    - Overflow handling with item dropping
    - Performance tracking
    """
    def __init__(self, maxsize: int = 1000, name: str = "Queue"):
        self._queue = queue.Queue(maxsize=maxsize)
        self._dropped_items = 0
        self._total_items = 0
        self.name = name
        self._lock = threading.Lock()  # Only for stats
    
    def put_nowait(self, item: Any) -> bool:
        """Non-blocking put with overflow handling"""
        try:
            self._queue.put_nowait(item)
            self._total_items += 1
            return True
        except queue.Full:
            # Drop oldest item and add new one for real-time processing
            try:
                self._queue.get_nowait()  # Drop oldest
                self._queue.put_nowait(item)  # Add new
                self._dropped_items += 1
                self._total_items += 1
                return True
            except queue.Empty:
                return False
    
    def get_nowait(self) -> Optional[Any]:
        """Non-blocking get"""
        try:
            return self._queue.get_nowait()
        except queue.Empty:
            return None
    
    def size(self) -> int:
        """Current queue size"""
        return self._queue.qsize()
    
    def get_stats(self) -> Dict[str, int]:
        """Get queue performance statistics"""
        return {
            'total_items': self._total_items,
            'dropped_items': self._dropped_items,
            'current_size': self.size(),
            'drop_rate': self._dropped_items / max(self._total_items, 1)
        }

class LockFreeRingBuffer:
    """
    Ultra-low latency ring buffer for real-time audio processing
    - Zero-copy operations where possible
    - Atomic read/write pointers
    - Thread-safe without locks
    """
    def __init__(self, sample_rate: int = 48000, channels: int = 1, buffer_seconds: float = 2.0):
        self.sample_rate = sample_rate
        self.channels = channels
        self.buffer_seconds = buffer_seconds
        self.buffer_size = int(sample_rate * channels * buffer_seconds)
        
        # Pre-allocated buffer - no dynamic allocation during runtime
        self.buffer = np.zeros(self.buffer_size, dtype=np.float32)
        
        # Atomic pointers for thread-safe access
        self.write_ptr = ctypes.c_uint64(0)
        self.read_ptr = ctypes.c_uint64(0)
        
        # Performance tracking
        self.writes_count = 0
        self.reads_count = 0
        
    def write_chunk(self, audio_chunk: np.ndarray) -> bool:
        """Write audio chunk without blocking"""
        if audio_chunk.size == 0:
            return False
            
        chunk_size = len(audio_chunk)
        write_pos = self.write_ptr.value % self.buffer_size
        
        # Handle buffer wraparound efficiently
        if write_pos + chunk_size <= self.buffer_size:
            self.buffer[write_pos:write_pos + chunk_size] = audio_chunk
        else:
            # Split write across buffer boundary
            first_part = self.buffer_size - write_pos
            self.buffer[write_pos:] = audio_chunk[:first_part]
            self.buffer[:chunk_size - first_part] = audio_chunk[first_part:]
        
        # Atomic update of write pointer
        self.write_ptr.value += chunk_size
        self.writes_count += 1
        return True
        
    def read_latest_chunk(self, chunk_size: int = 768) -> np.ndarray:
        """Read latest audio chunk for processing"""
        if self.write_ptr.value < chunk_size:
            return np.zeros(chunk_size, dtype=np.float32)
            
        read_pos = (self.write_ptr.value - chunk_size) % self.buffer_size
        
        if read_pos + chunk_size <= self.buffer_size:
            chunk = self.buffer[read_pos:read_pos + chunk_size].copy()
        else:
            # Handle wraparound read
            first_part = self.buffer_size - read_pos
            chunk = np.concatenate([
                self.buffer[read_pos:],
                self.buffer[:chunk_size - first_part]
            ])
        
        self.reads_count += 1
        return chunk
    
    def get_buffer_usage(self) -> float:
        """Get current buffer usage percentage"""
        available_samples = self.write_ptr.value % self.buffer_size
        return (available_samples / self.buffer_size) * 100

class HighPriorityThread(threading.Thread, ABC):
    """
    Base class for high-priority processing threads
    - OS-level priority setting
    - Performance monitoring
    - Graceful shutdown handling
    """
    def __init__(self, name: str, priority: str = "high", target_fps: float = 1000.0):
        super().__init__(name=name, daemon=True)
        self.priority = priority
        self.target_fps = target_fps
        self.target_interval = 1.0 / target_fps
        
        # Threading control
        self.running = threading.Event()
        self.shutdown_event = threading.Event()
        
        # Performance monitoring
        self.performance_stats = {
            'processing_times': [],
            'items_processed': 0,
            'errors': 0,
            'avg_processing_time': 0.0,
            'max_processing_time': 0.0,
            'fps': 0.0
        }
        
        # Timing for FPS calculation
        self.last_fps_calculation = time.time()
        self.fps_counter = 0
        
    def set_thread_priority(self):
        """Set OS-level thread priority for optimal performance"""
        try:
            if sys.platform == "win32":
                import win32api, win32process, win32con
                
                # Get current thread handle
                thread_handle = win32api.GetCurrentThread()
                
                if self.priority == "highest":
                    win32process.SetThreadPriority(thread_handle, win32con.THREAD_PRIORITY_TIME_CRITICAL)
                elif self.priority == "high":
                    win32process.SetThreadPriority(thread_handle, win32con.THREAD_PRIORITY_HIGHEST)
                elif self.priority == "above_normal":
                    win32process.SetThreadPriority(thread_handle, win32con.THREAD_PRIORITY_ABOVE_NORMAL)
                    
            elif sys.platform == "linux":
                import os
                # Set nice priority
                if self.priority in ["highest", "high"]:
                    os.nice(-10)  # Higher priority
                    
        except ImportError:
            print(f"Warning: Could not set thread priority for {self.name}")
        except Exception as e:
            print(f"Error setting thread priority: {e}")
    
    def update_performance_stats(self, processing_time: float):
        """Update performance statistics"""
        self.performance_stats['processing_times'].append(processing_time)
        self.performance_stats['items_processed'] += 1
        
        # Keep only recent times (last 1000 items)
        if len(self.performance_stats['processing_times']) > 1000:
            self.performance_stats['processing_times'] = self.performance_stats['processing_times'][-1000:]
        
        # Calculate averages
        times = self.performance_stats['processing_times']
        self.performance_stats['avg_processing_time'] = np.mean(times)
        self.performance_stats['max_processing_time'] = np.max(times)
        
        # Calculate FPS
        self.fps_counter += 1
        current_time = time.time()
        if current_time - self.last_fps_calculation >= 1.0:
            self.performance_stats['fps'] = self.fps_counter / (current_time - self.last_fps_calculation)
            self.fps_counter = 0
            self.last_fps_calculation = current_time
    
    def run(self):
        """Main thread execution with performance optimization"""
        try:
            self.set_thread_priority()
            self.running.set()
            
            print(f"✅ {self.name} thread started with {self.priority} priority")
            
            # Main processing loop
            self.process_loop()
            
        except Exception as e:
            print(f"❌ Error in {self.name} thread: {e}")
            self.performance_stats['errors'] += 1
        finally:
            self.running.clear()
            print(f"🛑 {self.name} thread stopped")
    
    @abstractmethod
    def process_loop(self):
        """Main processing loop - implement in subclasses"""
        pass
    
    def shutdown(self):
        """Graceful shutdown"""
        print(f"🛑 Shutting down {self.name} thread...")
        self.shutdown_event.set()
        self.running.clear()
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        return {
            'thread_name': self.name,
            'priority': self.priority,
            'items_processed': self.performance_stats['items_processed'],
            'errors': self.performance_stats['errors'],
            'avg_processing_time_ms': self.performance_stats['avg_processing_time'] * 1000,
            'max_processing_time_ms': self.performance_stats['max_processing_time'] * 1000,
            'current_fps': self.performance_stats['fps'],
            'target_fps': self.target_fps,
            'efficiency': min(self.performance_stats['fps'] / self.target_fps, 1.0) * 100
        }

class PerformanceMonitor:
    """
    Real-time performance monitoring for the entire voice pipeline
    - Component latency tracking
    - Resource usage monitoring  
    - Automatic optimization triggers
    """
    def __init__(self, threads: List[HighPriorityThread]):
        self.threads = {thread.name: thread for thread in threads}
        self.monitoring = False
        self.monitor_thread = None
        
        # Performance targets (milliseconds)
        self.targets = {
            'AudioInput': 16,
            'VADProcessor': 20,
            'STTProcessor': 200,
            'AIInference': 400,
            'TTSProcessor': 100,
            'AudioOutput': 64,
            'total_pipeline': 800
        }
        
        # System metrics
        self.system_metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'gpu_usage': [],
            'total_latency': []
        }
        
    def start_monitoring(self):
        """Start real-time performance monitoring"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print("📊 Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        print("📊 Performance monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Check thread performance
                self._check_thread_performance()
                
                # Generate alerts if needed
                self._check_performance_alerts()
                
                time.sleep(1.0)  # Monitor every second
                
            except Exception as e:
                print(f"❌ Performance monitoring error: {e}")
    
    def _collect_system_metrics(self):
        """Collect system-wide performance metrics"""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=0.1)
        self.system_metrics['cpu_usage'].append(cpu_percent)
        
        # Memory usage
        memory_percent = psutil.virtual_memory().percent
        self.system_metrics['memory_usage'].append(memory_percent)
        
        # Keep only recent metrics
        for metric in self.system_metrics.values():
            if len(metric) > 60:  # Keep last 60 seconds
                metric.pop(0)
    
    def _check_thread_performance(self):
        """Check individual thread performance against targets"""
        for thread_name, thread in self.threads.items():
            if not thread.is_alive():
                continue
                
            stats = thread.get_performance_report()
            target_time = self.targets.get(thread_name, 100)
            
            if stats['avg_processing_time_ms'] > target_time:
                print(f"⚠️ {thread_name} exceeding target: {stats['avg_processing_time_ms']:.1f}ms > {target_time}ms")
    
    def _check_performance_alerts(self):
        """Check for performance issues and generate alerts"""
        # CPU usage alerts
        if len(self.system_metrics['cpu_usage']) > 0:
            avg_cpu = np.mean(self.system_metrics['cpu_usage'][-10:])  # Last 10 seconds
            if avg_cpu > 85:
                print(f"🚨 High CPU usage: {avg_cpu:.1f}%")
        
        # Memory usage alerts
        if len(self.system_metrics['memory_usage']) > 0:
            avg_memory = np.mean(self.system_metrics['memory_usage'][-10:])
            if avg_memory > 90:
                print(f"🚨 High memory usage: {avg_memory:.1f}%")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        summary = {
            'timestamp': time.time(),
            'system_metrics': {
                'avg_cpu_usage': np.mean(self.system_metrics['cpu_usage'][-10:]) if self.system_metrics['cpu_usage'] else 0,
                'avg_memory_usage': np.mean(self.system_metrics['memory_usage'][-10:]) if self.system_metrics['memory_usage'] else 0,
            },
            'thread_performance': {}
        }
        
        for thread_name, thread in self.threads.items():
            if thread.is_alive():
                summary['thread_performance'][thread_name] = thread.get_performance_report()
        
        return summary

# Utility functions for threading optimization
def optimize_system_for_realtime():
    """Apply system-level optimizations for real-time processing"""
    try:
        import os
        
        # Set process priority (Windows)
        if sys.platform == "win32":
            import win32api, win32process, win32con
            pid = os.getpid()
            handle = win32api.OpenProcess(win32con.PROCESS_ALL_ACCESS, True, pid)
            win32process.SetPriorityClass(handle, win32process.HIGH_PRIORITY_CLASS)
            print("✅ Set high process priority")
        
        # Linux optimizations
        elif sys.platform == "linux":
            os.nice(-10)  # Higher priority
            print("✅ Set high process priority")
            
    except Exception as e:
        print(f"⚠️ Could not optimize system priority: {e}")

def get_optimal_chunk_size(sample_rate: int = 48000, target_latency_ms: float = 16.0) -> int:
    """Calculate optimal audio chunk size for target latency"""
    target_latency_sec = target_latency_ms / 1000.0
    chunk_size = int(sample_rate * target_latency_sec)
    
    # Round to nearest power of 2 for efficiency
    chunk_size = 2 ** int(np.log2(chunk_size))
    
    actual_latency = (chunk_size / sample_rate) * 1000
    print(f"📊 Optimal chunk size: {chunk_size} samples ({actual_latency:.1f}ms latency)")
    
    return chunk_size

if __name__ == "__main__":
    # Test the threading infrastructure
    print("🧪 Testing Threading Infrastructure...")
    
    # Test LockFreeQueue
    test_queue = LockFreeQueue(maxsize=10, name="TestQueue")
    for i in range(15):  # Overflow test
        test_queue.put_nowait(f"item_{i}")
    
    print(f"Queue stats: {test_queue.get_stats()}")
    
    # Test LockFreeRingBuffer  
    test_buffer = LockFreeRingBuffer(sample_rate=48000, buffer_seconds=1.0)
    test_audio = np.random.random(768).astype(np.float32)
    
    test_buffer.write_chunk(test_audio)
    read_audio = test_buffer.read_latest_chunk(768)
    
    print(f"Buffer test - Original: {test_audio[:5]}")
    print(f"Buffer test - Read: {read_audio[:5]}")
    print(f"Buffer usage: {test_buffer.get_buffer_usage():.1f}%")
    
    # Test optimal chunk size calculation
    get_optimal_chunk_size(48000, 16.0)
    
    print("✅ Threading infrastructure tests completed")
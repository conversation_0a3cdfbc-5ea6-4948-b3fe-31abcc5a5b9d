"""
Threading Infrastructure
High-performance threading components for ultra-low latency voice processing
"""

import threading
import time
import queue
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from abc import ABC, abstractmethod
import psutil
import os

logger = logging.getLogger(__name__)


@dataclass
class ProcessingResult:
    """Standard result format for processing operations"""
    data: Any
    timestamp: float
    processing_time_ms: float
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LockFreeQueue:
    """High-performance lock-free queue for inter-thread communication"""
    
    def __init__(self, maxsize: int = 1000, name: str = "Queue"):
        self.queue = queue.Queue(maxsize=maxsize)
        self.name = name
        self.maxsize = maxsize
        
        # Statistics
        self.put_count = 0
        self.get_count = 0
        self.drop_count = 0
        self.total_wait_time = 0
        
        logger.debug(f"QUEUE: Created {name} (maxsize: {maxsize})")
    
    def put_nowait(self, item: Any) -> bool:
        """Put item without blocking. Returns True if successful."""
        try:
            self.queue.put_nowait(item)
            self.put_count += 1
            return True
        except queue.Full:
            self.drop_count += 1
            return False
    
    def get_nowait(self) -> Optional[Any]:
        """Get item without blocking. Returns None if empty."""
        try:
            item = self.queue.get_nowait()
            self.get_count += 1
            return item
        except queue.Empty:
            return None
    
    def put(self, item: Any, timeout: Optional[float] = None) -> bool:
        """Put item with optional timeout. Returns True if successful."""
        try:
            start_time = time.time()
            self.queue.put(item, timeout=timeout)
            self.total_wait_time += time.time() - start_time
            self.put_count += 1
            return True
        except queue.Full:
            self.drop_count += 1
            return False
    
    def get(self, timeout: Optional[float] = None) -> Optional[Any]:
        """Get item with optional timeout. Returns None if timeout."""
        try:
            start_time = time.time()
            item = self.queue.get(timeout=timeout)
            self.total_wait_time += time.time() - start_time
            self.get_count += 1
            return item
        except queue.Empty:
            return None
    
    def size(self) -> int:
        """Get current queue size"""
        return self.queue.qsize()
    
    def is_empty(self) -> bool:
        """Check if queue is empty"""
        return self.queue.empty()
    
    def is_full(self) -> bool:
        """Check if queue is full"""
        return self.queue.full()
    
    def clear(self):
        """Clear all items from queue"""
        cleared = 0
        while not self.queue.empty():
            try:
                self.queue.get_nowait()
                cleared += 1
            except queue.Empty:
                break
        if cleared > 0:
            logger.debug(f"CLEAR: Cleared {cleared} items from {self.name}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics"""
        total_operations = self.put_count + self.get_count
        drop_rate = self.drop_count / max(1, self.put_count)
        avg_wait_time = self.total_wait_time / max(1, total_operations)
        
        return {
            'name': self.name,
            'size': self.size(),
            'maxsize': self.maxsize,
            'put_count': self.put_count,
            'get_count': self.get_count,
            'drop_count': self.drop_count,
            'drop_rate': drop_rate,
            'avg_wait_time_ms': avg_wait_time * 1000,
            'utilization': self.size() / self.maxsize
        }


class HighPriorityThread(threading.Thread, ABC):
    """Base class for high-priority processing threads"""
    
    def __init__(self, name: str, priority: str = "normal", target_fps: int = 60):
        super().__init__(name=name, daemon=True)
        
        self.thread_name = name
        self.priority = priority
        self.target_fps = target_fps
        self.target_frame_time = 1.0 / target_fps if target_fps > 0 else 0.016
        
        # Thread control
        self.should_stop = False
        self.is_initialized = False
        self.initialization_error = None
        
        # Performance tracking
        self.frame_count = 0
        self.total_processing_time = 0
        self.last_fps_check = time.time()
        self.current_fps = 0
        
        # Error handling
        self.error_count = 0
        self.last_error = None
        self.last_error_time = 0
        
        logger.debug(f"THREAD: Created {name} thread (priority: {priority}, target_fps: {target_fps})")
    
    def run(self):
        """Main thread execution"""
        try:
            # Set thread priority
            self._set_thread_priority()
            
            # Initialize thread-specific components
            if not self.initialize():
                logger.error(f"FATAL: Failed to initialize {self.thread_name} thread")
                return
            
            self.is_initialized = True
            logger.info(f"START: {self.thread_name} thread initialized and running")
            
            # Main processing loop
            self.run_processing_loop()
            
        except Exception as e:
            logger.error(f"FATAL: {self.thread_name} thread crashed: {e}")
            self.last_error = str(e)
            self.last_error_time = time.time()
        finally:
            self.cleanup()
            logger.info(f"STOP: {self.thread_name} thread stopped")
    
    def _set_thread_priority(self):
        """Set thread priority based on configuration"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                priority_map = {
                    'lowest': -2,
                    'low': -1,
                    'normal': 0,
                    'high': 1,
                    'highest': 2
                }
                priority_value = priority_map.get(self.priority, 0)
                ctypes.windll.kernel32.SetThreadPriority(
                    ctypes.windll.kernel32.GetCurrentThread(),
                    priority_value
                )
            else:  # Unix-like systems
                import os
                priority_map = {
                    'lowest': 19,
                    'low': 10,
                    'normal': 0,
                    'high': -10,
                    'highest': -20
                }
                priority_value = priority_map.get(self.priority, 0)
                os.nice(priority_value)
                
            logger.debug(f"PRIORITY: Set {self.thread_name} thread priority to {self.priority}")
            
        except Exception as e:
            logger.warning(f"WARNING: Could not set thread priority for {self.thread_name}: {e}")
    
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize thread-specific components. Return True if successful."""
        pass
    
    @abstractmethod
    def run_processing_loop(self):
        """Main processing loop implementation"""
        pass
    
    def cleanup(self):
        """Cleanup thread resources"""
        pass
    
    def shutdown(self):
        """Signal thread to stop"""
        logger.debug(f"SHUTDOWN: Signaling {self.thread_name} thread to stop")
        self.should_stop = True
    
    def update_performance_stats(self, processing_time: float):
        """Update performance statistics"""
        self.frame_count += 1
        self.total_processing_time += processing_time
        
        # Calculate FPS every second
        current_time = time.time()
        if current_time - self.last_fps_check >= 1.0:
            self.current_fps = self.frame_count / (current_time - self.last_fps_check)
            self.last_fps_check = current_time
            self.frame_count = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get thread performance statistics"""
        avg_processing_time = 0
        if self.frame_count > 0:
            avg_processing_time = self.total_processing_time / self.frame_count
        
        return {
            'name': self.thread_name,
            'is_alive': self.is_alive(),
            'is_initialized': self.is_initialized,
            'current_fps': self.current_fps,
            'target_fps': self.target_fps,
            'avg_processing_time_ms': avg_processing_time * 1000,
            'error_count': self.error_count,
            'last_error': self.last_error,
            'last_error_time': self.last_error_time
        }


class PerformanceMonitor:
    """System performance monitoring"""
    
    def __init__(self):
        self.start_time = time.time()
        self.cpu_samples = []
        self.memory_samples = []
        self.last_sample_time = 0
        
    def sample_system_metrics(self):
        """Sample current system metrics"""
        try:
            current_time = time.time()
            
            # Sample every 100ms to avoid overhead
            if current_time - self.last_sample_time < 0.1:
                return
            
            cpu_percent = psutil.cpu_percent(interval=None)
            memory_info = psutil.virtual_memory()
            
            self.cpu_samples.append(cpu_percent)
            self.memory_samples.append(memory_info.percent)
            
            # Keep only recent samples (last 60 seconds)
            max_samples = 600  # 60 seconds at 100ms intervals
            if len(self.cpu_samples) > max_samples:
                self.cpu_samples = self.cpu_samples[-max_samples:]
                self.memory_samples = self.memory_samples[-max_samples:]
            
            self.last_sample_time = current_time
            
        except Exception as e:
            logger.warning(f"WARNING: Error sampling system metrics: {e}")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system performance statistics"""
        try:
            self.sample_system_metrics()
            
            avg_cpu = sum(self.cpu_samples) / len(self.cpu_samples) if self.cpu_samples else 0
            avg_memory = sum(self.memory_samples) / len(self.memory_samples) if self.memory_samples else 0
            
            return {
                'uptime_seconds': time.time() - self.start_time,
                'cpu_percent': avg_cpu,
                'memory_percent': avg_memory,
                'sample_count': len(self.cpu_samples)
            }
            
        except Exception as e:
            logger.error(f"ERROR: Error getting system stats: {e}")
            return {
                'uptime_seconds': time.time() - self.start_time,
                'cpu_percent': 0,
                'memory_percent': 0,
                'sample_count': 0
            }


def optimize_system_for_realtime():
    """Optimize system settings for real-time audio processing"""
    try:
        # Set process priority
        process = psutil.Process()
        if os.name == 'nt':  # Windows
            process.nice(psutil.HIGH_PRIORITY_CLASS)
        else:  # Unix-like
            process.nice(-10)
        
        logger.info("OPTIMIZE: System optimized for real-time processing")
        return True
        
    except Exception as e:
        logger.warning(f"WARNING: Could not optimize system for real-time: {e}")
        return False

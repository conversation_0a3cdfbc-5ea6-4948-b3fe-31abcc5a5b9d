"""
AI Inference Processor
Dynamic model routing and response generation with <400ms latency
"""

import asyncio
import time
import threading
from typing import Optional, Dict, List, Any, Tuple
import logging
import hashlib
import json
from collections import deque, OrderedDict
from dataclasses import dataclass, asdict

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    print(" HTTPX not available. Install with: pip install httpx")

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)
from .circuit_breaker import circuit_breaker_manager, CircuitBreakerConfig, CircuitBreakerError

logger = logging.getLogger(__name__)

@dataclass
class ConversationContext:
    """Conversation context for maintaining dialogue flow"""
    exchanges: List[Dict[str, str]]
    total_exchanges: int
    user_preferences: Dict[str, Any]
    conversation_topics: List[str]
    personality_state: Dict[str, Any]
    last_update: float

@dataclass
class ModelPerformance:
    """Track performance metrics for each model"""
    model_name: str
    avg_response_time: float
    success_rate: float
    total_requests: int
    recent_response_times: deque
    error_count: int
    last_used: float

class AIInferenceThread(HighPriorityThread):
    """
    AI Inference processor with dynamic model routing
    - Multi-model support with intelligent routing
    - Response caching for performance
    - Context-aware conversation management
    - Ollama integration for your 22+ models
    - Performance optimization and fallback handling
    """
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 ollama_host: str = "localhost",
                 ollama_port: int = 11434,
                 max_response_tokens: int = 150,  # Optimized for voice
                 temperature: float = 0.8,
                 context_memory_size: int = 50):
        
        super().__init__("AIInference", priority="normal", target_fps=50)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # Ollama configuration
        self.ollama_host = ollama_host
        self.ollama_port = ollama_port
        self.ollama_url = f"http://{ollama_host}:{ollama_port}"
        
        # Generation parameters
        self.max_response_tokens = max_response_tokens
        self.temperature = temperature
        self.context_memory_size = context_memory_size
        
        # HTTP client for Ollama
        self.ollama_client = None
        
        # Model management
        self.available_models = {}
        self.model_performance = {}
        self.preferred_models = []  # Ordered by preference
        
        # Response caching
        self.response_cache = OrderedDict()
        self.cache_size_limit = 1000
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Context management
        self.conversation_context = ConversationContext(
            exchanges=[],
            total_exchanges=0,
            user_preferences={},
            conversation_topics=[],
            personality_state={'mode': 'helpful', 'enthusiasm': 0.8},
            last_update=time.time()
        )
        
        # Model router
        self.model_router = ModelRouter()
        
        # Performance tracking
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.avg_response_time = 0.0
        
        # AI personality configuration
        self.personality_config = {
            'name': 'Josie',
            'style': 'friendly, helpful, conversational',
            'response_length': 'concise but natural (under 30 words for voice)',
            'tone': 'warm and engaging',
            'expertise': 'general assistance with technical knowledge'
        }
    
    def initialize_ollama_connection(self) -> bool:
        """Initialize connection to Ollama server"""
        if not HTTPX_AVAILABLE:
            logger.error(" HTTPX not available - cannot connect to Ollama")
            return False

        try:
            # Create async HTTP client
            self.ollama_client = httpx.AsyncClient(
                base_url=self.ollama_url,
                timeout=httpx.Timeout(30.0),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )

            # Initialize circuit breaker for Ollama
            breaker_config = CircuitBreakerConfig(
                failure_threshold=3,      # Open after 3 failures
                recovery_timeout=15.0,    # Try recovery after 15 seconds
                success_threshold=2,      # Close after 2 successes
                timeout=10.0             # 10 second timeout
            )
            self.ollama_breaker = circuit_breaker_manager.get_breaker("ollama", breaker_config)

            # Test connection and get available models with circuit breaker
            try:
                connection_test = self.ollama_breaker.call(lambda: asyncio.run(self._test_connection()))
                if connection_test:
                    self.ollama_breaker.call(lambda: asyncio.run(self._load_available_models()))
                    self._initialize_model_preferences()
                    logger.info(f"Connected to Ollama at {self.ollama_url}")
                    logger.info(f"Available models: {len(self.available_models)}")
                    return True
                else:
                    logger.error(f"ERROR: Failed to connect to Ollama at {self.ollama_url}")
                    return False
            except CircuitBreakerError:
                logger.error("ERROR: Ollama circuit breaker is open - service unavailable")
                return False

        except Exception as e:
            logger.error(f"ERROR: Ollama initialization error: {e}")
            return False
    
    async def _test_connection(self) -> bool:
        """Test connection to Ollama server"""
        try:
            response = await self.ollama_client.get("/api/tags")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"ERROR: Ollama connection test failed: {e}")
            return False
    
    async def _load_available_models(self) -> bool:
        """Load list of available models from Ollama"""
        try:
            response = await self.ollama_client.get("/api/tags")
            if response.status_code == 200:
                data = response.json()
                models = data.get("models", [])
                
                for model in models:
                    model_name = model["name"]
                    model_info = {
                        'name': model_name,
                        'size': model.get('size', 0),
                        'modified_at': model.get('modified_at', ''),
                        'details': model.get('details', {}),
                        'capabilities': self._analyze_model_capabilities(model_name)
                    }
                    
                    self.available_models[model_name] = model_info
                    
                    # Initialize performance tracking
                    self.model_performance[model_name] = ModelPerformance(
                        model_name=model_name,
                        avg_response_time=0.0,
                        success_rate=1.0,
                        total_requests=0,
                        recent_response_times=deque(maxlen=100),
                        error_count=0,
                        last_used=0.0
                    )
                
                logger.info(f"LOADED: Loaded {len(self.available_models)} models from Ollama")
                return True
            else:
                logger.error(f"ERROR: Failed to get models: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False
    
    def _analyze_model_capabilities(self, model_name: str) -> Dict[str, Any]:
        """Analyze model capabilities based on name and size"""
        name_lower = model_name.lower()
        
        capabilities = {
            'vision': 'vl' in name_lower or 'vision' in name_lower,
            'reasoning': any(term in name_lower for term in ['reasoning', 'o1', 'deep', 'phi4']),
            'conversation': any(term in name_lower for term in ['chat', 'hermes', 'josie']),
            'code': any(term in name_lower for term in ['code', 'granite', 'deepseek']),
            'speed_tier': self._determine_speed_tier(model_name),
            'complexity_rating': self._determine_complexity_rating(model_name)
        }
        
        return capabilities
    
    def _determine_speed_tier(self, model_name: str) -> str:
        """Determine speed tier based on model characteristics"""
        name_lower = model_name.lower()
        
        # Extract parameter count if available
        if any(size in name_lower for size in ['32b', '35b']):
            return 'slow'
        elif any(size in name_lower for size in ['24b', '27b']):
            return 'medium'
        elif any(size in name_lower for size in ['14b', '8b', '7b']):
            return 'fast'
        elif any(size in name_lower for size in ['4b', '3b', '2b']):
            return 'very_fast'
        else:
            return 'medium'  # Default
    
    def _determine_complexity_rating(self, model_name: str) -> float:
        """Determine complexity rating (0.0-1.0) for model routing"""
        name_lower = model_name.lower()
        
        # Higher rating = more capable for complex tasks
        if any(term in name_lower for term in ['32b', '35b', 'deep', 'reasoning']):
            return 0.9
        elif any(term in name_lower for term in ['24b', '27b', 'qwen']):
            return 0.7
        elif any(term in name_lower for term in ['14b', '8b']):
            return 0.5
        elif any(term in name_lower for term in ['4b', 'mini']):
            return 0.3
        else:
            return 0.6  # Default
    
    def _initialize_model_preferences(self):
        """Initialize model preferences based on your collection"""
        # Order models by preference for different use cases
        model_names = list(self.available_models.keys())
        
        # Primary preferences (based on your documented collection)
        primary_models = [
            'qwen2.5vl:32b',  # Vision + high quality
            'qwen2.5vl:latest',
            'exaone-deep:32b',  # Reasoning
            'magistral:24b',    # Balanced
            'hermes3:8b',       # Fast conversation
            'llama3.2:latest'   # Very fast
        ]
        
        # Add available primary models
        self.preferred_models = [m for m in primary_models if m in model_names]
        
        # Add remaining models sorted by complexity
        remaining_models = [m for m in model_names if m not in self.preferred_models]
        remaining_models.sort(key=lambda x: self.available_models[x]['capabilities']['complexity_rating'], reverse=True)
        
        self.preferred_models.extend(remaining_models)
        
        logger.info(f"Model preference order: {self.preferred_models[:5]}...")
    
    def process_loop(self):
        """Main AI inference processing loop"""
        if not self.initialize_ollama_connection():
            logger.error("ERROR: Failed to initialize Ollama connection")
            return

        logger.info("AI inference processor started")

        try:
            # Use asyncio for the main loop to handle async Ollama calls
            asyncio.run(self._async_process_loop())
        except Exception as e:
            logger.error(f"ERROR: AI inference error: {e}")
        finally:
            if self.ollama_client:
                asyncio.run(self.ollama_client.aclose())
            logger.info("STOP: AI inference processor stopped")
    
    async def _async_process_loop(self):
        """Async processing loop for handling requests"""
        while self.running.is_set() and not self.shutdown_event.is_set():
            await self._process_text_input()
    
    async def _process_text_input(self):
        """Process text input from STT"""
        # Get text input from STT
        stt_result = self.input_queue.get_nowait()
        if stt_result is None:
            await asyncio.sleep(0.01)  # 10ms sleep when no data
            return
        
        start_time = time.time()
        
        try:
            # Extract transcription data
            transcription_data = stt_result.data
            if not isinstance(transcription_data, dict) or 'text' not in transcription_data:
                logger.warning("WARNING: Invalid transcription data from STT")
                return

            user_text = transcription_data['text'].strip()
            if not user_text:
                logger.debug("WARNING: Empty text from STT")
                return

            logger.debug(f"PROCESS: Processing: '{user_text[:50]}...'")

            # Check response cache first
            cache_key = self._generate_cache_key(user_text)
            cached_response = self._get_cached_response(cache_key)
            
            if cached_response:
                ai_response = cached_response
                response_source = "cache"
                self.cache_hits += 1
            else:
                # Generate new response
                ai_response = await self._generate_ai_response(user_text)
                response_source = "generated"
                self.cache_misses += 1
                
                # Cache the response
                if ai_response:
                    self._cache_response(cache_key, ai_response)
            
            if ai_response:
                # Update conversation context
                self._update_conversation_context(user_text, ai_response)
                
                # Send to TTS
                result = ProcessingResult(
                    data=ai_response,
                    timestamp=stt_result.timestamp,
                    latency=time.time() - start_time,
                    confidence=ai_response.get('confidence', 0.9),
                    stage="AI",
                    chunk_id=stt_result.chunk_id
                )
                
                if not self.output_queue.put_nowait(result):
                    logger.warning("WARNING: AI output queue full, dropping response")
                    self.failed_requests += 1
                else:
                    self.successful_requests += 1
                    logger.debug(f"OUTPUT: AI response ({response_source}): '{ai_response['text'][:50]}...'")
            else:
                self.failed_requests += 1
            
            self.total_requests += 1
            
            # Performance tracking
            processing_time = time.time() - start_time
            self.update_performance_stats(processing_time)
            self.avg_response_time = (self.avg_response_time * (self.total_requests - 1) + processing_time) / self.total_requests
            
        except Exception as e:
            logger.error(f"ERROR: Error processing text input: {e}")
            self.failed_requests += 1
    
    async def _generate_ai_response(self, user_text: str) -> Optional[Dict[str, Any]]:
        """Generate AI response using optimal model"""
        try:
            # Select best model for this request
            selected_model = self.model_router.select_model(
                user_text, 
                self.available_models, 
                self.model_performance,
                self.preferred_models
            )
            
            if not selected_model:
                logger.error("ERROR: No suitable model available")
                return None

            logger.debug(f"MODEL: Using model: {selected_model}")

            # Prepare prompt with context
            prompt = self._build_prompt(user_text)

            # Generate response with circuit breaker protection
            try:
                response_text = self.ollama_breaker.call(
                    lambda: asyncio.run(self._call_ollama_generate(selected_model, prompt))
                )
            except CircuitBreakerError:
                logger.warning("WARNING: Ollama circuit breaker open - using fallback response")
                return self._get_fallback_response(user_text)

            if response_text:
                # Process and clean response
                cleaned_response = self._clean_response(response_text)

                response_data = {
                    'text': cleaned_response,
                    'model': selected_model,
                    'confidence': 0.9,  # Default confidence
                    'timestamp': time.time(),
                    'tokens': len(cleaned_response.split()),
                    'context_used': len(self.conversation_context.exchanges)
                }

                # Update model performance
                self._update_model_performance(selected_model, True, time.time())

                return response_data
            else:
                self._update_model_performance(selected_model, False, time.time())
                return None
                
        except Exception as e:
            logger.error(f"ERROR: AI response generation error: {e}")
            return None

    def _get_fallback_response(self, user_text: str) -> Dict[str, Any]:
        """Generate fallback response when AI service is unavailable"""
        fallback_responses = [
            "I'm experiencing some technical difficulties right now. Could you please repeat that?",
            "Sorry, I'm having trouble processing that request at the moment. Can you try again?",
            "I'm temporarily unavailable. Please give me a moment and try again.",
            "There seems to be a connection issue. Let me try to help you in a moment.",
            "I'm working on resolving a technical issue. Please be patient with me."
        ]

        # Simple hash-based selection for consistency
        response_index = hash(user_text.lower()) % len(fallback_responses)
        fallback_text = fallback_responses[response_index]

        logger.info(f"FALLBACK: Using fallback response: '{fallback_text}'")

        return {
            'text': fallback_text,
            'model': 'fallback',
            'confidence': 0.5,  # Lower confidence for fallback
            'timestamp': time.time(),
            'tokens': len(fallback_text.split()),
            'context_used': 0,
            'is_fallback': True
        }

    async def _call_ollama_generate(self, model_name: str, prompt: str) -> Optional[str]:
        """Call Ollama API to generate response"""
        try:
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_response_tokens,
                    "top_k": 30,
                    "top_p": 0.9,
                    "repeat_penalty": 1.1,
                    "stop": ["\n\nUser:", "\n\nHuman:", "User:", "Human:"]
                }
            }
            
            response = await self.ollama_client.post("/api/generate", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                return data.get("response", "").strip()
            else:
                logger.error(f"ERROR: Ollama API error: HTTP {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"ERROR: Ollama API call error: {e}")
            return None
    
    def _build_prompt(self, user_text: str) -> str:
        """Build prompt with conversation context and personality"""
        # System prompt with personality
        system_prompt = f"""You are {self.personality_config['name']}, a {self.personality_config['style']} AI assistant.

Your responses should be:
- {self.personality_config['response_length']}
- {self.personality_config['tone']}
- Natural and conversational for voice interaction

Keep responses under 30 words when possible for natural speech flow."""
        
        # Add conversation context
        context_messages = []
        recent_exchanges = self.conversation_context.exchanges[-6:]  # Last 6 exchanges
        
        for exchange in recent_exchanges:
            context_messages.append(f"User: {exchange['user']}")
            context_messages.append(f"Josie: {exchange['assistant']}")
        
        context_str = "\n".join(context_messages) if context_messages else ""
        
        # Build full prompt
        if context_str:
            full_prompt = f"""{system_prompt}

Recent conversation:
{context_str}

User: {user_text}

Josie:"""
        else:
            full_prompt = f"""{system_prompt}

User: {user_text}

Josie:"""
        
        return full_prompt
    
    def _clean_response(self, response_text: str) -> str:
        """Clean and optimize response for voice output"""
        # Remove common artifacts
        cleaned = response_text.strip()
        
        # Remove role indicators
        for indicator in ["Josie:", "Assistant:", "AI:", "Bot:"]:
            if cleaned.startswith(indicator):
                cleaned = cleaned[len(indicator):].strip()
        
        # Remove trailing punctuation repetition
        while cleaned.endswith(("...", "!!", "??")):
            cleaned = cleaned[:-1]
        
        # Ensure proper sentence ending
        if cleaned and not cleaned.endswith(('.', '!', '?', ':')):
            cleaned += '.'
        
        # Limit length for voice (approximately 30 words max)
        words = cleaned.split()
        if len(words) > 35:
            cleaned = ' '.join(words[:30]) + '...'
        
        return cleaned
    
    def _generate_cache_key(self, user_text: str) -> str:
        """Generate cache key for user input"""
        # Include recent context for cache key
        context_hash = ""
        if self.conversation_context.exchanges:
            recent_context = str(self.conversation_context.exchanges[-2:])
            context_hash = hashlib.md5(recent_context.encode()).hexdigest()[:8]
        
        text_hash = hashlib.md5(user_text.lower().encode()).hexdigest()[:16]
        return f"{text_hash}_{context_hash}"
    
    def _get_cached_response(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached response if available"""
        if cache_key in self.response_cache:
            # Move to end (LRU)
            cached_response = self.response_cache.pop(cache_key)
            self.response_cache[cache_key] = cached_response
            return cached_response
        return None
    
    def _cache_response(self, cache_key: str, response: Dict[str, Any]):
        """Cache response with LRU eviction"""
        # Add timestamp for cache validity
        response_copy = response.copy()
        response_copy['cached_at'] = time.time()
        
        self.response_cache[cache_key] = response_copy
        
        # LRU eviction
        while len(self.response_cache) > self.cache_size_limit:
            self.response_cache.popitem(last=False)
    
    def _update_conversation_context(self, user_text: str, ai_response: Dict[str, Any]):
        """Update conversation context with new exchange"""
        exchange = {
            'user': user_text,
            'assistant': ai_response['text'],
            'timestamp': time.time(),
            'model': ai_response.get('model', 'unknown')
        }
        
        self.conversation_context.exchanges.append(exchange)
        self.conversation_context.total_exchanges += 1
        self.conversation_context.last_update = time.time()
        
        # Trim context to memory limit
        if len(self.conversation_context.exchanges) > self.context_memory_size:
            self.conversation_context.exchanges = self.conversation_context.exchanges[-self.context_memory_size:]
        
        # Update conversation topics (simple keyword extraction)
        self._update_conversation_topics(user_text)
    
    def _update_conversation_topics(self, user_text: str):
        """Update conversation topics based on user input"""
        # Simple topic detection (could be enhanced with NLP)
        potential_topics = []
        
        topic_keywords = {
            'weather': ['weather', 'rain', 'sunny', 'cloud', 'temperature'],
            'technology': ['computer', 'software', 'AI', 'programming', 'tech'],
            'work': ['job', 'work', 'office', 'meeting', 'project'],
            'health': ['health', 'exercise', 'diet', 'medical', 'doctor'],
            'entertainment': ['movie', 'music', 'game', 'book', 'show']
        }
        
        user_words = user_text.lower().split()
        for topic, keywords in topic_keywords.items():
            if any(keyword in user_words for keyword in keywords):
                potential_topics.append(topic)
        
        # Add new topics
        for topic in potential_topics:
            if topic not in self.conversation_context.conversation_topics:
                self.conversation_context.conversation_topics.append(topic)
        
        # Keep only recent topics
        if len(self.conversation_context.conversation_topics) > 10:
            self.conversation_context.conversation_topics = self.conversation_context.conversation_topics[-10:]
    
    def _update_model_performance(self, model_name: str, success: bool, response_time: float):
        """Update model performance metrics"""
        if model_name in self.model_performance:
            perf = self.model_performance[model_name]
            
            perf.total_requests += 1
            perf.last_used = time.time()
            
            if success:
                perf.recent_response_times.append(response_time)
                perf.avg_response_time = sum(perf.recent_response_times) / len(perf.recent_response_times)
                perf.success_rate = (perf.success_rate * (perf.total_requests - 1) + 1.0) / perf.total_requests
            else:
                perf.error_count += 1
                perf.success_rate = (perf.success_rate * (perf.total_requests - 1) + 0.0) / perf.total_requests
    
    def get_ai_stats(self) -> Dict[str, Any]:
        """Get detailed AI inference statistics"""
        stats = self.get_performance_report()
        
        # Calculate cache hit rate
        total_cache_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = (self.cache_hits / total_cache_requests * 100) if total_cache_requests > 0 else 0
        
        # Model performance summary
        model_stats = {}
        for model_name, perf in self.model_performance.items():
            model_stats[model_name] = {
                'avg_response_time': perf.avg_response_time,
                'success_rate': perf.success_rate * 100,
                'total_requests': perf.total_requests,
                'last_used': perf.last_used
            }
        
        stats.update({
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'success_rate': (self.successful_requests / max(self.total_requests, 1)) * 100,
            'avg_response_time': self.avg_response_time,
            'cache_hit_rate': cache_hit_rate,
            'cache_size': len(self.response_cache),
            'available_models': len(self.available_models),
            'conversation_exchanges': len(self.conversation_context.exchanges),
            'conversation_topics': self.conversation_context.conversation_topics,
            'model_performance': model_stats,
            'queue_input_stats': self.input_queue.get_stats(),
            'queue_output_stats': self.output_queue.get_stats()
        })
        
        return stats

class ModelRouter:
    """
    Intelligent model routing based on query complexity and performance
    """
    
    def __init__(self):
        self.complexity_analyzers = [
            self._analyze_length_complexity,
            self._analyze_question_complexity,
            self._analyze_technical_complexity,
            self._analyze_reasoning_complexity
        ]
    
    def select_model(self, 
                    user_text: str, 
                    available_models: Dict, 
                    model_performance: Dict,
                    preferred_models: List[str]) -> Optional[str]:
        """Select optimal model for the given input"""
        
        # Analyze query complexity
        complexity_score = self._analyze_query_complexity(user_text)
        
        # Filter available models by preference
        candidate_models = [m for m in preferred_models if m in available_models]
        
        if not candidate_models:
            return None
        
        # Score models based on complexity needs and performance
        model_scores = {}
        
        for model_name in candidate_models:
            model_info = available_models[model_name]
            perf_info = model_performance.get(model_name)
            
            score = self._calculate_model_score(
                model_info, 
                perf_info, 
                complexity_score
            )
            
            model_scores[model_name] = score
        
        # Select highest scoring model
        best_model = max(model_scores.items(), key=lambda x: x[1])
        return best_model[0]
    
    def _analyze_query_complexity(self, user_text: str) -> float:
        """Analyze query complexity (0.0 = simple, 1.0 = complex)"""
        complexity_scores = []
        
        for analyzer in self.complexity_analyzers:
            score = analyzer(user_text)
            complexity_scores.append(score)
        
        # Weighted average
        weights = [0.2, 0.3, 0.3, 0.2]  # Length, questions, technical, reasoning
        weighted_score = sum(score * weight for score, weight in zip(complexity_scores, weights))
        
        return min(max(weighted_score, 0.0), 1.0)
    
    def _analyze_length_complexity(self, text: str) -> float:
        """Complexity based on text length"""
        word_count = len(text.split())
        if word_count <= 5:
            return 0.1
        elif word_count <= 15:
            return 0.3
        elif word_count <= 30:
            return 0.6
        else:
            return 1.0
    
    def _analyze_question_complexity(self, text: str) -> float:
        """Complexity based on question types"""
        question_indicators = text.count('?')
        
        complex_question_words = [
            'why', 'how', 'explain', 'analyze', 'compare', 
            'evaluate', 'discuss', 'elaborate'
        ]
        
        complex_count = sum(1 for word in complex_question_words if word in text.lower())
        
        if question_indicators == 0 and complex_count == 0:
            return 0.2
        elif question_indicators == 1 and complex_count <= 1:
            return 0.5
        else:
            return 0.8
    
    def _analyze_technical_complexity(self, text: str) -> float:
        """Complexity based on technical content"""
        technical_terms = [
            'algorithm', 'programming', 'code', 'software', 'technical',
            'engineering', 'mathematics', 'science', 'analysis', 'system'
        ]
        
        technical_count = sum(1 for term in technical_terms if term in text.lower())
        
        if technical_count == 0:
            return 0.1
        elif technical_count <= 2:
            return 0.5
        else:
            return 0.9
    
    def _analyze_reasoning_complexity(self, text: str) -> float:
        """Complexity based on reasoning requirements"""
        reasoning_indicators = [
            'because', 'therefore', 'however', 'although', 'since',
            'if', 'then', 'else', 'cause', 'effect', 'reason'
        ]
        
        reasoning_count = sum(1 for indicator in reasoning_indicators if indicator in text.lower())
        
        if reasoning_count == 0:
            return 0.2
        elif reasoning_count <= 2:
            return 0.6
        else:
            return 1.0
    
    def _calculate_model_score(self, 
                              model_info: Dict, 
                              perf_info: Optional[ModelPerformance], 
                              complexity_score: float) -> float:
        """Calculate overall model score for selection"""
        
        # Base score from model capabilities
        capabilities = model_info.get('capabilities', {})
        complexity_rating = capabilities.get('complexity_rating', 0.5)
        
        # Match complexity needs
        complexity_match = 1.0 - abs(complexity_score - complexity_rating)
        
        # Performance score
        if perf_info and perf_info.total_requests > 0:
            perf_score = perf_info.success_rate * (1.0 / max(perf_info.avg_response_time, 0.1))
        else:
            perf_score = 0.5  # Default for untested models
        
        # Speed preference (favor faster models for simple queries)
        speed_tier = capabilities.get('speed_tier', 'medium')
        speed_bonus = {
            'very_fast': 0.3,
            'fast': 0.2,
            'medium': 0.1,
            'slow': 0.0
        }.get(speed_tier, 0.1)
        
        # Apply speed bonus inversely to complexity
        speed_bonus *= (1.0 - complexity_score)
        
        # Final score
        final_score = (complexity_match * 0.5 + 
                      perf_score * 0.3 + 
                      speed_bonus * 0.2)
        
        return final_score

# Test function
def test_ai_inference():
    """Test AI inference functionality"""
    print("TEST: Testing AI Inference...")

    if not HTTPX_AVAILABLE:
        print("ERROR: HTTPX not available - cannot test AI inference")
        return
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="AIInput")
    output_queue = LockFreeQueue(maxsize=100, name="AIOutput")
    
    # Create AI inference processor
    ai_processor = AIInferenceThread(
        input_queue=input_queue,
        output_queue=output_queue,
        ollama_host="localhost",
        ollama_port=11434
    )
    
    try:
        # Start AI processor
        ai_processor.start()
        
        # Wait for initialization
        time.sleep(3)
        
        # Test queries
        test_queries = [
            "Hello, how are you?",
            "What's the weather like?",
            "Explain quantum computing",
            "Tell me a joke"
        ]
        
        print("AI: Sending test queries...")
        
        for i, query in enumerate(test_queries):
            # Create mock transcription result
            transcription = {
                'text': query,
                'confidence': 0.95,
                'language': 'en',
                'duration': 2.0,
                'processing_time': 0.1
            }
            
            stt_result = ProcessingResult(
                data=transcription,
                timestamp=time.time(),
                latency=0.1,
                confidence=0.95,
                stage="STT",
                chunk_id=i
            )
            
            input_queue.put_nowait(stt_result)
            print(f"   Sent: '{query}'")
        
        # Wait for processing
        time.sleep(5)
        
        # Check results
        responses = 0
        while True:
            result = output_queue.get_nowait()
            if result is None:
                break
            responses += 1
            response_text = result.data.get('text', 'No text')
            model_used = result.data.get('model', 'Unknown')
            print(f"   Response {responses}: '{response_text}' (model: {model_used})")
        
        print(f"SUCCESS: AI test completed: {responses} responses generated")
        print(f"STATS: AI stats: {ai_processor.get_ai_stats()}")
        
    finally:
        ai_processor.shutdown()
        ai_processor.join(timeout=5.0)

if __name__ == "__main__":
    test_ai_inference()
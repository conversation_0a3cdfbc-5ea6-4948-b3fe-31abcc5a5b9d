"""
AI Inference Thread
High-performance AI inference with multiple engine support
"""

import time
import logging
from typing import Optional, Dict, Any, List
import threading
import json
from collections import deque

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)
from .circuit_breaker import CircuitBreaker

logger = logging.getLogger(__name__)


class AIInferenceThread(HighPriorityThread):
    """AI inference processing thread"""
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 ai_engine: str = "ollama",
                 model_name: str = "llama2",
                 ollama_host: str = "localhost",
                 ollama_port: int = 11434,
                 max_tokens: int = 150,
                 temperature: float = 0.9,
                 context_memory_size: int = 20):
        
        super().__init__("AIInference", priority="high", target_fps=5)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # AI configuration
        self.ai_engine = ai_engine
        self.model_name = model_name
        self.ollama_host = ollama_host
        self.ollama_port = ollama_port
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.context_memory_size = context_memory_size
        
        # Ollama connection
        self.ollama_url = f"http://{ollama_host}:{ollama_port}"
        self.session = None
        
        # Conversation context
        self.conversation_history = deque(maxlen=context_memory_size)
        self.system_prompt = self._get_default_system_prompt()
        
        # Circuit breaker for fault tolerance
        self.circuit_breaker = CircuitBreaker(
            name="ai_inference",
            failure_threshold=3,
            recovery_timeout=15.0,
            success_threshold=2
        )
        
        # Performance tracking
        self.inferences_processed = 0
        self.total_inference_time = 0
        self.failed_inferences = 0
        self.total_tokens_generated = 0
        
        logger.info(f"AI: Initialized (engine: {ai_engine}, model: {model_name})")
    
    def initialize(self) -> bool:
        """Initialize AI inference system"""
        try:
            if not REQUESTS_AVAILABLE:
                logger.error("FATAL: requests library not available")
                return False
            
            # Initialize based on AI engine
            if self.ai_engine == "ollama":
                return self._initialize_ollama()
            else:
                logger.error(f"ERROR: Unsupported AI engine: {self.ai_engine}")
                return False
                
        except Exception as e:
            logger.error(f"FATAL: AI inference initialization failed: {e}")
            return False
    
    def _initialize_ollama(self) -> bool:
        """Initialize Ollama connection"""
        try:
            # Create session for connection pooling
            self.session = requests.Session()
            self.session.timeout = 30
            
            # Test connection
            response = self.session.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                logger.info(f"AI: Connected to Ollama at {self.ollama_url}")
                logger.info(f"AI: Available models: {model_names}")
                
                # Check if our model is available
                if self.model_name not in model_names:
                    logger.warning(f"WARNING: Model '{self.model_name}' not found in Ollama")
                    logger.warning(f"Available models: {model_names}")
                    
                    # Try to use first available model
                    if model_names:
                        self.model_name = model_names[0]
                        logger.info(f"AI: Using fallback model: {self.model_name}")
                    else:
                        logger.error("ERROR: No models available in Ollama")
                        return False
                
                return True
            else:
                logger.error(f"ERROR: Ollama connection failed: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            logger.error(f"ERROR: Cannot connect to Ollama at {self.ollama_url}")
            logger.error("Make sure Ollama is running: ollama serve")
            return False
        except Exception as e:
            logger.error(f"ERROR: Ollama initialization failed: {e}")
            return False
    
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt for the AI"""
        return """You are a helpful AI voice assistant. Provide concise, natural responses suitable for voice conversation. Keep responses brief and conversational. Avoid long explanations unless specifically asked."""
    
    def run_processing_loop(self):
        """Main AI inference processing loop"""
        logger.info("AI: Processing loop started")
        
        while not self.should_stop:
            try:
                # Get transcribed text from STT
                stt_result = self.input_queue.get_nowait()
                if stt_result is None:
                    time.sleep(0.01)  # Brief pause if no data
                    continue
                
                # Process text for AI inference
                start_time = time.time()
                ai_result = self._process_text_input(stt_result)
                processing_time = time.time() - start_time
                
                # Send result if inference successful
                if ai_result:
                    success = self.output_queue.put_nowait(ai_result)
                    if not success:
                        logger.warning("WARNING: AI output queue full")
                
                # Update performance stats
                self.update_performance_stats(processing_time)
                
            except Exception as e:
                logger.error(f"ERROR: AI processing error: {e}")
                time.sleep(0.01)
    
    def _process_text_input(self, stt_result: ProcessingResult) -> Optional[ProcessingResult]:
        """Process text input for AI inference"""
        try:
            if not self.circuit_breaker.can_execute():
                logger.warning("AI: Circuit breaker open - skipping inference")
                return None
            
            user_text = stt_result.data['text']
            confidence = stt_result.data.get('confidence', 1.0)
            
            # Skip low-quality transcriptions
            if confidence < 0.5:
                logger.debug(f"AI: Skipping low confidence input: '{user_text}' ({confidence:.3f})")
                return None
            
            # Generate AI response
            start_time = time.time()
            response_text, token_count = self._generate_response(user_text)
            inference_time = time.time() - start_time
            
            if response_text:
                # Update conversation history
                self.conversation_history.append({
                    'role': 'user',
                    'content': user_text,
                    'timestamp': time.time()
                })
                self.conversation_history.append({
                    'role': 'assistant',
                    'content': response_text,
                    'timestamp': time.time()
                })
                
                # Create result
                result = ProcessingResult(
                    data={
                        'text': response_text,
                        'user_input': user_text,
                        'user_confidence': confidence,
                        'token_count': token_count,
                        'inference_time': inference_time,
                        'model_name': self.model_name,
                        'ai_engine': self.ai_engine
                    },
                    timestamp=time.time(),
                    processing_time_ms=inference_time * 1000,
                    metadata={
                        'source': 'ai_inference',
                        'conversation_length': len(self.conversation_history)
                    }
                )
                
                # Update stats
                self.inferences_processed += 1
                self.total_inference_time += inference_time
                self.total_tokens_generated += token_count
                self.circuit_breaker.record_success()
                
                logger.info(f"AI: '{user_text}' -> '{response_text}' ({inference_time:.2f}s, {token_count} tokens)")
                return result
            else:
                self.failed_inferences += 1
                self.circuit_breaker.record_failure()
                return None
                
        except Exception as e:
            logger.error(f"ERROR: AI text processing failed: {e}")
            self.failed_inferences += 1
            self.circuit_breaker.record_failure()
            return None
    
    def _generate_response(self, user_text: str) -> tuple[str, int]:
        """Generate AI response using configured engine"""
        try:
            if self.ai_engine == "ollama":
                return self._generate_ollama_response(user_text)
            else:
                logger.error(f"ERROR: Unsupported AI engine: {self.ai_engine}")
                return "", 0
                
        except Exception as e:
            logger.error(f"ERROR: Response generation failed: {e}")
            return "", 0
    
    def _generate_ollama_response(self, user_text: str) -> tuple[str, int]:
        """Generate response using Ollama"""
        try:
            # Build conversation context
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add recent conversation history
            for entry in list(self.conversation_history)[-10:]:  # Last 10 messages
                messages.append({
                    "role": entry['role'],
                    "content": entry['content']
                })
            
            # Add current user message
            messages.append({"role": "user", "content": user_text})
            
            # Prepare request
            request_data = {
                "model": self.model_name,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            # Make request
            response = self.session.post(
                f"{self.ollama_url}/api/chat",
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('message', {}).get('content', '').strip()
                
                # Estimate token count (rough approximation)
                token_count = len(response_text.split()) * 1.3  # Approximate tokens
                
                return response_text, int(token_count)
            else:
                logger.error(f"ERROR: Ollama request failed: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return "", 0
                
        except requests.exceptions.Timeout:
            logger.error("ERROR: Ollama request timeout")
            return "", 0
        except requests.exceptions.ConnectionError:
            logger.error("ERROR: Ollama connection error")
            return "", 0
        except Exception as e:
            logger.error(f"ERROR: Ollama response generation failed: {e}")
            return "", 0
    
    def get_ai_stats(self) -> Dict[str, Any]:
        """Get AI inference statistics"""
        avg_inference_time = 0
        avg_tokens_per_response = 0
        tokens_per_second = 0
        
        if self.inferences_processed > 0:
            avg_inference_time = self.total_inference_time / self.inferences_processed
            avg_tokens_per_response = self.total_tokens_generated / self.inferences_processed
            
            if self.total_inference_time > 0:
                tokens_per_second = self.total_tokens_generated / self.total_inference_time
        
        return {
            'ai_engine': self.ai_engine,
            'model_name': self.model_name,
            'inferences_processed': self.inferences_processed,
            'failed_inferences': self.failed_inferences,
            'success_rate': self.inferences_processed / max(1, self.inferences_processed + self.failed_inferences),
            'avg_inference_time': avg_inference_time,
            'avg_tokens_per_response': avg_tokens_per_response,
            'tokens_per_second': tokens_per_second,
            'total_tokens_generated': self.total_tokens_generated,
            'conversation_length': len(self.conversation_history),
            'circuit_breaker_state': self.circuit_breaker.state.value,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens
        }
    
    def update_system_prompt(self, new_prompt: str):
        """Update system prompt"""
        self.system_prompt = new_prompt
        logger.info("AI: System prompt updated")
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history.clear()
        logger.info("AI: Conversation history cleared")
    
    def cleanup(self):
        """Cleanup AI resources"""
        try:
            if self.session:
                self.session.close()
                logger.debug("AI: Session closed")
                
        except Exception as e:
            logger.error(f"ERROR: AI cleanup error: {e}")


# Test function
def test_ai_inference():
    """Test AI inference functionality"""
    print("Testing AI Inference...")
    
    if not REQUESTS_AVAILABLE:
        print("ERROR: requests library not available - cannot test AI inference")
        return
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="AIInputTest")
    output_queue = LockFreeQueue(maxsize=100, name="AIOutputTest")
    
    # Create AI inference thread
    ai = AIInferenceThread(
        input_queue=input_queue,
        output_queue=output_queue,
        ai_engine="ollama",
        model_name="llama2"
    )
    
    # Test initialization
    if ai.initialize():
        print("SUCCESS: AI inference initialized")
        print(f"Using model: {ai.model_name}")
        
        ai.cleanup()
        print("SUCCESS: AI inference test completed")
    else:
        print("WARNING: AI inference initialization failed (Ollama may not be running)")


if __name__ == "__main__":
    test_ai_inference()

"""
🎧 Voice Activity Detection (VAD) Processor
Ultra-fast speech detection with <20ms latency
"""

import numpy as np
import time
import threading
from typing import Optional, List, Tuple
import logging
from collections import deque

try:
    import torch
    import torchaudio
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️ PyTorch not available. VAD will use traditional methods.")

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    AudioChunk,
    ProcessingResult
)

logger = logging.getLogger(__name__)

class VADThread(HighPriorityThread):
    """
    High-priority Voice Activity Detection thread
    - Neural VAD using Silero VAD (if available)
    - Fallback to traditional energy-based VAD
    - Smart speech segmentation
    - Configurable sensitivity and timing
    """
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 vad_method: str = "auto",  # "silero", "energy", "auto"
                 sensitivity: float = 0.5,
                 min_speech_duration: float = 0.1,  # 100ms minimum
                 max_silence_duration: float = 0.5):  # 500ms max silence
        
        super().__init__("VADProcessor", priority="high", target_fps=3000)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # VAD configuration
        self.vad_method = vad_method
        self.sensitivity = sensitivity
        self.min_speech_duration = min_speech_duration
        self.max_silence_duration = max_silence_duration
        
        # VAD models
        self.silero_vad = None
        self.energy_vad = None
        self.active_vad_method = None
        
        # Speech state management
        self.voice_active = False
        self.speech_buffer = deque()
        self.silence_duration = 0.0
        self.speech_duration = 0.0
        self.last_chunk_time = time.time()
        
        # Performance optimization
        self.chunk_duration = 0.016  # 16ms chunks
        self.vad_cache = {}
        self.cache_size_limit = 1000
        
        # Statistics
        self.total_chunks = 0
        self.speech_chunks = 0
        self.silence_chunks = 0
        
    def initialize_vad_models(self) -> bool:
        """Initialize VAD models based on availability"""
        try:
            if self.vad_method == "auto" or self.vad_method == "silero":
                if self._initialize_silero_vad():
                    self.active_vad_method = "silero"
                    logger.info("✅ Using Silero VAD (neural)")
                elif self.vad_method == "silero":
                    logger.error("❌ Silero VAD requested but not available")
                    return False
                else:
                    self._initialize_energy_vad()
                    self.active_vad_method = "energy"
                    logger.info("✅ Using Energy-based VAD (fallback)")
            else:
                self._initialize_energy_vad()
                self.active_vad_method = "energy"
                logger.info("✅ Using Energy-based VAD")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize VAD: {e}")
            return False
    
    def _initialize_silero_vad(self) -> bool:
        """Initialize Silero VAD model"""
        if not TORCH_AVAILABLE:
            return False
        
        try:
            # Load Silero VAD model
            self.silero_vad = SileroVAD(sensitivity=self.sensitivity)
            return self.silero_vad.is_loaded()
            
        except Exception as e:
            logger.error(f"❌ Failed to load Silero VAD: {e}")
            return False
    
    def _initialize_energy_vad(self):
        """Initialize energy-based VAD"""
        self.energy_vad = EnergyVAD(
            sensitivity=self.sensitivity,
            min_speech_duration=self.min_speech_duration
        )
    
    def process_loop(self):
        """Main VAD processing loop"""
        if not self.initialize_vad_models():
            logger.error("❌ Failed to initialize VAD models")
            return
        
        logger.info(f"🎧 VAD processor started using {self.active_vad_method} method")
        
        try:
            while self.running.is_set() and not self.shutdown_event.is_set():
                self._process_audio_chunk()
                
        except Exception as e:
            logger.error(f"❌ VAD processing error: {e}")
        finally:
            self._finalize_pending_speech()
            logger.info("🛑 VAD processor stopped")
    
    def _process_audio_chunk(self):
        """Process a single audio chunk from the input queue"""
        # Get audio chunk
        audio_chunk = self.input_queue.get_nowait()
        if audio_chunk is None:
            time.sleep(0.001)  # 1ms sleep when no data
            return
        
        start_time = time.time()
        
        try:
            # Perform VAD analysis
            voice_probability = self._detect_voice(audio_chunk)
            
            # Update speech state
            self._update_speech_state(audio_chunk, voice_probability)
            
            # Performance tracking
            processing_time = time.time() - start_time
            self.update_performance_stats(processing_time)
            
            self.total_chunks += 1
            
        except Exception as e:
            logger.error(f"❌ Error processing audio chunk: {e}")
            self.performance_stats['errors'] += 1
    
    def _detect_voice(self, audio_chunk: AudioChunk) -> float:
        """Detect voice in audio chunk and return probability"""
        try:
            # Check cache first for performance
            cache_key = self._generate_cache_key(audio_chunk.data)
            if cache_key in self.vad_cache:
                return self.vad_cache[cache_key]
            
            # Perform VAD detection
            if self.active_vad_method == "silero":
                voice_prob = self.silero_vad.detect(audio_chunk.data)
            else:
                voice_prob = self.energy_vad.detect(audio_chunk.data)
            
            # Cache result
            if len(self.vad_cache) < self.cache_size_limit:
                self.vad_cache[cache_key] = voice_prob
            
            return voice_prob
            
        except Exception as e:
            logger.error(f"❌ VAD detection error: {e}")
            return 0.0
    
    def _generate_cache_key(self, audio_data: np.ndarray) -> str:
        """Generate cache key for audio data"""
        # Use RMS and peak values as a simple hash
        rms = np.sqrt(np.mean(audio_data ** 2))
        peak = np.max(np.abs(audio_data))
        return f"{rms:.4f}_{peak:.4f}_{len(audio_data)}"
    
    def _update_speech_state(self, audio_chunk: AudioChunk, voice_probability: float):
        """Update speech state based on VAD results"""
        current_time = time.time()
        chunk_duration = current_time - self.last_chunk_time
        self.last_chunk_time = current_time
        
        # Determine if voice is active
        voice_detected = voice_probability > self.sensitivity
        
        if voice_detected:
            self.speech_chunks += 1
            
            if not self.voice_active:
                # Start of speech
                self._start_speech_segment(audio_chunk)
            else:
                # Continue speech
                self.speech_buffer.append(audio_chunk)
                self.speech_duration += chunk_duration
            
            self.silence_duration = 0.0
            
        else:
            self.silence_chunks += 1
            
            if self.voice_active:
                # Potential end of speech
                self.silence_duration += chunk_duration
                self.speech_buffer.append(audio_chunk)  # Include silence for context
                
                # Check if silence duration exceeds threshold
                if self.silence_duration >= self.max_silence_duration:
                    self._end_speech_segment()
            
            # Reset speech duration if no voice activity
            if not self.voice_active:
                self.speech_duration = 0.0
    
    def _start_speech_segment(self, audio_chunk: AudioChunk):
        """Start a new speech segment"""
        self.voice_active = True
        self.speech_buffer.clear()
        self.speech_buffer.append(audio_chunk)
        self.speech_duration = 0.0
        self.silence_duration = 0.0
        
        logger.debug("🗣️ Speech segment started")
    
    def _end_speech_segment(self):
        """End current speech segment and send to next stage"""
        if not self.voice_active or len(self.speech_buffer) == 0:
            return
        
        # Check minimum speech duration
        if self.speech_duration < self.min_speech_duration:
            logger.debug(f"⏭️ Speech segment too short: {self.speech_duration:.3f}s < {self.min_speech_duration:.3f}s")
            self.voice_active = False
            self.speech_buffer.clear()
            return
        
        # Combine audio chunks into speech segment
        speech_segment = self._create_speech_segment()
        
        if speech_segment:
            # Send to STT processing
            result = ProcessingResult(
                data=speech_segment,
                timestamp=speech_segment.timestamp,
                latency=time.time() - speech_segment.timestamp,
                confidence=0.9,  # VAD confidence
                stage="VAD",
                chunk_id=speech_segment.chunk_id
            )
            
            if not self.output_queue.put_nowait(result):
                logger.warning("⚠️ VAD output queue full, dropping speech segment")
                self.performance_stats['errors'] += 1
            else:
                logger.debug(f"📤 Speech segment sent: {self.speech_duration:.3f}s, {len(speech_segment.data)} samples")
        
        # Reset state
        self.voice_active = False
        self.speech_buffer.clear()
        self.speech_duration = 0.0
        self.silence_duration = 0.0
    
    def _create_speech_segment(self) -> Optional[AudioChunk]:
        """Combine buffered chunks into a single speech segment"""
        if not self.speech_buffer:
            return None
        
        try:
            # Combine all audio data
            audio_data_list = [chunk.data for chunk in self.speech_buffer]
            combined_audio = np.concatenate(audio_data_list)
            
            # Use first chunk's metadata
            first_chunk = self.speech_buffer[0]
            
            speech_segment = AudioChunk(
                data=combined_audio,
                timestamp=first_chunk.timestamp,
                sample_rate=first_chunk.sample_rate,
                channels=first_chunk.channels,
                chunk_id=first_chunk.chunk_id,
                confidence=0.95  # High confidence for detected speech
            )
            
            return speech_segment
            
        except Exception as e:
            logger.error(f"❌ Error creating speech segment: {e}")
            return None
    
    def _finalize_pending_speech(self):
        """Finalize any pending speech segments on shutdown"""
        if self.voice_active and len(self.speech_buffer) > 0:
            logger.info("🔚 Finalizing pending speech segment...")
            self._end_speech_segment()
    
    def get_vad_stats(self) -> dict:
        """Get detailed VAD statistics"""
        stats = self.get_performance_report()
        
        if self.total_chunks > 0:
            speech_ratio = self.speech_chunks / self.total_chunks
            silence_ratio = self.silence_chunks / self.total_chunks
        else:
            speech_ratio = silence_ratio = 0.0
        
        stats.update({
            'vad_method': self.active_vad_method,
            'sensitivity': self.sensitivity,
            'total_chunks': self.total_chunks,
            'speech_chunks': self.speech_chunks,
            'silence_chunks': self.silence_chunks,
            'speech_ratio': speech_ratio,
            'silence_ratio': silence_ratio,
            'voice_active': self.voice_active,
            'current_speech_duration': self.speech_duration,
            'current_silence_duration': self.silence_duration,
            'cache_size': len(self.vad_cache),
            'queue_input_stats': self.input_queue.get_stats(),
            'queue_output_stats': self.output_queue.get_stats()
        })
        
        return stats

class SileroVAD:
    """
    Silero VAD model wrapper for neural voice activity detection
    - High accuracy neural network
    - Optimized for real-time processing
    - Configurable sensitivity
    """
    
    def __init__(self, sensitivity: float = 0.5, model_name: str = "silero_vad"):
        self.sensitivity = sensitivity
        self.model_name = model_name
        self.model = None
        self.sample_rate = 16000  # Silero VAD expects 16kHz
        self.window_size_samples = 512  # 32ms at 16kHz
        
        self._load_model()
    
    def _load_model(self):
        """Load Silero VAD model"""
        try:
            # Try to load from local cache first
            model_path = f"{self.model_name}.jit"
            
            try:
                self.model = torch.jit.load(model_path)
                logger.info("✅ Loaded Silero VAD from local cache")
            except:
                # Download from Silero repository
                logger.info("📥 Downloading Silero VAD model...")
                self.model, _ = torch.hub.load(
                    repo_or_dir='snakers4/silero-vad',
                    model='silero_vad',
                    force_reload=False
                )
                
                # Save for future use
                torch.jit.save(self.model, model_path)
                logger.info("✅ Silero VAD model downloaded and cached")
            
            self.model.eval()
            
        except Exception as e:
            logger.error(f"❌ Failed to load Silero VAD: {e}")
            self.model = None
    
    def is_loaded(self) -> bool:
        """Check if model is successfully loaded"""
        return self.model is not None
    
    def detect(self, audio_data: np.ndarray) -> float:
        """Detect voice activity in audio data"""
        if self.model is None:
            return 0.0
        
        try:
            # Resample to 16kHz if needed
            if len(audio_data) == 768:  # 16ms at 48kHz
                # Downsample 48kHz -> 16kHz (3:1 ratio)
                audio_16k = audio_data[::3][:256]  # Take every 3rd sample
            else:
                audio_16k = audio_data
            
            # Ensure correct length
            if len(audio_16k) != self.window_size_samples:
                # Pad or truncate to correct size
                if len(audio_16k) < self.window_size_samples:
                    audio_16k = np.pad(audio_16k, (0, self.window_size_samples - len(audio_16k)))
                else:
                    audio_16k = audio_16k[:self.window_size_samples]
            
            # Convert to tensor
            audio_tensor = torch.from_numpy(audio_16k).float().unsqueeze(0)
            
            # Run VAD
            with torch.no_grad():
                voice_prob = self.model(audio_tensor, self.sample_rate).item()
            
            return voice_prob
            
        except Exception as e:
            logger.error(f"❌ Silero VAD detection error: {e}")
            return 0.0

class EnergyVAD:
    """
    Energy-based VAD as fallback
    - Fast computation
    - Configurable thresholds
    - Adaptive noise floor estimation
    """
    
    def __init__(self, 
                 sensitivity: float = 0.5,
                 min_speech_duration: float = 0.1,
                 noise_adaptation_rate: float = 0.01):
        
        self.sensitivity = sensitivity
        self.min_speech_duration = min_speech_duration
        self.noise_adaptation_rate = noise_adaptation_rate
        
        # Energy thresholds
        self.base_threshold_db = -30.0  # Base threshold in dB
        self.dynamic_range_db = 20.0    # Dynamic range for sensitivity
        
        # Adaptive noise floor
        self.noise_floor = 1e-8
        self.noise_estimation_window = deque(maxlen=100)
        
        # Smoothing
        self.energy_history = deque(maxlen=10)
        
    def detect(self, audio_data: np.ndarray) -> float:
        """Detect voice activity using energy-based method"""
        if len(audio_data) == 0:
            return 0.0
        
        try:
            # Calculate RMS energy
            rms_energy = np.sqrt(np.mean(audio_data ** 2))
            
            # Update energy history
            self.energy_history.append(rms_energy)
            
            # Update noise floor estimation
            self._update_noise_floor(rms_energy)
            
            # Calculate energy threshold
            threshold = self._calculate_threshold()
            
            # Smooth energy using recent history
            smoothed_energy = np.mean(self.energy_history)
            
            # Calculate voice probability
            if smoothed_energy > threshold:
                # Energy above threshold
                energy_ratio = smoothed_energy / threshold
                voice_prob = min(energy_ratio - 1.0, 1.0)  # Scale to 0-1
            else:
                voice_prob = 0.0
            
            return voice_prob
            
        except Exception as e:
            logger.error(f"❌ Energy VAD error: {e}")
            return 0.0
    
    def _update_noise_floor(self, current_energy: float):
        """Update adaptive noise floor"""
        self.noise_estimation_window.append(current_energy)
        
        if len(self.noise_estimation_window) >= 10:
            # Use lower percentile as noise floor
            noise_estimate = np.percentile(list(self.noise_estimation_window), 20)
            
            # Adapt noise floor slowly
            self.noise_floor += (noise_estimate - self.noise_floor) * self.noise_adaptation_rate
            self.noise_floor = max(self.noise_floor, 1e-8)  # Prevent division by zero
    
    def _calculate_threshold(self) -> float:
        """Calculate dynamic energy threshold"""
        # Base threshold adjusted by sensitivity
        threshold_db = self.base_threshold_db + (1.0 - self.sensitivity) * self.dynamic_range_db
        threshold_linear = 10 ** (threshold_db / 20.0)
        
        # Add noise floor
        threshold = max(threshold_linear, self.noise_floor * 2.0)
        
        return threshold

# Test function
def test_vad_processor():
    """Test VAD processor functionality"""
    print("🧪 Testing VAD Processor...")
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="VADInput")
    output_queue = LockFreeQueue(maxsize=100, name="VADOutput")
    
    # Create VAD processor
    vad_processor = VADThread(
        input_queue=input_queue,
        output_queue=output_queue,
        vad_method="auto",
        sensitivity=0.5
    )
    
    try:
        # Start VAD processor
        vad_processor.start()
        
        # Generate test audio chunks
        print("🎤 Generating test audio chunks...")
        
        sample_rate = 48000
        chunk_size = 768  # 16ms
        
        # Generate speech-like signal (sine waves)
        for i in range(100):
            if i < 20 or i > 80:
                # Silence
                audio_data = np.random.normal(0, 0.01, chunk_size).astype(np.float32)
            else:
                # Speech-like signal
                t = np.linspace(0, chunk_size/sample_rate, chunk_size)
                audio_data = (np.sin(2*np.pi*440*t) * 0.1 + 
                             np.sin(2*np.pi*880*t) * 0.05 +
                             np.random.normal(0, 0.02, chunk_size)).astype(np.float32)
            
            chunk = AudioChunk(
                data=audio_data,
                timestamp=time.time(),
                sample_rate=sample_rate,
                channels=1,
                chunk_id=i
            )
            
            input_queue.put_nowait(chunk)
            time.sleep(0.01)  # Simulate real-time
        
        # Let VAD process chunks
        time.sleep(2.0)
        
        # Check results
        speech_segments = 0
        while True:
            result = output_queue.get_nowait()
            if result is None:
                break
            speech_segments += 1
            print(f"   Speech segment {speech_segments}: {len(result.data.data)} samples")
        
        print(f"✅ VAD test completed: {speech_segments} speech segments detected")
        print(f"📊 VAD stats: {vad_processor.get_vad_stats()}")
        
    finally:
        vad_processor.shutdown()
        vad_processor.join(timeout=2.0)

if __name__ == "__main__":
    test_vad_processor()
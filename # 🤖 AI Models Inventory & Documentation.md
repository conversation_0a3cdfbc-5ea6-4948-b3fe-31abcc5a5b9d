# 🤖 AI Models Inventory & Documentation

## 📋 Overview
This document provides a comprehensive inventory of all AI models, LLM models, TTS models, ASR models, and other machine learning models available in our current setup.

---

## 🧠 Large Language Models (LLMs)

### 🔥 Currently Installed Ollama Models

| Model Name | Model ID | Size | Purpose | Last Modified |
|------------|----------|------|---------|---------------|
| **JOSIEFIED-Qwen3** | `goekdenizguelmez/JOSIEFIED-Qwen3:14b` | 9.0 GB | **Primary Voice Agent** | 4 hours ago |
| **Llama 3.2** | `llama3.2:latest` | 2.0 GB | General Purpose | 2 days ago |
| **KotakNeo** | `adityakale/kotakneo:latest` | 2.0 GB | Specialized | 9 days ago |
| **ExaOne Deep** | `exaone-deep:32b` | 19 GB | Large Context | 11 days ago |
| **AM Thinking** | `huihui_ai/am-thinking-abliterate:latest` | 19 GB | Reasoning | 11 days ago |
| **Magistral** | `huihui_ai/magistral-abliterated:24b` | 14 GB | Advanced | 11 days ago |
| **Homunculus** | `huihui_ai/homunculus-abliterated:latest` | 7.6 GB | Specialized | 11 days ago |
| **AceReason** | `huihui_ai/acereason-nemotron-abliterated:14b` | 9.0 GB | Reasoning | 11 days ago |
| **Qwen 2.5 VL** | `qwen2.5vl:32b` | 21 GB | **Vision + Language** | 11 days ago |
| **Phi4 Reasoning** | `phi4-reasoning:plus` | 11 GB | Reasoning | 11 days ago |
| **Nemotron Mini** | `nemotron-mini:4b` | 2.7 GB | Lightweight | 11 days ago |
| **Hermes 3** | `hermes3:8b` | 4.7 GB | Conversational | 12 days ago |
| **Marco-O1** | `marco-o1:7b` | 4.7 GB | Reasoning | 12 days ago |
| **Magistral** | `magistral:24b` | 14 GB | Advanced | 2 weeks ago |
| **Command-R** | `command-r:35b` | 18 GB | Large Context | 2 weeks ago |
| **Cogito** | `cogito:32b` | 19 GB | Reasoning | 2 weeks ago |
| **Gemma 3** | `gemma3:27b` | 17 GB | Google Model | 2 weeks ago |
| **Mistral Small** | `mistral-small:24b` | 14 GB | Efficient | 2 weeks ago |
| **Falcon 3** | `falcon3:10b` | 6.3 GB | General Purpose | 2 weeks ago |
| **Granite 3.3** | `granite3.3:8b` | 4.9 GB | IBM Model | 2 weeks ago |
| **Qwen 3** | `qwen3:32b` | 20 GB | Large Context | 2 weeks ago |
| **DeepSeek R1** | `deepseek-r1:latest` | 5.2 GB | **Latest Reasoning** | 2 weeks ago |


---

## 🎤 Speech-to-Text (ASR) Models

### 🔊 Whisper Models (OpenAI)
| Model | Size | Use Case | Performance |
|-------|------|----------|-------------|
| **whisper-large-v3** | Large | **Primary ASR** | Highest accuracy |
| **whisper-large-v3-turbo** | Large | Fast inference | High accuracy + speed |
| **whisper-medium** | Medium | Balanced | Good accuracy |
| **whisper-small** | Small | Lightweight | Fast inference |
| **whisper-tiny** | Tiny | Edge devices | Ultra-fast |

### 🌊 Deepgram Models
| Model | Specialization | Use Case |
|-------|----------------|----------|
| **nova-3** | General | **Primary for LiveKit** |
| **nova-3-medical** | Medical | Healthcare |
| **nova-2-general** | General | Standard |
| **enhanced-general** | Enhanced | High accuracy |

### 🔗 Other ASR Providers
- **AssemblyAI** - Real-time transcription
- **Speechmatics** - Multi-language
- **Gladia** - Fast processing
- **Groq Whisper** - GPU-accelerated

---

## 🗣️ Text-to-Speech (TTS) Models

### 🎵 OpenAI TTS
| Model | Quality | Speed | Voices |
|-------|---------|-------|--------|
| **tts-1-hd** | High | Medium | 10 voices |
| **tts-1** | Standard | Fast | 10 voices |

**Available Voices**: alloy, ash, ballad, coral, echo, fable, onyx, nova, sage, shimmer

### 🎭 ElevenLabs
- **Multilingual V2** - Natural voices
- **Turbo V2** - Fast synthesis
- **Custom voices** - Cloned voices

### 🚀 Cartesia
| Model | Features | Speed |
|-------|----------|-------|
| **sonic-2** | Latest | Ultra-fast |
| **sonic-turbo** | Speed optimized | Fastest |
| **sonic-lite** | Lightweight | Fast |

### 🎪 Other TTS Providers
- **Rime AI** - Mistv2, Arcana models
- **PlayAI** - Custom voices
- **Resemble** - Voice cloning
- **Speechify** - Natural voices

### 🖥️ System TTS (Windows)
- **Microsoft Zira Desktop** - **Currently Used**
- **Microsoft David Desktop** - Alternative

---

## 👁️ Vision Models

### 🔍 Multimodal LLMs
- **Qwen 2.5 VL (32B)** - Vision + Language (Installed)
- **GPT-4o** - OpenAI multimodal
- **Gemini 2.0 Flash** - Google multimodal
- **Claude 3.5 Sonnet** - Anthropic vision

### 🎨 Image Generation
- **DALL-E 3** - OpenAI latest
- **DALL-E 2** - OpenAI standard

---

## 🔧 Specialized Models

### 🎯 Voice Activity Detection (VAD)
- **Silero VAD** - **Primary VAD** (Fast, accurate)
- **WebRTC VAD** - Browser-based
- **Multilingual VAD** - Multi-language support

### 🔄 Turn Detection
- **MultilingualModel** - **Currently Used**
- **EndOfSpeechModel** - Simple detection

### 📊 Embedding Models
- **text-embedding-3-large** - OpenAI latest
- **text-embedding-3-small** - Efficient
- **text-embedding-ada-002** - Standard

---

## ⚙️ Current Voice Agent Configuration

### 🎯 Active Setup
```yaml
Primary LLM: goekdenizguelmez/JOSIEFIED-Qwen3:14b
ASR Model: openai/whisper-large-v3 (GPU-accelerated)
TTS System: Microsoft Zira Desktop (Windows SAPI)
VAD: Silero VAD
Device: CPU (RTX 4070 Ti SUPER available)
```

### 🚨 Known Issues
- **Ollama Model**: Returns `<think>` tags instead of clean responses
- **Performance**: 5-8 second response times (target: <2 seconds)
- **GPU**: Not fully utilized for Whisper acceleration

---

## 📈 Performance Metrics

### ⚡ Current Benchmarks
| Component | Current Time | Target Time | Status |
|-----------|--------------|-------------|---------|
| **Whisper ASR** | 2.9-5.2s | <0.5s | ❌ Needs GPU |
| **Ollama LLM** | 2.3-3.2s | <1.0s | ⚠️ Acceptable |
| **Windows TTS** | 0.17-0.27s | <0.3s | ✅ Good |
| **Total Response** | 5.4-8.4s | <2.0s | ❌ Too slow |

### 🎯 Optimization Priorities
1. **Fix Ollama prompting** - Remove `<think>` tags
2. **Enable GPU acceleration** - For Whisper ASR
3. **Optimize model selection** - Faster alternatives
4. **Reduce latency** - Pipeline optimization

---

## 🔮 Future Enhancements

### 📋 Planned Additions
- **Real-time streaming** - Faster response times
- **Voice cloning** - Custom voice synthesis
- **Multi-language** - International support
- **Edge deployment** - Local processing

### 🛠️ Technical Roadmap
- Implement streaming ASR/TTS
- Add GPU memory optimization
- Create model switching system
- Build performance monitoring

---

*Last Updated: 2025-06-28*
*Total Models: 22 Ollama + 50+ Cloud Models*
*Primary Use Case: Interactive Voice Agents*

"""
Circuit Breaker Pattern Implementation
Provides fault tolerance for AI inference and other critical components
"""

import time
import logging
from enum import Enum
from typing import Optional, Callable, Any
import threading

logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreaker:
    """Circuit breaker for fault tolerance"""
    
    def __init__(self,
                 name: str,
                 failure_threshold: int = 5,
                 recovery_timeout: float = 30.0,
                 success_threshold: int = 3):
        """
        Initialize circuit breaker
        
        Args:
            name: Circuit breaker name for logging
            failure_threshold: Number of failures before opening
            recovery_timeout: Time to wait before trying again (seconds)
            success_threshold: Successes needed to close from half-open
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        
        # State management
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Statistics
        self.total_requests = 0
        self.total_failures = 0
        self.total_successes = 0
        
        logger.info(f"INIT: Circuit breaker '{name}' initialized")
    
    def can_execute(self) -> bool:
        """Check if request can be executed"""
        with self._lock:
            self.total_requests += 1
            
            if self.state == CircuitBreakerState.CLOSED:
                return True
            
            elif self.state == CircuitBreakerState.OPEN:
                # Check if recovery timeout has passed
                if time.time() - self.last_failure_time >= self.recovery_timeout:
                    logger.info(f"RECOVERY: Circuit breaker '{self.name}' transitioning to HALF_OPEN")
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.success_count = 0
                    return True
                else:
                    return False
            
            elif self.state == CircuitBreakerState.HALF_OPEN:
                return True
            
            return False
    
    def record_success(self):
        """Record successful operation"""
        with self._lock:
            self.total_successes += 1
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.success_threshold:
                    logger.info(f"RECOVERY: Circuit breaker '{self.name}' CLOSED after recovery")
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
                    self.success_count = 0
            
            elif self.state == CircuitBreakerState.CLOSED:
                # Reset failure count on success
                self.failure_count = 0
    
    def record_failure(self):
        """Record failed operation"""
        with self._lock:
            self.total_failures += 1
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitBreakerState.CLOSED:
                if self.failure_count >= self.failure_threshold:
                    logger.error(f"ALERT: Circuit breaker '{self.name}' OPENED after {self.failure_count} failures")
                    self.state = CircuitBreakerState.OPEN
            
            elif self.state == CircuitBreakerState.HALF_OPEN:
                logger.warning(f"WARNING: Circuit breaker '{self.name}' failed during recovery - back to OPEN")
                self.state = CircuitBreakerState.OPEN
                self.success_count = 0
    
    def force_open(self):
        """Force circuit breaker to open state"""
        with self._lock:
            logger.warning(f"FORCE: Circuit breaker '{self.name}' forced OPEN")
            self.state = CircuitBreakerState.OPEN
            self.last_failure_time = time.time()
    
    def force_close(self):
        """Force circuit breaker to closed state"""
        with self._lock:
            logger.info(f"FORCE: Circuit breaker '{self.name}' forced CLOSED")
            self.state = CircuitBreakerState.CLOSED
            self.failure_count = 0
            self.success_count = 0
    
    def get_stats(self) -> dict:
        """Get circuit breaker statistics"""
        with self._lock:
            failure_rate = 0
            if self.total_requests > 0:
                failure_rate = self.total_failures / self.total_requests
            
            return {
                'name': self.name,
                'state': self.state.value,
                'failure_count': self.failure_count,
                'success_count': self.success_count,
                'total_requests': self.total_requests,
                'total_failures': self.total_failures,
                'total_successes': self.total_successes,
                'failure_rate': failure_rate,
                'last_failure_time': self.last_failure_time,
                'time_since_last_failure': time.time() - self.last_failure_time if self.last_failure_time > 0 else 0
            }
    
    def __str__(self) -> str:
        """String representation"""
        return f"CircuitBreaker(name='{self.name}', state={self.state.value}, failures={self.failure_count})"


class CircuitBreakerDecorator:
    """Decorator for applying circuit breaker to functions"""
    
    def __init__(self, circuit_breaker: CircuitBreaker, fallback: Optional[Callable] = None):
        self.circuit_breaker = circuit_breaker
        self.fallback = fallback
    
    def __call__(self, func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            if not self.circuit_breaker.can_execute():
                logger.warning(f"BLOCKED: Function '{func.__name__}' blocked by circuit breaker")
                if self.fallback:
                    return self.fallback(*args, **kwargs)
                else:
                    raise Exception(f"Circuit breaker '{self.circuit_breaker.name}' is open")
            
            try:
                result = func(*args, **kwargs)
                self.circuit_breaker.record_success()
                return result
            except Exception as e:
                self.circuit_breaker.record_failure()
                raise e
        
        return wrapper


# Utility functions for common circuit breaker patterns
def create_ai_circuit_breaker(name: str) -> CircuitBreaker:
    """Create circuit breaker optimized for AI inference"""
    return CircuitBreaker(
        name=name,
        failure_threshold=3,  # Fail fast for AI
        recovery_timeout=15.0,  # Quick recovery attempts
        success_threshold=2   # Conservative recovery
    )


def create_network_circuit_breaker(name: str) -> CircuitBreaker:
    """Create circuit breaker optimized for network operations"""
    return CircuitBreaker(
        name=name,
        failure_threshold=5,  # More tolerance for network issues
        recovery_timeout=30.0,  # Longer recovery time
        success_threshold=3   # More conservative recovery
    )


def create_audio_circuit_breaker(name: str) -> CircuitBreaker:
    """Create circuit breaker optimized for audio processing"""
    return CircuitBreaker(
        name=name,
        failure_threshold=10,  # High tolerance for audio glitches
        recovery_timeout=5.0,   # Quick recovery for real-time audio
        success_threshold=1    # Fast recovery
    )


# Test function
def test_circuit_breaker():
    """Test circuit breaker functionality"""
    print("Testing Circuit Breaker...")
    
    # Create test circuit breaker
    cb = CircuitBreaker("test", failure_threshold=3, recovery_timeout=1.0)
    
    # Test normal operation
    assert cb.can_execute() == True
    cb.record_success()
    print("✅ Normal operation test passed")
    
    # Test failure handling
    for i in range(3):
        assert cb.can_execute() == True
        cb.record_failure()
    
    # Should be open now
    assert cb.can_execute() == False
    print("✅ Failure threshold test passed")
    
    # Test recovery
    time.sleep(1.1)  # Wait for recovery timeout
    assert cb.can_execute() == True  # Should be half-open
    cb.record_success()
    cb.record_success()  # Should close after success threshold
    assert cb.can_execute() == True
    print("✅ Recovery test passed")
    
    print("✅ All circuit breaker tests passed!")


if __name__ == "__main__":
    test_circuit_breaker()

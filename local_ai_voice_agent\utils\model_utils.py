"""
Model management utilities for local AI voice agent
Handles model loading, configuration, and management
"""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional

import yaml
from pathlib import Path

logger = logging.getLogger(__name__)


class ModelManager:
    """Manager for AI models and configurations"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize Model Manager
        
        Args:
            config_path: Path to model configuration file
        """
        self._config_path = config_path or self._find_config_file()
        self._config = {}
        self._loaded_models = {}
        
        self._load_config()
    
    def _find_config_file(self) -> str:
        """Find the model configuration file"""
        possible_paths = [
            "config/models.yaml",
            "../config/models.yaml",
            "local_ai_voice_agent/config/models.yaml",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # Create default config if none found
        return self._create_default_config()
    
    def _create_default_config(self) -> str:
        """Create a default configuration file"""
        default_config = {
            "llm": {
                "primary": {
                    "name": "JOSIEFIED-Qwen3",
                    "model_id": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                    "endpoint": "http://localhost:11434/v1"
                },
                "vision": {
                    "name": "Qwen 2.5 VL",
                    "model_id": "qwen2.5vl:32b",
                    "endpoint": "http://localhost:11434/v1"
                }
            },
            "stt": {
                "primary": {
                    "name": "Whisper Large V3",
                    "model": "large-v3",
                    "language": "en"
                }
            },
            "tts": {
                "primary": {
                    "name": "Auto TTS",
                    "engine": "auto",
                    "rate": 200,
                    "volume": 0.9
                }
            }
        }
        
        config_dir = "config"
        os.makedirs(config_dir, exist_ok=True)
        config_path = os.path.join(config_dir, "models.yaml")
        
        with open(config_path, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False)
        
        logger.info(f"Created default config at {config_path}")
        return config_path
    
    def _load_config(self):
        """Load model configuration from file"""
        try:
            with open(self._config_path, 'r') as f:
                self._config = yaml.safe_load(f)
            logger.info(f"Loaded model config from {self._config_path}")
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            self._config = {}
    
    def get_llm_config(self, model_type: str = "primary") -> Dict[str, Any]:
        """Get LLM configuration"""
        return self._config.get("llm", {}).get(model_type, {})
    
    def get_stt_config(self, model_type: str = "primary") -> Dict[str, Any]:
        """Get STT configuration"""
        return self._config.get("stt", {}).get(model_type, {})
    
    def get_tts_config(self, model_type: str = "primary") -> Dict[str, Any]:
        """Get TTS configuration"""
        return self._config.get("tts", {}).get(model_type, {})
    
    def get_vad_config(self) -> Dict[str, Any]:
        """Get VAD configuration"""
        return self._config.get("vad", {})
    
    def get_vision_config(self) -> Dict[str, Any]:
        """Get Vision configuration"""
        return self._config.get("vision", {})
    
    def get_agent_config(self) -> Dict[str, Any]:
        """Get Agent configuration"""
        return self._config.get("agent", {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get Performance configuration"""
        return self._config.get("performance", {})
    
    def list_available_models(self, model_category: str) -> List[str]:
        """List available models for a category"""
        category_config = self._config.get(model_category, {})
        return list(category_config.keys())
    
    def validate_model_availability(self, model_category: str, model_name: str) -> bool:
        """Validate if a model is available"""
        category_config = self._config.get(model_category, {})
        return model_name in category_config
    
    async def check_ollama_models(self) -> Dict[str, bool]:
        """Check which Ollama models are available"""
        try:
            import httpx
            
            llm_config = self.get_llm_config()
            endpoint = llm_config.get("endpoint", "http://localhost:11434").replace("/v1", "")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{endpoint}/api/tags", timeout=5.0)
                
                if response.status_code == 200:
                    available_models = response.json().get("models", [])
                    model_names = [model.get("name", "") for model in available_models]
                    
                    # Check configured models
                    results = {}
                    for model_type, config in self._config.get("llm", {}).items():
                        model_id = config.get("model_id", "")
                        results[model_id] = any(model_id in name for name in model_names)
                    
                    return results
                else:
                    logger.warning(f"Failed to check Ollama models: {response.status_code}")
                    return {}
                    
        except Exception as e:
            logger.error(f"Error checking Ollama models: {e}")
            return {}
    
    def get_model_instructions(self) -> str:
        """Get agent instructions from config"""
        agent_config = self.get_agent_config()
        return agent_config.get("instructions", "You are a helpful AI assistant.")
    
    def is_vision_enabled(self) -> bool:
        """Check if vision capabilities are enabled"""
        agent_config = self.get_agent_config()
        capabilities = agent_config.get("capabilities", {})
        return capabilities.get("vision_processing", False)
    
    def is_function_calling_enabled(self) -> bool:
        """Check if function calling is enabled"""
        agent_config = self.get_agent_config()
        capabilities = agent_config.get("capabilities", {})
        return capabilities.get("function_calling", False)
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return self._config.get("logging", {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        })
    
    def update_config(self, updates: Dict[str, Any]):
        """Update configuration and save to file"""
        try:
            # Deep merge updates into config
            self._deep_merge(self._config, updates)
            
            # Save to file
            with open(self._config_path, 'w') as f:
                yaml.dump(self._config, f, default_flow_style=False)
            
            logger.info("Configuration updated and saved")
            
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
    
    def _deep_merge(self, base: Dict, updates: Dict):
        """Deep merge two dictionaries"""
        for key, value in updates.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def get_memory_limit(self) -> int:
        """Get memory limit in GB"""
        perf_config = self.get_performance_config()
        return perf_config.get("memory_limit_gb", 16)
    
    def get_timeout_settings(self) -> Dict[str, float]:
        """Get timeout settings"""
        perf_config = self.get_performance_config()
        return {
            "request_timeout": perf_config.get("timeout_seconds", 30),
            "max_concurrent": perf_config.get("max_concurrent_requests", 3)
        }
    
    def export_config(self, output_path: str):
        """Export current configuration to a file"""
        try:
            with open(output_path, 'w') as f:
                yaml.dump(self._config, f, default_flow_style=False)
            logger.info(f"Configuration exported to {output_path}")
        except Exception as e:
            logger.error(f"Failed to export config: {e}")
    
    def import_config(self, input_path: str):
        """Import configuration from a file"""
        try:
            with open(input_path, 'r') as f:
                imported_config = yaml.safe_load(f)
            
            self._config = imported_config
            logger.info(f"Configuration imported from {input_path}")
            
        except Exception as e:
            logger.error(f"Failed to import config: {e}")


class ModelValidator:
    """Validator for model configurations and availability"""
    
    @staticmethod
    def validate_llm_config(config: Dict[str, Any]) -> bool:
        """Validate LLM configuration"""
        required_fields = ["model_id", "endpoint"]
        return all(field in config for field in required_fields)
    
    @staticmethod
    def validate_stt_config(config: Dict[str, Any]) -> bool:
        """Validate STT configuration"""
        required_fields = ["model"]
        return all(field in config for field in required_fields)
    
    @staticmethod
    def validate_tts_config(config: Dict[str, Any]) -> bool:
        """Validate TTS configuration"""
        required_fields = ["engine"]
        return all(field in config for field in required_fields)
    
    @staticmethod
    async def test_ollama_connection(endpoint: str) -> bool:
        """Test connection to Ollama server"""
        try:
            import httpx
            
            base_url = endpoint.replace("/v1", "")
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{base_url}/api/tags", timeout=5.0)
                return response.status_code == 200
                
        except Exception:
            return False
    
    @staticmethod
    def validate_whisper_model(model_name: str) -> bool:
        """Validate Whisper model name"""
        valid_models = [
            "tiny", "tiny.en", "base", "base.en", "small", "small.en",
            "medium", "medium.en", "large", "large-v1", "large-v2", "large-v3"
        ]
        return model_name in valid_models

2025-06-28 17:00:21,249 - core.audio_output - INFO -    2: Microsoft Sound Mapper - Output (2 ch)
2025-06-28 17:00:21,249 - core.audio_output - INFO -    3: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:00:21,249 - core.audio_output - INFO -    4: Z34G (HD Audio Driver for Displ (2 ch)
2025-06-28 17:00:21,249 - core.audio_output - INFO -    5: Realtek Digital Output (Realtek (2 ch)
2025-06-28 17:00:21,249 - core.audio_output - INFO -    6: Headset Earphone (Arctis 7 Chat (1 ch)
2025-06-28 17:00:21,249 - core.audio_output - INFO -    9: Primary Sound Driver (2 ch)
2025-06-28 17:00:21,249 - core.audio_output - INFO -    10: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    11: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    12: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    13: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    14: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    15: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    16: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    17: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    20: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    21: Headphones (Realtek USB Audio) (2 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    22: Speakers (Realtek USB Audio) (8 ch)
2025-06-28 17:00:21,250 - core.audio_output - INFO -    23: SPDIF Interface (Realtek USB Audio) (2 ch)
2025-06-28 17:00:21,251 - core.audio_output - INFO -    27: Z34G (ACX HD Audio Speaker) (2 ch)
2025-06-28 17:00:21,251 - core.audio_output - INFO -    28: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:00:21,260 - core.audio_output - INFO -    Device: 3
2025-06-28 17:00:21,260 - core.audio_output - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:00:21,260 - core.audio_output - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:00:21,260 - core.audio_output - INFO -    Channels: 1
2025-06-28 17:00:21,315 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-28 17:00:21,315 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\comtypes\gen'
2025-06-28 17:00:21,694 - core.audio_input - INFO -    0: Microsoft Sound Mapper - Input (2 ch)
2025-06-28 17:00:21,694 - core.audio_input - INFO -    1: Headset Microphone (Arctis 7 Ch (1 ch)
2025-06-28 17:00:21,694 - core.audio_input - INFO -    7: Primary Sound Capture Driver (2 ch)
2025-06-28 17:00:21,694 - core.audio_input - INFO -    8: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:00:21,694 - core.audio_input - INFO -    18: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:00:21,694 - core.audio_input - INFO -    19: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:00:21,694 - core.audio_input - INFO -    24: Line In (Realtek USB Audio) (2 ch)
2025-06-28 17:00:21,695 - core.audio_input - INFO -    25: Microphone (Realtek USB Audio) (2 ch)
2025-06-28 17:00:21,695 - core.audio_input - INFO -    26: Stereo Mix (Realtek USB Audio) (2 ch)
2025-06-28 17:00:21,704 - core.audio_input - INFO -    Device: 1
2025-06-28 17:00:21,704 - core.audio_input - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:00:21,704 - core.audio_input - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:00:21,704 - core.audio_input - INFO -    Channels: 1
2025-06-28 17:00:21,881 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 17:01:13,764 - core.stt_processor - INFO -    Model: small
2025-06-28 17:01:13,765 - core.stt_processor - INFO -    Device: cpu
2025-06-28 17:01:13,765 - core.stt_processor - INFO -    Compute: int8
2025-06-28 17:01:13,765 - core.stt_processor - INFO -    Memory: 244 MB
2025-06-28 17:07:19,769 - __main__ - INFO -    Duration: 418.0 seconds
2025-06-28 17:07:19,770 - __main__ - INFO -    Exchanges: 0
2025-06-28 17:07:19,770 - __main__ - INFO -    Interruptions: 0
2025-06-28 17:07:19,770 - __main__ - INFO -    System Health: 50.0%
2025-06-28 17:07:19,777 - __main__ - INFO -    Duration: 418.0 seconds
2025-06-28 17:07:19,777 - __main__ - INFO -    Exchanges: 0
2025-06-28 17:07:19,777 - __main__ - INFO -    Interruptions: 0
2025-06-28 17:07:19,777 - __main__ - INFO -    System Health: 50.0%
2025-06-28 17:13:20,402 - core.audio_output - INFO -    2: Microsoft Sound Mapper - Output (2 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    3: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    4: Z34G (HD Audio Driver for Displ (2 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    5: Realtek Digital Output (Realtek (2 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    6: Headset Earphone (Arctis 7 Chat (1 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    9: Primary Sound Driver (2 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    10: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    11: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:13:20,402 - core.audio_output - INFO -    12: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    13: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    14: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    15: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    16: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    17: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    20: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    21: Headphones (Realtek USB Audio) (2 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    22: Speakers (Realtek USB Audio) (8 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    23: SPDIF Interface (Realtek USB Audio) (2 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    27: Z34G (ACX HD Audio Speaker) (2 ch)
2025-06-28 17:13:20,403 - core.audio_output - INFO -    28: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:13:20,411 - core.audio_output - INFO -    Device: 3
2025-06-28 17:13:20,411 - core.audio_output - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:13:20,411 - core.audio_output - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:13:20,411 - core.audio_output - INFO -    Channels: 1
2025-06-28 17:13:20,466 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-28 17:13:20,466 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\comtypes\gen'
2025-06-28 17:13:20,849 - core.audio_input - INFO -    0: Microsoft Sound Mapper - Input (2 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    1: Headset Microphone (Arctis 7 Ch (1 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    7: Primary Sound Capture Driver (2 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    8: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    18: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    19: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    24: Line In (Realtek USB Audio) (2 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    25: Microphone (Realtek USB Audio) (2 ch)
2025-06-28 17:13:20,849 - core.audio_input - INFO -    26: Stereo Mix (Realtek USB Audio) (2 ch)
2025-06-28 17:13:20,858 - core.audio_input - INFO -    Device: 1
2025-06-28 17:13:20,859 - core.audio_input - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:13:20,859 - core.audio_input - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:13:20,859 - core.audio_input - INFO -    Channels: 1
2025-06-28 17:13:21,066 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 17:13:21,671 - core.stt_processor - INFO -    Model: small
2025-06-28 17:13:21,671 - core.stt_processor - INFO -    Device: cpu
2025-06-28 17:13:21,671 - core.stt_processor - INFO -    Compute: int8
2025-06-28 17:13:21,671 - core.stt_processor - INFO -    Memory: 244 MB
2025-06-28 17:13:24,384 - ultra_voice_agent - INFO -    Duration: 3.4 seconds
2025-06-28 17:13:24,384 - ultra_voice_agent - INFO -    Exchanges: 0
2025-06-28 17:13:24,384 - ultra_voice_agent - INFO -    Interruptions: 0
2025-06-28 17:13:24,384 - ultra_voice_agent - INFO -    System Health: 50.0%
2025-06-28 17:15:52,802 - core.audio_output - INFO -    2: Microsoft Sound Mapper - Output (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    3: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    4: Z34G (HD Audio Driver for Displ (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    5: Realtek Digital Output (Realtek (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    6: Headset Earphone (Arctis 7 Chat (1 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    9: Primary Sound Driver (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    10: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    11: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    12: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    13: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:15:52,802 - core.audio_output - INFO -    14: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    15: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    16: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    17: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    20: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    21: Headphones (Realtek USB Audio) (2 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    22: Speakers (Realtek USB Audio) (8 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    23: SPDIF Interface (Realtek USB Audio) (2 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    27: Z34G (ACX HD Audio Speaker) (2 ch)
2025-06-28 17:15:52,803 - core.audio_output - INFO -    28: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:15:52,811 - core.audio_output - INFO -    Device: 3
2025-06-28 17:15:52,811 - core.audio_output - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:15:52,811 - core.audio_output - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:15:52,812 - core.audio_output - INFO -    Channels: 1
2025-06-28 17:15:52,871 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-28 17:15:52,871 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\comtypes\gen'
2025-06-28 17:15:53,252 - core.audio_input - INFO -    0: Microsoft Sound Mapper - Input (2 ch)
2025-06-28 17:15:53,252 - core.audio_input - INFO -    1: Headset Microphone (Arctis 7 Ch (1 ch)
2025-06-28 17:15:53,252 - core.audio_input - INFO -    7: Primary Sound Capture Driver (2 ch)
2025-06-28 17:15:53,253 - core.audio_input - INFO -    8: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:15:53,253 - core.audio_input - INFO -    18: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:15:53,253 - core.audio_input - INFO -    19: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:15:53,253 - core.audio_input - INFO -    24: Line In (Realtek USB Audio) (2 ch)
2025-06-28 17:15:53,253 - core.audio_input - INFO -    25: Microphone (Realtek USB Audio) (2 ch)
2025-06-28 17:15:53,253 - core.audio_input - INFO -    26: Stereo Mix (Realtek USB Audio) (2 ch)
2025-06-28 17:15:53,263 - core.audio_input - INFO -    Device: 1
2025-06-28 17:15:53,263 - core.audio_input - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:15:53,263 - core.audio_input - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:15:53,263 - core.audio_input - INFO -    Channels: 1
2025-06-28 17:15:53,472 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 17:15:54,085 - core.stt_processor - INFO -    Model: small
2025-06-28 17:15:54,085 - core.stt_processor - INFO -    Device: cpu
2025-06-28 17:15:54,085 - core.stt_processor - INFO -    Compute: int8
2025-06-28 17:15:54,085 - core.stt_processor - INFO -    Memory: 244 MB
2025-06-28 17:15:57,907 - ultra_voice_agent - INFO -    Duration: 4.6 seconds
2025-06-28 17:15:57,907 - ultra_voice_agent - INFO -    Exchanges: 0
2025-06-28 17:15:57,908 - ultra_voice_agent - INFO -    Interruptions: 0
2025-06-28 17:15:57,908 - ultra_voice_agent - INFO -    System Health: 50.0%
2025-06-28 17:24:56,801 - core.audio_output - INFO -    2: Microsoft Sound Mapper - Output (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    3: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    4: Z34G (HD Audio Driver for Displ (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    5: Realtek Digital Output (Realtek (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    6: Headset Earphone (Arctis 7 Chat (1 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    9: Primary Sound Driver (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    10: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    11: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    12: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    13: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    14: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    15: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    16: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    17: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    20: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:24:56,801 - core.audio_output - INFO -    21: Headphones (Realtek USB Audio) (2 ch)
2025-06-28 17:24:56,802 - core.audio_output - INFO -    22: Speakers (Realtek USB Audio) (8 ch)
2025-06-28 17:24:56,802 - core.audio_output - INFO -    23: SPDIF Interface (Realtek USB Audio) (2 ch)
2025-06-28 17:24:56,802 - core.audio_output - INFO -    27: Z34G (ACX HD Audio Speaker) (2 ch)
2025-06-28 17:24:56,802 - core.audio_output - INFO -    28: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:24:56,809 - core.audio_output - INFO -    Device: 3
2025-06-28 17:24:56,809 - core.audio_output - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:24:56,809 - core.audio_output - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:24:56,809 - core.audio_output - INFO -    Channels: 1
2025-06-28 17:24:56,866 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-28 17:24:56,866 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\comtypes\gen'
2025-06-28 17:24:57,248 - core.audio_input - INFO -    0: Microsoft Sound Mapper - Input (2 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    1: Headset Microphone (Arctis 7 Ch (1 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    7: Primary Sound Capture Driver (2 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    8: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    18: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    19: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    24: Line In (Realtek USB Audio) (2 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    25: Microphone (Realtek USB Audio) (2 ch)
2025-06-28 17:24:57,248 - core.audio_input - INFO -    26: Stereo Mix (Realtek USB Audio) (2 ch)
2025-06-28 17:24:57,257 - core.audio_input - INFO -    Device: 1
2025-06-28 17:24:57,257 - core.audio_input - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:24:57,257 - core.audio_input - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:24:57,257 - core.audio_input - INFO -    Channels: 1
2025-06-28 17:24:57,434 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 17:24:58,073 - core.stt_processor - INFO -    Model: small
2025-06-28 17:24:58,073 - core.stt_processor - INFO -    Device: cpu
2025-06-28 17:24:58,073 - core.stt_processor - INFO -    Compute: int8
2025-06-28 17:24:58,073 - core.stt_processor - INFO -    Memory: 244 MB
2025-06-28 17:33:10,054 - core.audio_output - INFO -    2: Microsoft Sound Mapper - Output (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    3: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    4: Z34G (HD Audio Driver for Displ (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    5: Realtek Digital Output (Realtek (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    6: Headset Earphone (Arctis 7 Chat (1 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    9: Primary Sound Driver (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    10: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    11: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    12: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    13: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    14: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:33:10,054 - core.audio_output - INFO -    15: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    16: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    17: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    20: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    21: Headphones (Realtek USB Audio) (2 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    22: Speakers (Realtek USB Audio) (8 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    23: SPDIF Interface (Realtek USB Audio) (2 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    27: Z34G (ACX HD Audio Speaker) (2 ch)
2025-06-28 17:33:10,055 - core.audio_output - INFO -    28: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:33:10,065 - core.audio_output - INFO -    Device: 3
2025-06-28 17:33:10,065 - core.audio_output - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:33:10,065 - core.audio_output - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:33:10,065 - core.audio_output - INFO -    Channels: 1
2025-06-28 17:33:10,117 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-28 17:33:10,117 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\comtypes\gen'
2025-06-28 17:33:10,498 - core.audio_input - INFO -    0: Microsoft Sound Mapper - Input (2 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    1: Headset Microphone (Arctis 7 Ch (1 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    7: Primary Sound Capture Driver (2 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    8: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    18: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    19: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    24: Line In (Realtek USB Audio) (2 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    25: Microphone (Realtek USB Audio) (2 ch)
2025-06-28 17:33:10,498 - core.audio_input - INFO -    26: Stereo Mix (Realtek USB Audio) (2 ch)
2025-06-28 17:33:10,509 - core.audio_input - INFO -    Device: 1
2025-06-28 17:33:10,509 - core.audio_input - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:33:10,509 - core.audio_input - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:33:10,510 - core.audio_input - INFO -    Channels: 1
2025-06-28 17:33:10,731 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 17:33:11,457 - core.stt_processor - INFO -    Model: small
2025-06-28 17:33:11,457 - core.stt_processor - INFO -    Device: cpu
2025-06-28 17:33:11,457 - core.stt_processor - INFO -    Compute: int8
2025-06-28 17:33:11,457 - core.stt_processor - INFO -    Memory: 244 MB
2025-06-28 17:54:49,721 - __main__ - WARNING - Failed to load config: Expecting value: line 1 column 1 (char 0), using defaults
2025-06-28 17:54:49,721 - __main__ - INFO - Initializing Ultra-Performance Voice Agent...
2025-06-28 17:54:49,725 - core.stt_processor - INFO - Using CPU for STT processing
2025-06-28 17:54:49,725 - __main__ - INFO - Initialized 6 processing threads
2025-06-28 17:54:49,725 - __main__ - INFO - Testing system connectivity...
2025-06-28 17:54:49,725 - __main__ - INFO - System connectivity tests passed
2025-06-28 17:54:49,725 - __main__ - INFO - Ultra Voice Agent initialization completed
2025-06-28 17:54:49,725 - __main__ - INFO - Target latency: <800.0ms
2025-06-28 17:54:49,726 - __main__ - INFO - Starting Ultra Voice Agent...
2025-06-28 17:54:49,787 - core.audio_output - INFO - Available audio output devices:
2025-06-28 17:54:49,787 - core.audio_output - INFO -    2: Microsoft Sound Mapper - Output (2 ch)
2025-06-28 17:54:49,787 - core.audio_output - INFO -    3: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:54:49,787 - core.audio_output - INFO -    4: Z34G (HD Audio Driver for Displ (2 ch)
2025-06-28 17:54:49,787 - core.audio_output - INFO -    5: Realtek Digital Output (Realtek (2 ch)
2025-06-28 17:54:49,787 - core.audio_output - INFO -    6: Headset Earphone (Arctis 7 Chat (1 ch)
2025-06-28 17:54:49,787 - core.audio_output - INFO -    9: Primary Sound Driver (2 ch)
2025-06-28 17:54:49,787 - core.audio_output - INFO -    10: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:54:49,787 - core.audio_output - INFO -    11: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    12: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    13: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    14: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    15: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    16: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    17: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    20: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    21: Headphones (Realtek USB Audio) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    22: Speakers (Realtek USB Audio) (8 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    23: SPDIF Interface (Realtek USB Audio) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    27: Z34G (ACX HD Audio Speaker) (2 ch)
2025-06-28 17:54:49,788 - core.audio_output - INFO -    28: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 17:54:49,789 - core.audio_output - INFO - Using default output device: Headphones (Arctis 7 Game)
2025-06-28 17:54:49,797 - core.audio_output - INFO - Audio output initialized:
2025-06-28 17:54:49,797 - core.audio_output - INFO -    Device: 3
2025-06-28 17:54:49,797 - core.audio_output - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:54:49,797 - core.audio_output - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:54:49,797 - core.audio_output - INFO -    Channels: 1
2025-06-28 17:54:49,797 - core.audio_output - INFO - Audio output stream started
2025-06-28 17:54:49,797 - core.audio_output - INFO - Audio output processor started
2025-06-28 17:54:49,851 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-28 17:54:49,851 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\comtypes\gen'
2025-06-28 17:54:49,928 - core.tts_processor - ERROR - Voice configuration error: list index out of range
2025-06-28 17:54:49,928 - core.tts_processor - INFO - Using pyttsx3 TTS (fallback)
2025-06-28 17:54:49,928 - core.tts_processor - INFO - TTS engine initialized: pyttsx3
2025-06-28 17:54:49,928 - core.tts_processor - INFO - TTS processor started
2025-06-28 17:54:50,027 - core.stt_processor - INFO - Loading Whisper model: small on cpu
2025-06-28 17:54:50,128 - core.vad_processor - INFO - Loading Silero VAD model...
2025-06-28 17:54:50,166 - core.circuit_breaker - INFO - Circuit breaker 'ollama' initialized
2025-06-28 17:54:50,169 - core.vad_processor - INFO - Silero VAD model loaded successfully
2025-06-28 17:54:50,169 - core.vad_processor - INFO - Using Silero VAD (neural)
2025-06-28 17:54:50,169 - core.vad_processor - INFO - VAD processor started using silero method
2025-06-28 17:54:50,228 - core.audio_input - INFO - Available audio input devices:
2025-06-28 17:54:50,228 - core.audio_input - INFO -    0: Microsoft Sound Mapper - Input (2 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    1: Headset Microphone (Arctis 7 Ch (1 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    7: Primary Sound Capture Driver (2 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    8: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    18: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    19: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    24: Line In (Realtek USB Audio) (2 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    25: Microphone (Realtek USB Audio) (2 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO -    26: Stereo Mix (Realtek USB Audio) (2 ch)
2025-06-28 17:54:50,229 - core.audio_input - INFO - Using default input device: Headset Microphone (Arctis 7 Ch
2025-06-28 17:54:50,235 - core.audio_input - INFO - Audio stream initialized:
2025-06-28 17:54:50,235 - core.audio_input - INFO -    Device: 1
2025-06-28 17:54:50,235 - core.audio_input - INFO -    Sample Rate: 48000 Hz
2025-06-28 17:54:50,235 - core.audio_input - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 17:54:50,235 - core.audio_input - INFO -    Channels: 1
2025-06-28 17:54:50,236 - core.audio_input - INFO - Audio stream started
2025-06-28 17:54:50,236 - core.audio_input - INFO - Audio input thread processing started
2025-06-28 17:54:50,329 - __main__ - INFO - Ultra Voice Agent started successfully
2025-06-28 17:54:50,329 - __main__ - INFO - Ready for voice conversation!
2025-06-28 17:54:50,329 - __main__ - INFO - Voice conversation started
2025-06-28 17:54:50,330 - __main__ - INFO - Speak naturally - I'm listening!
2025-06-28 17:54:50,330 - __main__ - INFO - Press Ctrl+C to stop
2025-06-28 17:54:50,446 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 17:54:50,449 - core.ai_inference - ERROR - Error loading models: Event loop is closed
2025-06-28 17:54:50,449 - core.ai_inference - INFO - Model preference order: []...
2025-06-28 17:54:50,449 - core.ai_inference - INFO - Connected to Ollama at http://localhost:11434
2025-06-28 17:54:50,449 - core.ai_inference - INFO - Available models: 0
2025-06-28 17:54:50,449 - core.ai_inference - INFO - AI inference processor started
2025-06-28 17:54:50,742 - core.audio_input - WARNING - Audio FPS below target: 30.5 vs 62.5
2025-06-28 17:54:53,265 - core.stt_processor - INFO - Whisper model loaded:
2025-06-28 17:54:53,265 - core.stt_processor - INFO -    Model: small
2025-06-28 17:54:53,265 - core.stt_processor - INFO -    Device: cpu
2025-06-28 17:54:53,265 - core.stt_processor - INFO -    Compute: int8
2025-06-28 17:54:53,265 - core.stt_processor - INFO -    Memory: 244 MB
2025-06-28 17:54:53,265 - core.stt_processor - INFO - STT processor started
2025-06-28 18:10:11,157 - __main__ - WARNING - Failed to load config: Expecting value: line 1 column 1 (char 0), using defaults
2025-06-28 18:10:11,157 - core.performance_optimizer - INFO - Adaptive Performance Optimizer initialized
2025-06-28 18:10:11,158 - core.performance_optimizer - INFO - NLP Optimizer initialized
2025-06-28 18:10:11,158 - __main__ - INFO - Initializing Ultra-Performance Voice Agent...
2025-06-28 18:10:11,163 - __main__ - INFO - Initializing Qwen VL engine: qwen2.5vl:32b
2025-06-28 18:10:11,402 - core.circuit_breaker - INFO - Circuit breaker 'qwen_vl' initialized
2025-06-28 18:10:11,402 - core.qwen_vl_processor - INFO - Qwen VL Processor initialized: qwen2.5vl:32b
2025-06-28 18:10:11,402 - core.qwen_vl_processor - INFO - Ollama endpoint: http://localhost:11434
2025-06-28 18:10:11,402 - core.qwen_vl_processor - INFO - Vision support: True
2025-06-28 18:10:11,402 - core.qwen_vl_processor - INFO - Qwen VL Thread initialized
2025-06-28 18:10:11,402 - __main__ - INFO - Initialized 6 processing threads
2025-06-28 18:10:11,402 - __main__ - INFO - Testing system connectivity...
2025-06-28 18:10:11,402 - __main__ - INFO - System connectivity tests passed
2025-06-28 18:10:11,402 - __main__ - INFO - Ultra Voice Agent initialization completed
2025-06-28 18:10:11,402 - __main__ - INFO - Target latency: <800.0ms
2025-06-28 18:10:11,403 - __main__ - INFO - Starting Ultra Voice Agent...
2025-06-28 18:10:11,460 - core.audio_output - INFO - Available audio output devices:
2025-06-28 18:10:11,461 - core.audio_output - INFO -    2: Microsoft Sound Mapper - Output (2 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    3: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    4: Z34G (HD Audio Driver for Displ (2 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    5: Realtek Digital Output (Realtek (2 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    6: Headset Earphone (Arctis 7 Chat (1 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    9: Primary Sound Driver (2 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    10: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    11: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 18:10:11,461 - core.audio_output - INFO -    12: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 18:10:11,462 - core.audio_output - INFO -    13: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 18:10:11,462 - core.audio_output - INFO -    14: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 18:10:11,462 - core.audio_output - INFO -    15: Z34G (HD Audio Driver for Display Audio) (2 ch)
2025-06-28 18:10:11,462 - core.audio_output - INFO -    16: Realtek Digital Output (Realtek USB Audio) (2 ch)
2025-06-28 18:10:11,462 - core.audio_output - INFO -    17: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 18:10:11,462 - core.audio_output - INFO -    20: Headset Earphone (Arctis 7 Chat) (1 ch)
2025-06-28 18:10:11,462 - core.audio_output - INFO -    21: Headphones (Realtek USB Audio) (2 ch)
2025-06-28 18:10:11,463 - core.audio_output - INFO -    22: Speakers (Realtek USB Audio) (8 ch)
2025-06-28 18:10:11,463 - core.audio_output - INFO -    23: SPDIF Interface (Realtek USB Audio) (2 ch)
2025-06-28 18:10:11,463 - core.audio_output - INFO -    27: Z34G (ACX HD Audio Speaker) (2 ch)
2025-06-28 18:10:11,463 - core.audio_output - INFO -    28: Headphones (Arctis 7 Game) (2 ch)
2025-06-28 18:10:11,463 - core.audio_output - INFO - Using default output device: Headphones (Arctis 7 Game)
2025-06-28 18:10:11,471 - core.audio_output - INFO - Audio output initialized:
2025-06-28 18:10:11,471 - core.audio_output - INFO -    Device: 3
2025-06-28 18:10:11,471 - core.audio_output - INFO -    Sample Rate: 48000 Hz
2025-06-28 18:10:11,471 - core.audio_output - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 18:10:11,471 - core.audio_output - INFO -    Channels: 1
2025-06-28 18:10:11,471 - core.audio_output - INFO - Audio output stream started
2025-06-28 18:10:11,472 - core.audio_output - INFO - Audio output processor started
2025-06-28 18:10:11,540 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-28 18:10:11,541 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\comtypes\gen'
2025-06-28 18:10:11,619 - core.tts_processor - ERROR - Voice configuration error: list index out of range
2025-06-28 18:10:11,619 - core.tts_processor - INFO - Using pyttsx3 TTS (fallback)
2025-06-28 18:10:11,619 - core.tts_processor - INFO - TTS engine initialized: pyttsx3
2025-06-28 18:10:11,619 - core.tts_processor - INFO - TTS processor started
2025-06-28 18:10:11,705 - core.stt_processor - INFO - Loading Whisper model: small on cuda
2025-06-28 18:10:11,705 - core.stt_processor - INFO - GPU Configuration: compute_type=float16, cpu_threads=4
2025-06-28 18:10:11,806 - core.vad_processor - INFO - Loading Silero VAD model...
2025-06-28 18:10:11,849 - core.vad_processor - INFO - Silero VAD model loaded successfully
2025-06-28 18:10:11,849 - core.vad_processor - INFO - Using Silero VAD (neural)
2025-06-28 18:10:11,849 - core.vad_processor - INFO - VAD processor started using silero method
2025-06-28 18:10:11,886 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 18:10:11,905 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-06-28 18:10:11,906 - core.qwen_vl_processor - INFO - Model qwen2.5vl:32b is available
2025-06-28 18:10:11,906 - core.qwen_vl_processor - INFO - Qwen VL Processor initialized successfully
2025-06-28 18:10:11,906 - core.qwen_vl_processor - INFO - Qwen VL processor started and ready
2025-06-28 18:10:11,906 - core.audio_input - INFO - Available audio input devices:
2025-06-28 18:10:11,907 - core.audio_input - INFO -    0: Microsoft Sound Mapper - Input (2 ch)
2025-06-28 18:10:11,907 - core.audio_input - INFO -    1: Headset Microphone (Arctis 7 Ch (1 ch)
2025-06-28 18:10:11,907 - core.audio_input - INFO -    7: Primary Sound Capture Driver (2 ch)
2025-06-28 18:10:11,907 - core.audio_input - INFO -    8: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 18:10:11,907 - core.qwen_vl_processor - INFO - Qwen VL Processor cleaned up
2025-06-28 18:10:11,907 - core.audio_input - INFO -    18: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 18:10:11,907 - core.audio_input - INFO -    19: Headset Microphone (Arctis 7 Chat) (1 ch)
2025-06-28 18:10:11,907 - core.audio_input - INFO -    24: Line In (Realtek USB Audio) (2 ch)
2025-06-28 18:10:11,907 - core.audio_input - INFO -    25: Microphone (Realtek USB Audio) (2 ch)
2025-06-28 18:10:11,907 - core.audio_input - INFO -    26: Stereo Mix (Realtek USB Audio) (2 ch)
2025-06-28 18:10:11,908 - core.qwen_vl_processor - INFO - Qwen VL Thread stopped
2025-06-28 18:10:11,908 - core.audio_input - INFO - Using default input device: Headset Microphone (Arctis 7 Ch
2025-06-28 18:10:11,913 - core.audio_input - INFO - Audio stream initialized:
2025-06-28 18:10:11,913 - core.audio_input - INFO -    Device: 1
2025-06-28 18:10:11,913 - core.audio_input - INFO -    Sample Rate: 48000 Hz
2025-06-28 18:10:11,913 - core.audio_input - INFO -    Chunk Size: 768 samples (16.0ms)
2025-06-28 18:10:11,913 - core.audio_input - INFO -    Channels: 1
2025-06-28 18:10:11,914 - core.audio_input - INFO - Audio stream started
2025-06-28 18:10:11,914 - core.audio_input - INFO - Audio input thread processing started
2025-06-28 18:10:12,008 - core.performance_optimizer - INFO - Performance monitoring started
2025-06-28 18:10:12,008 - __main__ - INFO - Ultra Voice Agent started successfully
2025-06-28 18:10:12,008 - __main__ - INFO - Ready for voice conversation!
2025-06-28 18:10:12,008 - __main__ - INFO - Voice conversation started
2025-06-28 18:10:12,008 - __main__ - INFO - Speak naturally - I'm listening!
2025-06-28 18:10:12,008 - __main__ - INFO - Press Ctrl+C to stop
2025-06-28 18:10:12,230 - core.audio_input - WARNING - Audio FPS below target: 18.7 vs 62.5
2025-06-28 18:10:12,820 - core.stt_processor - INFO - Whisper model loaded:
2025-06-28 18:10:12,820 - core.stt_processor - INFO -    Model: small
2025-06-28 18:10:12,820 - core.stt_processor - INFO -    Device: cuda
2025-06-28 18:10:12,820 - core.stt_processor - INFO -    Compute: float16
2025-06-28 18:10:12,821 - core.stt_processor - INFO -    Memory: unknown MB
2025-06-28 18:10:12,821 - core.stt_processor - INFO - STT processor started
2025-06-28 18:10:13,147 - core.performance_optimizer - INFO - Performance monitoring stopped
2025-06-28 18:10:13,249 - __main__ - INFO -    Duration: 1.2 seconds
2025-06-28 18:10:13,249 - __main__ - INFO -    Exchanges: 0
2025-06-28 18:10:13,249 - __main__ - INFO -    Interruptions: 0
2025-06-28 18:10:13,249 - __main__ - INFO -    System Health: 50.0%

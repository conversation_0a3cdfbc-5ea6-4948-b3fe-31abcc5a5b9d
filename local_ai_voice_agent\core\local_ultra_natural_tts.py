"""
Local Ultra-Natural Text-to-Speech System
The most human-like voice synthesis using only LOCAL models
No external APIs required - everything runs on your machine
"""

import asyncio
import logging
import os
import tempfile
import time
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading
import queue

logger = logging.getLogger(__name__)


class LocalUltraNaturalTTS:
    """Ultra-natural TTS using only local models"""
    
    def __init__(self):
        self.current_engine = None
        self.available_engines = {}
        self.voice_settings = {
            "voice_id": "female_natural",
            "speed": 1.0,
            "pitch": 0.0,
            "emotion": "neutral"
        }
        
        # Initialize the best available local engine
        self.initialize_local_engines()
    
    def initialize_local_engines(self):
        """Initialize local TTS engines in order of quality"""
        logger.info("🎭 Initializing local ultra-natural voice system...")
        
        # Try engines in order of naturalness (all local)
        engines_to_try = [
            ("coqui_xtts", self._init_coqui_xtts),
            ("bark_tts", self._init_bark_tts),
            ("piper_tts", self._init_piper_tts),
            ("tortoise_tts", self._init_tortoise_tts),
            ("styletts2", self._init_styletts2),
            ("edge_tts_local", self._init_edge_tts_local),
            ("enhanced_pyttsx3", self._init_enhanced_pyttsx3)
        ]
        
        for engine_name, init_func in engines_to_try:
            try:
                logger.info(f"Trying {engine_name}...")
                if init_func():
                    self.current_engine = engine_name
                    logger.info(f"✓ Successfully initialized {engine_name}")
                    return True
            except Exception as e:
                logger.warning(f"Failed to initialize {engine_name}: {e}")
                continue
        
        logger.error("❌ No local ultra-natural TTS engines could be initialized")
        return False
    
    def _init_coqui_xtts(self) -> bool:
        """Initialize Coqui XTTS v2 (best local neural TTS)"""
        try:
            # Check if TTS is installed
            try:
                import TTS
                from TTS.api import TTS as CoquiTTS
            except ImportError:
                logger.info("Coqui TTS not installed. Installing...")
                # Install Coqui TTS
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "TTS"
                ])
                import TTS
                from TTS.api import TTS as CoquiTTS
            
            # Initialize XTTS v2 model
            self.coqui_tts = CoquiTTS("tts_models/multilingual/multi-dataset/xtts_v2")
            
            # Available voice samples (you can add your own)
            self.coqui_voices = {
                "female_natural": "female_voice.wav",  # Will use default if not found
                "male_natural": "male_voice.wav",
                "friendly_female": "friendly_female.wav",
                "calm_male": "calm_male.wav"
            }
            
            self.available_engines["coqui_xtts"] = {
                "name": "Coqui XTTS v2",
                "quality": "ultra_natural",
                "description": "Best local neural TTS with voice cloning capabilities"
            }
            
            logger.info("✓ Coqui XTTS v2 initialized successfully")
            return True
            
        except Exception as e:
            logger.warning(f"Coqui XTTS initialization failed: {e}")
            return False
    
    def _init_bark_tts(self) -> bool:
        """Initialize Bark TTS (generative audio with emotions)"""
        try:
            # Check if bark is installed
            try:
                from bark import SAMPLE_RATE, generate_audio, preload_models
                from scipy.io.wavfile import write as write_wav
            except ImportError:
                logger.info("Bark TTS not installed. Installing...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "git+https://github.com/suno-ai/bark.git"
                ])
                from bark import SAMPLE_RATE, generate_audio, preload_models
                from scipy.io.wavfile import write as write_wav
            
            # Preload models
            logger.info("Loading Bark models (this may take a moment)...")
            preload_models()
            
            # Available voice presets
            self.bark_voices = {
                "female_natural": "v2/en_speaker_6",     # Natural female
                "male_natural": "v2/en_speaker_9",       # Natural male
                "friendly_female": "v2/en_speaker_0",    # Friendly female
                "calm_male": "v2/en_speaker_7",          # Calm male
                "expressive_female": "v2/en_speaker_1",  # Expressive female
                "deep_male": "v2/en_speaker_8"           # Deep male
            }
            
            self.available_engines["bark_tts"] = {
                "name": "Bark TTS",
                "quality": "very_natural",
                "description": "Generative audio model with natural emotions and expressions"
            }
            
            logger.info("✓ Bark TTS initialized successfully")
            return True
            
        except Exception as e:
            logger.warning(f"Bark TTS initialization failed: {e}")
            return False
    
    def _init_piper_tts(self) -> bool:
        """Initialize Piper TTS (fast, high-quality local TTS)"""
        try:
            # Check if piper-tts is available
            try:
                import piper
            except ImportError:
                logger.info("Piper TTS not installed. Installing...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "piper-tts"
                ])
                import piper
            
            # Download a high-quality voice model
            self.piper_model_path = self._download_piper_model()
            
            if self.piper_model_path:
                self.available_engines["piper_tts"] = {
                    "name": "Piper TTS",
                    "quality": "natural",
                    "description": "Fast, high-quality local neural TTS"
                }
                
                logger.info("✓ Piper TTS initialized successfully")
                return True
            
            return False
            
        except Exception as e:
            logger.warning(f"Piper TTS initialization failed: {e}")
            return False
    
    def _init_tortoise_tts(self) -> bool:
        """Initialize Tortoise TTS (ultra-realistic but slower)"""
        try:
            # Tortoise requires manual setup
            logger.info("Tortoise TTS requires manual installation")
            return False
            
        except Exception as e:
            logger.warning(f"Tortoise TTS initialization failed: {e}")
            return False
    
    def _init_styletts2(self) -> bool:
        """Initialize StyleTTS2 (state-of-the-art local TTS)"""
        try:
            # StyleTTS2 requires manual setup
            logger.info("StyleTTS2 requires manual installation")
            return False
            
        except Exception as e:
            logger.warning(f"StyleTTS2 initialization failed: {e}")
            return False
    
    def _init_edge_tts_local(self) -> bool:
        """Initialize Edge TTS as local fallback"""
        try:
            import edge_tts
            
            # Enhanced voice selection
            self.edge_voices = {
                "female_natural": "en-US-AriaNeural",
                "male_natural": "en-US-GuyNeural",
                "friendly_female": "en-US-JennyNeural",
                "calm_male": "en-US-BrianNeural",
                "expressive_female": "en-US-MichelleNeural",
                "deep_male": "en-US-ChristopherNeural"
            }
            
            self.available_engines["edge_tts_local"] = {
                "name": "Edge TTS (Local)",
                "quality": "natural",
                "description": "Microsoft neural voices (requires internet for download)"
            }
            
            logger.info("✓ Edge TTS (local) initialized successfully")
            return True
            
        except ImportError:
            logger.info("Edge TTS not available")
            return False
        except Exception as e:
            logger.warning(f"Edge TTS initialization failed: {e}")
            return False
    
    def _init_enhanced_pyttsx3(self) -> bool:
        """Initialize enhanced pyttsx3 as final fallback"""
        try:
            import pyttsx3
            
            self.pyttsx3_engine = pyttsx3.init()
            
            # Get available voices
            voices = self.pyttsx3_engine.getProperty('voices')
            
            # Enhanced voice mapping
            self.pyttsx3_voices = {}
            for i, voice in enumerate(voices):
                if 'zira' in voice.name.lower() or 'aria' in voice.name.lower():
                    self.pyttsx3_voices["female_natural"] = voice.id
                elif 'david' in voice.name.lower() or 'mark' in voice.name.lower():
                    self.pyttsx3_voices["male_natural"] = voice.id
            
            # Set enhanced properties
            self.pyttsx3_engine.setProperty('rate', 180)  # Slightly faster
            self.pyttsx3_engine.setProperty('volume', 0.9)
            
            self.available_engines["enhanced_pyttsx3"] = {
                "name": "Enhanced pyttsx3",
                "quality": "basic",
                "description": "Enhanced Windows SAPI with better settings"
            }
            
            logger.info("✓ Enhanced pyttsx3 initialized successfully")
            return True
            
        except Exception as e:
            logger.warning(f"Enhanced pyttsx3 initialization failed: {e}")
            return False
    
    def _download_piper_model(self) -> Optional[str]:
        """Download a high-quality Piper voice model"""
        try:
            import requests
            
            # High-quality English voice model
            model_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/en/en_US/lessac/medium/en_US-lessac-medium.onnx"
            config_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/en/en_US/lessac/medium/en_US-lessac-medium.onnx.json"
            
            models_dir = Path("local_ai_voice_agent/models/piper")
            models_dir.mkdir(parents=True, exist_ok=True)
            
            model_path = models_dir / "en_US-lessac-medium.onnx"
            config_path = models_dir / "en_US-lessac-medium.onnx.json"
            
            if not model_path.exists():
                logger.info("Downloading Piper voice model...")
                
                # Download model
                response = requests.get(model_url)
                with open(model_path, 'wb') as f:
                    f.write(response.content)
                
                # Download config
                response = requests.get(config_url)
                with open(config_path, 'wb') as f:
                    f.write(response.content)
                
                logger.info("✓ Piper model downloaded successfully")
            
            return str(model_path)
            
        except Exception as e:
            logger.warning(f"Failed to download Piper model: {e}")
            return None
    
    async def speak_local_ultra_natural(self, text: str, emotion: str = "neutral", voice_id: str = None) -> bool:
        """Speak with local ultra-natural voice"""
        try:
            if not self.current_engine:
                logger.error("No local ultra-natural TTS engine available")
                return False
            
            # Enhance text for natural speech
            enhanced_text = self._enhance_text_for_local_speech(text, emotion)
            
            # Get voice parameters
            voice_params = self._get_local_voice_params(emotion)
            
            # Use the current engine
            if self.current_engine == "coqui_xtts":
                return await self._speak_coqui_xtts(enhanced_text, voice_params, voice_id)
            elif self.current_engine == "bark_tts":
                return await self._speak_bark(enhanced_text, voice_params, voice_id)
            elif self.current_engine == "piper_tts":
                return await self._speak_piper(enhanced_text, voice_params, voice_id)
            elif self.current_engine == "edge_tts_local":
                return await self._speak_edge_local(enhanced_text, voice_params, voice_id)
            elif self.current_engine == "enhanced_pyttsx3":
                return await self._speak_enhanced_pyttsx3(enhanced_text, voice_params, voice_id)
            
            return False
            
        except Exception as e:
            logger.error(f"Local ultra-natural TTS error: {e}")
            return False
    
    def _enhance_text_for_local_speech(self, text: str, emotion: str) -> str:
        """Enhance text for local ultra-natural speech"""
        import re
        
        # Clean up text
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
        text = re.sub(r'`.*?`', '', text)
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
        text = re.sub(r'\*([^*]+)\*', r'\1', text)
        
        # Add emotional context for local models
        if emotion == "happy":
            text = f"[Happy] {text}"
        elif emotion == "excited":
            text = f"[Excited] {text}"
        elif emotion == "calm":
            text = f"[Calm] {text}"
        elif emotion == "thoughtful":
            text = f"[Thoughtful] {text}"
        elif emotion == "friendly":
            text = f"[Friendly] {text}"
        
        # Add natural pauses
        text = re.sub(r',', ', ', text)
        text = re.sub(r'\.', '. ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _get_local_voice_params(self, emotion: str) -> Dict[str, Any]:
        """Get voice parameters for local engines"""
        emotional_params = {
            "happy": {"speed": 1.1, "pitch": 0.1, "energy": "high"},
            "excited": {"speed": 1.2, "pitch": 0.2, "energy": "very_high"},
            "calm": {"speed": 0.9, "pitch": -0.1, "energy": "low"},
            "thoughtful": {"speed": 0.8, "pitch": -0.05, "energy": "medium"},
            "friendly": {"speed": 1.0, "pitch": 0.05, "energy": "medium"},
            "neutral": {"speed": 1.0, "pitch": 0.0, "energy": "medium"}
        }
        
        return emotional_params.get(emotion, emotional_params["neutral"])
    
    async def _speak_coqui_xtts(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using Coqui XTTS v2"""
        try:
            # Select voice
            voice = voice_id or self.voice_settings["voice_id"]
            
            # Generate audio with XTTS
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                # Use default speaker if no custom voice file
                self.coqui_tts.tts_to_file(
                    text=text,
                    file_path=tmp_file.name,
                    speaker="Claribel Dervla",  # Default high-quality speaker
                    language="en"
                )
                
                # Play audio
                await self._play_audio_file(tmp_file.name)
                
                # Cleanup
                os.unlink(tmp_file.name)
            
            logger.info(f"🗣️ Josie (Coqui XTTS): {text}")
            return True
            
        except Exception as e:
            logger.error(f"Coqui XTTS error: {e}")
            return False
    
    async def _speak_bark(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using Bark TTS"""
        try:
            from bark import generate_audio, SAMPLE_RATE
            from scipy.io.wavfile import write as write_wav
            import numpy as np
            
            # Select voice preset
            voice = voice_id or self.bark_voices.get(self.voice_settings["voice_id"], "v2/en_speaker_6")
            
            # Generate audio
            audio_array = generate_audio(text, history_prompt=voice)
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                write_wav(tmp_file.name, SAMPLE_RATE, audio_array)
                
                # Play audio
                await self._play_audio_file(tmp_file.name)
                
                # Cleanup
                os.unlink(tmp_file.name)
            
            logger.info(f"🗣️ Josie (Bark): {text}")
            return True
            
        except Exception as e:
            logger.error(f"Bark TTS error: {e}")
            return False
    
    async def _speak_piper(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using Piper TTS"""
        try:
            # Use piper command line tool
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                cmd = [
                    "piper",
                    "--model", self.piper_model_path,
                    "--output_file", tmp_file.name
                ]
                
                # Run piper
                process = subprocess.Popen(
                    cmd,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                stdout, stderr = process.communicate(input=text)
                
                if process.returncode == 0:
                    # Play audio
                    await self._play_audio_file(tmp_file.name)
                    
                    # Cleanup
                    os.unlink(tmp_file.name)
                    
                    logger.info(f"🗣️ Josie (Piper): {text}")
                    return True
                else:
                    logger.error(f"Piper error: {stderr}")
                    return False
            
        except Exception as e:
            logger.error(f"Piper TTS error: {e}")
            return False
    
    async def _speak_edge_local(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using Edge TTS (local processing)"""
        try:
            import edge_tts
            
            # Select voice
            voice = voice_id or self.edge_voices.get(self.voice_settings["voice_id"], "en-US-AriaNeural")
            
            # Generate speech
            communicate = edge_tts.Communicate(text, voice)
            
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                tmp_file_path = tmp_file.name
            
            await communicate.save(tmp_file_path)
            await self._play_audio_file(tmp_file_path)
            
            # Cleanup
            os.unlink(tmp_file_path)
            
            logger.info(f"🗣️ Josie (Edge Local): {text}")
            return True
            
        except Exception as e:
            logger.error(f"Edge TTS local error: {e}")
            return False
    
    async def _speak_enhanced_pyttsx3(self, text: str, voice_params: Dict, voice_id: str = None) -> bool:
        """Speak using enhanced pyttsx3"""
        try:
            # Set voice
            voice = voice_id or self.pyttsx3_voices.get(self.voice_settings["voice_id"])
            if voice:
                self.pyttsx3_engine.setProperty('voice', voice)
            
            # Set emotional parameters
            speed = int(180 * voice_params.get("speed", 1.0))
            self.pyttsx3_engine.setProperty('rate', speed)
            
            # Speak
            self.pyttsx3_engine.say(text)
            self.pyttsx3_engine.runAndWait()
            
            logger.info(f"🗣️ Josie (Enhanced pyttsx3): {text}")
            return True
            
        except Exception as e:
            logger.error(f"Enhanced pyttsx3 error: {e}")
            return False
    
    async def _play_audio_file(self, file_path: str):
        """Play audio file with enhanced quality"""
        try:
            import pygame
            pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=512)
            pygame.mixer.init()
            
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.05)
            
            pygame.mixer.quit()
            
        except Exception as e:
            logger.error(f"Audio playback error: {e}")
    
    def get_local_voice_info(self) -> Dict[str, Any]:
        """Get local voice system information"""
        return {
            "current_engine": self.current_engine,
            "available_engines": self.available_engines,
            "voice_settings": self.voice_settings,
            "is_local": True,
            "requires_internet": self.current_engine == "edge_tts_local"
        }


# Convenience function
async def create_local_ultra_natural_tts() -> LocalUltraNaturalTTS:
    """Create and initialize local ultra-natural TTS engine"""
    engine = LocalUltraNaturalTTS()
    return engine

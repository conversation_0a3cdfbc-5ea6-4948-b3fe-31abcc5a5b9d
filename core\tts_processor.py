"""
Text-to-Speech (TTS) Processor
High-performance text-to-speech with multiple engine support
"""

import numpy as np
import time
import logging
from typing import Optional, Dict, Any, List
import threading
import tempfile
import os
import io

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False

try:
    import soundfile as sf
    SOUNDFILE_AVAILABLE = True
except ImportError:
    SOUNDFILE_AVAILABLE = False

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)

logger = logging.getLogger(__name__)


class TTSThread(HighPriorityThread):
    """Text-to-Speech processing thread"""
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 tts_engine: str = "pyttsx3",
                 voice_id: Optional[str] = None,
                 speech_rate: int = 200,
                 volume: float = 0.9):
        
        super().__init__("TTS", priority="high", target_fps=10)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # TTS configuration
        self.tts_engine = tts_engine
        self.voice_id = voice_id
        self.speech_rate = speech_rate
        self.volume = volume
        
        # TTS engines
        self.pyttsx3_engine = None
        self.engine_initialized = False
        
        # Performance tracking
        self.syntheses_processed = 0
        self.total_synthesis_time = 0
        self.failed_syntheses = 0
        self.total_text_length = 0
        
        # Audio processing
        self.temp_dir = tempfile.mkdtemp(prefix="tts_")
        self.sample_rate = 22050  # Standard TTS sample rate
        
        logger.info(f"TTS: Initialized (engine: {tts_engine}, rate: {speech_rate}, volume: {volume})")
    
    def initialize(self) -> bool:
        """Initialize TTS system"""
        try:
            if self.tts_engine == "pyttsx3":
                return self._initialize_pyttsx3()
            else:
                logger.error(f"ERROR: Unsupported TTS engine: {self.tts_engine}")
                return False
                
        except Exception as e:
            logger.error(f"FATAL: TTS initialization failed: {e}")
            return False
    
    def _initialize_pyttsx3(self) -> bool:
        """Initialize pyttsx3 TTS engine"""
        try:
            if not PYTTSX3_AVAILABLE:
                logger.error("FATAL: pyttsx3 not available")
                logger.error("Install with: pip install pyttsx3")
                return False
            
            # Initialize pyttsx3 engine
            self.pyttsx3_engine = pyttsx3.init()
            
            if self.pyttsx3_engine is None:
                logger.error("ERROR: Failed to initialize pyttsx3 engine")
                return False
            
            # Configure voice settings
            self._configure_pyttsx3_voice()
            
            # Set speech rate
            self.pyttsx3_engine.setProperty('rate', self.speech_rate)
            
            # Set volume
            self.pyttsx3_engine.setProperty('volume', self.volume)
            
            self.engine_initialized = True
            logger.info("TTS: pyttsx3 engine initialized successfully")
            
            # Test synthesis
            self._test_synthesis()
            
            return True
            
        except Exception as e:
            logger.error(f"ERROR: pyttsx3 initialization failed: {e}")
            return False
    
    def _configure_pyttsx3_voice(self):
        """Configure pyttsx3 voice"""
        try:
            voices = self.pyttsx3_engine.getProperty('voices')
            
            if not voices:
                logger.warning("WARNING: No voices available in pyttsx3")
                return
            
            logger.info(f"TTS: Available voices: {len(voices)}")
            for i, voice in enumerate(voices):
                logger.debug(f"Voice {i}: {voice.id} - {voice.name}")
            
            # Select voice
            if self.voice_id:
                # Try to find specific voice
                for voice in voices:
                    if self.voice_id in voice.id or self.voice_id in voice.name:
                        self.pyttsx3_engine.setProperty('voice', voice.id)
                        logger.info(f"TTS: Selected voice: {voice.name}")
                        return
                
                logger.warning(f"WARNING: Voice '{self.voice_id}' not found, using default")
            
            # Use default voice (usually first one)
            if voices:
                self.pyttsx3_engine.setProperty('voice', voices[0].id)
                logger.info(f"TTS: Using default voice: {voices[0].name}")
                
        except Exception as e:
            logger.error(f"ERROR: Voice configuration failed: {e}")
    
    def _test_synthesis(self):
        """Test TTS synthesis"""
        try:
            # Test with short phrase
            test_text = "TTS engine initialized successfully."
            
            # Create temporary file for test
            temp_file = os.path.join(self.temp_dir, "test.wav")
            
            # Synthesize to file
            self.pyttsx3_engine.save_to_file(test_text, temp_file)
            self.pyttsx3_engine.runAndWait()
            
            # Check if file was created
            if os.path.exists(temp_file):
                logger.debug("TTS: Test synthesis successful")
                os.remove(temp_file)
            else:
                logger.warning("WARNING: Test synthesis failed - no output file")
                
        except Exception as e:
            logger.warning(f"WARNING: TTS test synthesis failed: {e}")
    
    def run_processing_loop(self):
        """Main TTS processing loop"""
        logger.info("TTS: Processing loop started")
        
        while not self.should_stop:
            try:
                # Get AI response text
                ai_result = self.input_queue.get_nowait()
                if ai_result is None:
                    time.sleep(0.01)  # Brief pause if no data
                    continue
                
                # Process text for speech synthesis
                start_time = time.time()
                tts_result = self._synthesize_speech(ai_result)
                processing_time = time.time() - start_time
                
                # Send result if synthesis successful
                if tts_result:
                    success = self.output_queue.put_nowait(tts_result)
                    if not success:
                        logger.warning("WARNING: TTS output queue full")
                
                # Update performance stats
                self.update_performance_stats(processing_time)
                
            except Exception as e:
                logger.error(f"ERROR: TTS processing error: {e}")
                time.sleep(0.01)
    
    def _synthesize_speech(self, ai_result: ProcessingResult) -> Optional[ProcessingResult]:
        """Synthesize speech from text"""
        try:
            if not self.engine_initialized:
                logger.error("ERROR: TTS engine not initialized")
                return None
            
            text = ai_result.data['text']
            
            # Skip empty text
            if not text or not text.strip():
                logger.debug("TTS: Skipping empty text")
                return None
            
            # Clean text for TTS
            cleaned_text = self._clean_text_for_tts(text)
            
            if not cleaned_text:
                logger.debug("TTS: No text after cleaning")
                return None
            
            # Synthesize speech
            start_time = time.time()
            audio_data = self._generate_speech_audio(cleaned_text)
            synthesis_time = time.time() - start_time
            
            if audio_data is not None:
                result = ProcessingResult(
                    data={
                        'audio_data': audio_data,
                        'sample_rate': self.sample_rate,
                        'text': cleaned_text,
                        'original_text': text,
                        'synthesis_time': synthesis_time,
                        'tts_engine': self.tts_engine,
                        'speech_rate': self.speech_rate,
                        'volume': self.volume
                    },
                    timestamp=time.time(),
                    processing_time_ms=synthesis_time * 1000,
                    metadata={
                        'source': 'tts',
                        'text_length': len(cleaned_text),
                        'audio_duration': len(audio_data) / self.sample_rate if len(audio_data) > 0 else 0
                    }
                )
                
                self.syntheses_processed += 1
                self.total_synthesis_time += synthesis_time
                self.total_text_length += len(cleaned_text)
                
                logger.info(f"TTS: '{cleaned_text[:50]}...' -> {len(audio_data)} samples ({synthesis_time:.2f}s)")
                return result
            else:
                self.failed_syntheses += 1
                logger.error("ERROR: Speech synthesis failed")
                return None
                
        except Exception as e:
            logger.error(f"ERROR: Speech synthesis error: {e}")
            self.failed_syntheses += 1
            return None
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Clean text for TTS synthesis"""
        try:
            # Remove extra whitespace
            cleaned = ' '.join(text.split())
            
            # Remove or replace problematic characters
            replacements = {
                '"': '',
                '"': '',
                '"': '',
                ''': "'",
                ''': "'",
                '…': '...',
                '–': '-',
                '—': '-',
                '\n': ' ',
                '\r': ' ',
                '\t': ' '
            }
            
            for old, new in replacements.items():
                cleaned = cleaned.replace(old, new)
            
            # Remove multiple spaces
            while '  ' in cleaned:
                cleaned = cleaned.replace('  ', ' ')
            
            return cleaned.strip()
            
        except Exception as e:
            logger.error(f"ERROR: Text cleaning failed: {e}")
            return text
    
    def _generate_speech_audio(self, text: str) -> Optional[np.ndarray]:
        """Generate speech audio from text"""
        try:
            if self.tts_engine == "pyttsx3":
                return self._generate_pyttsx3_audio(text)
            else:
                logger.error(f"ERROR: Unsupported TTS engine: {self.tts_engine}")
                return None
                
        except Exception as e:
            logger.error(f"ERROR: Audio generation failed: {e}")
            return None
    
    def _generate_pyttsx3_audio(self, text: str) -> Optional[np.ndarray]:
        """Generate audio using pyttsx3"""
        try:
            # Create temporary file
            temp_file = os.path.join(self.temp_dir, f"tts_{int(time.time() * 1000)}.wav")
            
            # Synthesize to file
            self.pyttsx3_engine.save_to_file(text, temp_file)
            self.pyttsx3_engine.runAndWait()
            
            # Load audio file
            if os.path.exists(temp_file):
                try:
                    if SOUNDFILE_AVAILABLE:
                        audio_data, sample_rate = sf.read(temp_file)
                        
                        # Convert to mono if stereo
                        if len(audio_data.shape) > 1:
                            audio_data = np.mean(audio_data, axis=1)
                        
                        # Ensure float32
                        audio_data = audio_data.astype(np.float32)
                        
                        # Update sample rate
                        self.sample_rate = sample_rate
                        
                    else:
                        # Fallback: create dummy audio (silence)
                        duration = len(text) * 0.1  # Rough estimate: 0.1s per character
                        audio_data = np.zeros(int(self.sample_rate * duration), dtype=np.float32)
                        logger.warning("WARNING: Using dummy audio - install soundfile for real TTS")
                    
                    # Clean up temp file
                    os.remove(temp_file)
                    
                    return audio_data
                    
                except Exception as e:
                    logger.error(f"ERROR: Failed to load audio file: {e}")
                    # Clean up temp file
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                    return None
            else:
                logger.error("ERROR: TTS did not create output file")
                return None
                
        except Exception as e:
            logger.error(f"ERROR: pyttsx3 audio generation failed: {e}")
            return None
    
    def get_tts_stats(self) -> Dict[str, Any]:
        """Get TTS statistics"""
        avg_synthesis_time = 0
        avg_text_length = 0
        chars_per_second = 0
        
        if self.syntheses_processed > 0:
            avg_synthesis_time = self.total_synthesis_time / self.syntheses_processed
            avg_text_length = self.total_text_length / self.syntheses_processed
            
            if self.total_synthesis_time > 0:
                chars_per_second = self.total_text_length / self.total_synthesis_time
        
        return {
            'tts_engine': self.tts_engine,
            'voice_id': self.voice_id,
            'speech_rate': self.speech_rate,
            'volume': self.volume,
            'engine_initialized': self.engine_initialized,
            'syntheses_processed': self.syntheses_processed,
            'failed_syntheses': self.failed_syntheses,
            'success_rate': self.syntheses_processed / max(1, self.syntheses_processed + self.failed_syntheses),
            'avg_synthesis_time': avg_synthesis_time,
            'avg_text_length': avg_text_length,
            'chars_per_second': chars_per_second,
            'sample_rate': self.sample_rate
        }
    
    def cleanup(self):
        """Cleanup TTS resources"""
        try:
            # Stop pyttsx3 engine
            if self.pyttsx3_engine:
                try:
                    self.pyttsx3_engine.stop()
                except:
                    pass
                self.pyttsx3_engine = None
            
            # Clean up temporary directory
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                logger.debug("TTS: Temporary directory cleaned up")
                
        except Exception as e:
            logger.error(f"ERROR: TTS cleanup error: {e}")


# Test function
def test_tts():
    """Test TTS functionality"""
    print("Testing TTS...")
    
    if not PYTTSX3_AVAILABLE:
        print("ERROR: pyttsx3 not available - cannot test TTS")
        return
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="TTSInputTest")
    output_queue = LockFreeQueue(maxsize=100, name="TTSOutputTest")
    
    # Create TTS thread
    tts = TTSThread(
        input_queue=input_queue,
        output_queue=output_queue,
        tts_engine="pyttsx3",
        speech_rate=150
    )
    
    # Test initialization
    if tts.initialize():
        print("SUCCESS: TTS initialized")
        
        # Test text cleaning
        test_text = "Hello, this is a test of the TTS system!"
        cleaned = tts._clean_text_for_tts(test_text)
        print(f"Text cleaned: '{test_text}' -> '{cleaned}'")
        
        tts.cleanup()
        print("SUCCESS: TTS test completed")
    else:
        print("ERROR: TTS initialization failed")


if __name__ == "__main__":
    test_tts()

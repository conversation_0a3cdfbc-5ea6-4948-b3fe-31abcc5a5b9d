"""
🗣️ Text-to-Speech (TTS) Processor
Ultra-fast neural speech synthesis with <100ms latency
"""

import numpy as np
import time
import threading
from typing import Optional, Dict, List, Any, Tuple
import logging
import tempfile
import os
from pathlib import Path
import io

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    print("⚠️ pyttsx3 not available. Install with: pip install pyttsx3")

try:
    import torch
    import torchaudio
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️ PyTorch not available. Advanced TTS models disabled.")

# Future imports for advanced TTS (optional)
try:
    # These would be installed separately
    from f5_tts import F5TTS
    F5_TTS_AVAILABLE = True
except ImportError:
    F5_TTS_AVAILABLE = False

try:
    from chattts import ChatTTS
    CHAT_TTS_AVAILABLE = True
except ImportError:
    CHAT_TTS_AVAILABLE = False

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)

logger = logging.getLogger(__name__)

class TTSThread(HighPriorityThread):
    """
    High-priority Text-to-Speech processing thread
    - Multiple TTS engine support (pyttsx3, F5-TTS, ChatTTS)
    - Streaming sentence-level synthesis
    - Voice customization and emotion control
    - Optimized for minimal latency
    """
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 tts_engine: str = "auto",  # "pyttsx3", "f5", "chat", "auto"
                 voice_settings: Optional[Dict] = None,
                 chunk_sentences: bool = True,
                 target_sample_rate: int = 48000):
        
        super().__init__("TTSProcessor", priority="high", target_fps=100)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # TTS configuration
        self.tts_engine_name = tts_engine
        self.voice_settings = voice_settings or self._get_default_voice_settings()
        self.chunk_sentences = chunk_sentences
        self.target_sample_rate = target_sample_rate
        
        # TTS engines
        self.active_tts_engine = None
        self.pyttsx3_engine = None
        self.f5_tts_engine = None
        self.chat_tts_engine = None
        
        # Sentence processing
        self.sentence_splitter = SentenceSplitter()
        
        # Performance optimization
        self.synthesis_cache = {}
        self.cache_size_limit = 200
        
        # Statistics
        self.sentences_synthesized = 0
        self.synthesis_failures = 0
        self.total_synthesis_time = 0.0
        self.total_audio_generated = 0.0
        
        # Audio generation tracking
        self.generation_times = []
        
    def _get_default_voice_settings(self) -> Dict:
        """Get default voice settings"""
        return {
            'rate': 180,        # Words per minute
            'volume': 0.9,      # Volume level (0.0-1.0)
            'voice_id': None,   # Auto-select best voice
            'pitch': 0,         # Pitch adjustment
            'emotion': 'neutral', # Emotion for advanced TTS
            'speed': 1.0,       # Speed multiplier
            'quality': 'high'   # Quality setting
        }
    
    def initialize_tts_engines(self) -> bool:
        """Initialize TTS engines based on availability"""
        try:
            if self.tts_engine_name == "auto":
                # Auto-select best available engine
                if self._initialize_advanced_tts():
                    pass  # Advanced TTS initialized
                elif self._initialize_pyttsx3():
                    self.tts_engine_name = "pyttsx3"
                    logger.info("✅ Using pyttsx3 TTS (fallback)")
                else:
                    logger.error("❌ No TTS engines available")
                    return False
            elif self.tts_engine_name == "pyttsx3":
                if not self._initialize_pyttsx3():
                    return False
            elif self.tts_engine_name == "f5":
                if not self._initialize_f5_tts():
                    return False
            elif self.tts_engine_name == "chat":
                if not self._initialize_chat_tts():
                    return False
            else:
                logger.error(f"❌ Unknown TTS engine: {self.tts_engine_name}")
                return False
            
            logger.info(f"✅ TTS engine initialized: {self.tts_engine_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ TTS initialization error: {e}")
            return False
    
    def _initialize_advanced_tts(self) -> bool:
        """Try to initialize advanced TTS engines"""
        # Try F5-TTS first (highest quality)
        if F5_TTS_AVAILABLE and self._initialize_f5_tts():
            self.tts_engine_name = "f5"
            logger.info("✅ Using F5-TTS (premium neural)")
            return True
        
        # Try ChatTTS (good for conversation)
        if CHAT_TTS_AVAILABLE and self._initialize_chat_tts():
            self.tts_engine_name = "chat"
            logger.info("✅ Using ChatTTS (conversational)")
            return True
        
        return False
    
    def _initialize_pyttsx3(self) -> bool:
        """Initialize pyttsx3 TTS engine"""
        if not PYTTSX3_AVAILABLE:
            return False
        
        try:
            self.pyttsx3_engine = pyttsx3.init()
            
            # Configure voice settings
            self._configure_pyttsx3_voice()
            
            self.active_tts_engine = "pyttsx3"
            return True
            
        except Exception as e:
            logger.error(f"❌ pyttsx3 initialization error: {e}")
            return False
    
    def _configure_pyttsx3_voice(self):
        """Configure pyttsx3 voice settings"""
        try:
            # Set rate
            self.pyttsx3_engine.setProperty('rate', self.voice_settings['rate'])
            
            # Set volume
            self.pyttsx3_engine.setProperty('volume', self.voice_settings['volume'])
            
            # Select best voice
            voices = self.pyttsx3_engine.getProperty('voices')
            if voices:
                selected_voice = self._select_best_voice(voices)
                if selected_voice:
                    self.pyttsx3_engine.setProperty('voice', selected_voice.id)
                    logger.info(f"🎤 Selected voice: {selected_voice.name}")
            
        except Exception as e:
            logger.error(f"❌ Voice configuration error: {e}")
    
    def _select_best_voice(self, voices) -> Optional[Any]:
        """Select the best available voice"""
        if not voices:
            return None
        
        # Preference order: female, English, high quality
        preferred_voices = []
        
        for voice in voices:
            voice_name = voice.name.lower()
            voice_lang = getattr(voice, 'languages', [''])[0].lower() if hasattr(voice, 'languages') else ''
            
            score = 0
            
            # Prefer female voices
            if any(term in voice_name for term in ['female', 'woman', 'zira', 'hazel', 'susan']):
                score += 3
            
            # Prefer English voices
            if 'en' in voice_lang or any(term in voice_name for term in ['english', 'us', 'uk']):
                score += 2
            
            # Prefer high-quality voices
            if any(term in voice_name for term in ['high', 'premium', 'enhanced']):
                score += 1
            
            preferred_voices.append((voice, score))
        
        # Sort by score and return best
        preferred_voices.sort(key=lambda x: x[1], reverse=True)
        return preferred_voices[0][0] if preferred_voices else voices[0]
    
    def _initialize_f5_tts(self) -> bool:
        """Initialize F5-TTS engine (premium neural TTS)"""
        if not F5_TTS_AVAILABLE or not TORCH_AVAILABLE:
            return False
        
        try:
            # Initialize F5-TTS model
            self.f5_tts_engine = F5TTSEngine(
                sample_rate=self.target_sample_rate,
                voice_settings=self.voice_settings
            )
            
            if self.f5_tts_engine.initialize():
                self.active_tts_engine = "f5"
                return True
            
        except Exception as e:
            logger.error(f"❌ F5-TTS initialization error: {e}")
        
        return False
    
    def _initialize_chat_tts(self) -> bool:
        """Initialize ChatTTS engine (conversational TTS)"""
        if not CHAT_TTS_AVAILABLE or not TORCH_AVAILABLE:
            return False
        
        try:
            # Initialize ChatTTS model
            self.chat_tts_engine = ChatTTSEngine(
                sample_rate=self.target_sample_rate,
                voice_settings=self.voice_settings
            )
            
            if self.chat_tts_engine.initialize():
                self.active_tts_engine = "chat"
                return True
            
        except Exception as e:
            logger.error(f"❌ ChatTTS initialization error: {e}")
        
        return False
    
    def process_loop(self):
        """Main TTS processing loop"""
        if not self.initialize_tts_engines():
            logger.error("❌ Failed to initialize TTS engines")
            return
        
        logger.info("🗣️ TTS processor started")
        
        try:
            while self.running.is_set() and not self.shutdown_event.is_set():
                self._process_text_input()
                
        except Exception as e:
            logger.error(f"❌ TTS processing error: {e}")
        finally:
            self._cleanup_tts_engines()
            logger.info("🛑 TTS processor stopped")
    
    def _process_text_input(self):
        """Process text input from AI"""
        # Get AI response
        ai_result = self.input_queue.get_nowait()
        if ai_result is None:
            time.sleep(0.005)  # 5ms sleep when no data
            return
        
        start_time = time.time()
        
        try:
            # Extract text from AI response
            ai_response = ai_result.data
            if not isinstance(ai_response, dict) or 'text' not in ai_response:
                logger.warning("⚠️ Invalid AI response data")
                return
            
            response_text = ai_response['text'].strip()
            if not response_text:
                logger.debug("⚠️ Empty text from AI")
                return
            
            logger.debug(f"🗣️ Synthesizing: '{response_text[:50]}...'")
            
            # Process text based on chunking strategy
            if self.chunk_sentences:
                self._process_text_by_sentences(ai_result, response_text)
            else:
                self._process_complete_text(ai_result, response_text)
            
            # Performance tracking
            processing_time = time.time() - start_time
            self.update_performance_stats(processing_time)
            
        except Exception as e:
            logger.error(f"❌ Error processing text input: {e}")
            self.synthesis_failures += 1
    
    def _process_text_by_sentences(self, ai_result: ProcessingResult, text: str):
        """Process text sentence by sentence for streaming"""
        sentences = self.sentence_splitter.split_text(text)
        
        for i, sentence in enumerate(sentences):
            if sentence.strip():
                audio_data = self._synthesize_sentence(sentence.strip())
                
                if audio_data is not None:
                    # Send audio chunk immediately
                    self._send_audio_chunk(ai_result, audio_data, sentence, i, len(sentences))
                else:
                    self.synthesis_failures += 1
    
    def _process_complete_text(self, ai_result: ProcessingResult, text: str):
        """Process complete text as one audio chunk"""
        audio_data = self._synthesize_text(text)
        
        if audio_data is not None:
            self._send_audio_chunk(ai_result, audio_data, text, 0, 1)
        else:
            self.synthesis_failures += 1
    
    def _synthesize_sentence(self, sentence: str) -> Optional[np.ndarray]:
        """Synthesize a single sentence"""
        # Check cache first
        cache_key = self._generate_cache_key(sentence)
        if cache_key in self.synthesis_cache:
            logger.debug("💾 Using cached audio")
            return self.synthesis_cache[cache_key]
        
        # Synthesize new audio
        audio_data = self._synthesize_text(sentence)
        
        # Cache result
        if audio_data is not None and len(self.synthesis_cache) < self.cache_size_limit:
            self.synthesis_cache[cache_key] = audio_data
        
        return audio_data
    
    def _synthesize_text(self, text: str) -> Optional[np.ndarray]:
        """Synthesize text to audio using active TTS engine"""
        synthesis_start = time.time()
        
        try:
            if self.active_tts_engine == "pyttsx3":
                audio_data = self._synthesize_with_pyttsx3(text)
            elif self.active_tts_engine == "f5":
                audio_data = self._synthesize_with_f5(text)
            elif self.active_tts_engine == "chat":
                audio_data = self._synthesize_with_chat(text)
            else:
                logger.error(f"❌ Unknown active TTS engine: {self.active_tts_engine}")
                return None
            
            if audio_data is not None:
                synthesis_time = time.time() - synthesis_start
                self.generation_times.append(synthesis_time)
                if len(self.generation_times) > 100:
                    self.generation_times = self.generation_times[-100:]
                
                self.sentences_synthesized += 1
                self.total_synthesis_time += synthesis_time
                self.total_audio_generated += len(audio_data) / self.target_sample_rate
                
                logger.debug(f"✅ Synthesis completed in {synthesis_time:.3f}s")
            
            return audio_data
            
        except Exception as e:
            logger.error(f"❌ Text synthesis error: {e}")
            return None
    
    def _synthesize_with_pyttsx3(self, text: str) -> Optional[np.ndarray]:
        """Synthesize using pyttsx3 (system TTS)"""
        try:
            # Create temporary file for audio output
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                # Save to file
                self.pyttsx3_engine.save_to_file(text, temp_path)
                self.pyttsx3_engine.runAndWait()
                
                # Load audio file
                if os.path.exists(temp_path):
                    audio_data = self._load_audio_file(temp_path)
                    return audio_data
                else:
                    logger.error("❌ pyttsx3 failed to generate audio file")
                    return None
                    
            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            logger.error(f"❌ pyttsx3 synthesis error: {e}")
            return None
    
    def _synthesize_with_f5(self, text: str) -> Optional[np.ndarray]:
        """Synthesize using F5-TTS"""
        if self.f5_tts_engine:
            return self.f5_tts_engine.synthesize(text)
        return None
    
    def _synthesize_with_chat(self, text: str) -> Optional[np.ndarray]:
        """Synthesize using ChatTTS"""
        if self.chat_tts_engine:
            return self.chat_tts_engine.synthesize(text)
        return None
    
    def _load_audio_file(self, file_path: str) -> Optional[np.ndarray]:
        """Load audio file and convert to target format"""
        try:
            if TORCH_AVAILABLE:
                # Use torchaudio for loading
                waveform, sample_rate = torchaudio.load(file_path)
                
                # Convert to mono
                if waveform.shape[0] > 1:
                    waveform = torch.mean(waveform, dim=0, keepdim=True)
                
                # Resample if needed
                if sample_rate != self.target_sample_rate:
                    resampler = torchaudio.transforms.Resample(sample_rate, self.target_sample_rate)
                    waveform = resampler(waveform)
                
                # Convert to numpy
                audio_data = waveform.squeeze().numpy().astype(np.float32)
                return audio_data
            else:
                # Fallback: try with basic file reading (limited format support)
                logger.warning("⚠️ PyTorch not available - limited audio format support")
                return None
                
        except Exception as e:
            logger.error(f"❌ Audio file loading error: {e}")
            return None
    
    def _send_audio_chunk(self, 
                         ai_result: ProcessingResult, 
                         audio_data: np.ndarray, 
                         text_chunk: str,
                         chunk_index: int,
                         total_chunks: int):
        """Send synthesized audio chunk to output"""
        try:
            # Create audio result
            audio_result = {
                'audio_data': audio_data,
                'sample_rate': self.target_sample_rate,
                'text': text_chunk,
                'duration': len(audio_data) / self.target_sample_rate,
                'chunk_index': chunk_index,
                'total_chunks': total_chunks,
                'tts_engine': self.active_tts_engine,
                'synthesis_time': self.generation_times[-1] if self.generation_times else 0.0
            }
            
            result = ProcessingResult(
                data=audio_result,
                timestamp=ai_result.timestamp,
                latency=time.time() - ai_result.timestamp,
                confidence=0.95,  # TTS confidence
                stage="TTS",
                chunk_id=ai_result.chunk_id
            )
            
            if not self.output_queue.put_nowait(result):
                logger.warning("⚠️ TTS output queue full, dropping audio")
                self.synthesis_failures += 1
            else:
                logger.debug(f"📤 Audio chunk sent: {chunk_index+1}/{total_chunks} ({len(audio_data)} samples)")
                
        except Exception as e:
            logger.error(f"❌ Error sending audio chunk: {e}")
            self.synthesis_failures += 1
    
    def _generate_cache_key(self, text: str) -> str:
        """Generate cache key for text"""
        # Include TTS engine and voice settings in key
        import hashlib
        
        key_data = f"{text}_{self.active_tts_engine}_{self.voice_settings['rate']}_{self.voice_settings['voice_id']}"
        return hashlib.md5(key_data.encode()).hexdigest()[:16]
    
    def _cleanup_tts_engines(self):
        """Clean up TTS resources"""
        logger.info("🧹 Cleaning up TTS engines...")
        
        if self.f5_tts_engine:
            try:
                self.f5_tts_engine.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning F5-TTS: {e}")
        
        if self.chat_tts_engine:
            try:
                self.chat_tts_engine.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning ChatTTS: {e}")
        
        # pyttsx3 doesn't need explicit cleanup
        
        logger.info("✅ TTS cleanup completed")
    
    def get_tts_stats(self) -> Dict[str, Any]:
        """Get detailed TTS statistics"""
        stats = self.get_performance_report()
        
        # Calculate synthesis metrics
        if self.sentences_synthesized > 0:
            avg_synthesis_time = self.total_synthesis_time / self.sentences_synthesized
            synthesis_throughput = self.total_audio_generated / self.total_synthesis_time if self.total_synthesis_time > 0 else 0
        else:
            avg_synthesis_time = 0
            synthesis_throughput = 0
        
        stats.update({
            'active_tts_engine': self.active_tts_engine,
            'voice_settings': self.voice_settings,
            'sentences_synthesized': self.sentences_synthesized,
            'synthesis_failures': self.synthesis_failures,
            'success_rate': (self.sentences_synthesized / max(self.sentences_synthesized + self.synthesis_failures, 1)) * 100,
            'avg_synthesis_time': avg_synthesis_time,
            'total_audio_generated_seconds': self.total_audio_generated,
            'synthesis_throughput_ratio': synthesis_throughput,
            'cache_size': len(self.synthesis_cache),
            'recent_generation_times': self.generation_times[-10:] if self.generation_times else [],
            'queue_input_stats': self.input_queue.get_stats(),
            'queue_output_stats': self.output_queue.get_stats()
        })
        
        return stats

class SentenceSplitter:
    """
    Intelligent sentence splitting for streaming TTS
    - Handles common abbreviations
    - Preserves meaning boundaries
    - Optimized for speech output
    """
    
    def __init__(self):
        # Common abbreviations that shouldn't split sentences
        self.abbreviations = {
            'mr.', 'mrs.', 'ms.', 'dr.', 'prof.', 'sr.', 'jr.',
            'vs.', 'etc.', 'inc.', 'ltd.', 'corp.', 'co.',
            'u.s.', 'u.k.', 'e.g.', 'i.e.', 'a.m.', 'p.m.'
        }
    
    def split_text(self, text: str) -> List[str]:
        """Split text into sentences optimized for TTS"""
        if not text.strip():
            return []
        
        # Simple sentence splitting with abbreviation handling
        sentences = []
        current_sentence = ""
        
        words = text.split()
        
        for i, word in enumerate(words):
            current_sentence += word + " "
            
            # Check for sentence endings
            if word.endswith(('.', '!', '?', ':')):
                # Check if it's an abbreviation
                if word.lower() not in self.abbreviations:
                    # Check if next word starts with capital (if exists)
                    if i + 1 < len(words):
                        next_word = words[i + 1]
                        if next_word[0].isupper() or next_word.lower() in ['and', 'but', 'or', 'so']:
                            # End of sentence
                            sentences.append(current_sentence.strip())
                            current_sentence = ""
                    else:
                        # End of text
                        sentences.append(current_sentence.strip())
                        current_sentence = ""
        
        # Add remaining text as final sentence
        if current_sentence.strip():
            sentences.append(current_sentence.strip())
        
        # Filter out very short sentences (less than 3 characters)
        sentences = [s for s in sentences if len(s.strip()) >= 3]
        
        return sentences

# Advanced TTS Engine Classes (placeholders for future implementation)
class F5TTSEngine:
    """F5-TTS engine wrapper for premium neural synthesis"""
    
    def __init__(self, sample_rate: int = 48000, voice_settings: Dict = None):
        self.sample_rate = sample_rate
        self.voice_settings = voice_settings or {}
        self.model = None
        
    def initialize(self) -> bool:
        """Initialize F5-TTS model"""
        # Placeholder - would implement actual F5-TTS loading
        logger.info("📥 F5-TTS initialization (placeholder)")
        return False  # Not implemented yet
    
    def synthesize(self, text: str) -> Optional[np.ndarray]:
        """Synthesize text using F5-TTS"""
        # Placeholder for F5-TTS synthesis
        return None
    
    def cleanup(self):
        """Clean up F5-TTS resources"""
        pass

class ChatTTSEngine:
    """ChatTTS engine wrapper for conversational synthesis"""
    
    def __init__(self, sample_rate: int = 48000, voice_settings: Dict = None):
        self.sample_rate = sample_rate
        self.voice_settings = voice_settings or {}
        self.model = None
        
    def initialize(self) -> bool:
        """Initialize ChatTTS model"""
        # Placeholder - would implement actual ChatTTS loading
        logger.info("📥 ChatTTS initialization (placeholder)")
        return False  # Not implemented yet
    
    def synthesize(self, text: str) -> Optional[np.ndarray]:
        """Synthesize text using ChatTTS"""
        # Placeholder for ChatTTS synthesis
        return None
    
    def cleanup(self):
        """Clean up ChatTTS resources"""
        pass

# Test function
def test_tts_processor():
    """Test TTS processor functionality"""
    print("🧪 Testing TTS Processor...")
    
    if not PYTTSX3_AVAILABLE:
        print("❌ pyttsx3 not available - cannot test TTS")
        return
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="TTSInput")
    output_queue = LockFreeQueue(maxsize=100, name="TTSOutput")
    
    # Create TTS processor
    tts_processor = TTSThread(
        input_queue=input_queue,
        output_queue=output_queue,
        tts_engine="pyttsx3",  # Use reliable pyttsx3 for testing
        chunk_sentences=True
    )
    
    try:
        # Start TTS processor
        tts_processor.start()
        
        # Wait for initialization
        time.sleep(2)
        
        # Test messages
        test_messages = [
            "Hello, I am your AI voice assistant.",
            "This is a test of the text-to-speech system. How does it sound?",
            "Testing sentence chunking. This should be split into multiple audio chunks.",
            "Short test."
        ]
        
        print("🗣️ Generating speech for test messages...")
        
        for i, message in enumerate(test_messages):
            # Create mock AI response
            ai_response = {
                'text': message,
                'model': 'test_model',
                'confidence': 0.95,
                'timestamp': time.time()
            }
            
            ai_result = ProcessingResult(
                data=ai_response,
                timestamp=time.time(),
                latency=0.1,
                confidence=0.95,
                stage="AI",
                chunk_id=i
            )
            
            input_queue.put_nowait(ai_result)
            print(f"   Sent: '{message[:30]}...'")
        
        # Wait for processing
        time.sleep(5)
        
        # Check results
        audio_chunks = 0
        while True:
            result = output_queue.get_nowait()
            if result is None:
                break
            audio_chunks += 1
            audio_data = result.data
            duration = audio_data.get('duration', 0)
            print(f"   Audio chunk {audio_chunks}: {duration:.2f}s, engine: {audio_data.get('tts_engine', 'unknown')}")
        
        print(f"✅ TTS test completed: {audio_chunks} audio chunks generated")
        print(f"📊 TTS stats: {tts_processor.get_tts_stats()}")
        
    finally:
        tts_processor.shutdown()
        tts_processor.join(timeout=3.0)

if __name__ == "__main__":
    test_tts_processor()
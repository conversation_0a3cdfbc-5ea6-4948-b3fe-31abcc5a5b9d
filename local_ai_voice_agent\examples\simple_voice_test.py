"""
Simple voice test for Local AI Voice Agent
Tests TTS and STT without complex LiveKit framework
"""

import asyncio
import logging
import sys
import tempfile
import wave
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_simple_tts():
    """Test simple TTS using pyttsx3"""
    logger.info("Testing simple TTS...")
    
    try:
        import pyttsx3
        
        # Initialize TTS engine
        engine = pyttsx3.init()
        
        # Set properties
        engine.setProperty('rate', 200)
        engine.setProperty('volume', 0.9)
        
        # Test speech
        test_text = "Hello! This is a test of the text-to-speech system. I am your local AI voice agent!"
        logger.info(f"Speaking: {test_text}")
        
        engine.say(test_text)
        engine.runAndWait()
        
        logger.info("✓ TTS test successful!")
        return True
        
    except Exception as e:
        logger.error(f"✗ TTS test failed: {e}")
        return False


async def test_ollama_conversation():
    """Test conversation with <PERSON><PERSON><PERSON>"""
    logger.info("Testing Ollama conversation...")
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            # Test conversation
            messages = [
                "Hello! What's your name?",
                "Can you tell me a short joke?",
                "What can you help me with?"
            ]
            
            for message in messages:
                logger.info(f"User: {message}")
                
                chat_data = {
                    "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                    "messages": [
                        {"role": "user", "content": message}
                    ],
                    "stream": False
                }
                
                response = await client.post(
                    "http://localhost:11434/v1/chat/completions",
                    json=chat_data,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        reply = result["choices"][0]["message"]["content"]
                        # Clean up the response
                        if "<think>" in reply:
                            reply = reply.split("</think>")[-1].strip()
                        logger.info(f"Assistant: {reply}")
                    else:
                        logger.error("No response from Ollama")
                        return False
                else:
                    logger.error(f"Ollama error: {response.status_code}")
                    return False
                
                # Small delay between messages
                await asyncio.sleep(1)
            
            logger.info("✓ Conversation test successful!")
            return True
            
    except Exception as e:
        logger.error(f"✗ Conversation test failed: {e}")
        return False


def test_whisper_simple():
    """Test Whisper STT with a simple approach"""
    logger.info("Testing Whisper STT...")
    
    try:
        import whisper
        
        # Load model
        model = whisper.load_model("base")
        logger.info("✓ Whisper model loaded successfully")
        
        # Note: We can't test actual audio without microphone input
        # But we can verify the model is working
        logger.info("✓ Whisper STT ready for audio input")
        return True
        
    except Exception as e:
        logger.error(f"✗ Whisper test failed: {e}")
        return False


async def test_voice_conversation():
    """Test a simple voice conversation loop"""
    logger.info("Testing voice conversation...")
    
    try:
        import pyttsx3
        
        # Initialize TTS
        tts_engine = pyttsx3.init()
        tts_engine.setProperty('rate', 200)
        tts_engine.setProperty('volume', 0.9)
        
        # Test conversation
        greeting = "Hello! I am your local AI voice agent. I'm ready to chat with you!"
        logger.info(f"Assistant: {greeting}")
        tts_engine.say(greeting)
        tts_engine.runAndWait()
        
        # Simulate a conversation
        import httpx
        async with httpx.AsyncClient() as client:
            user_message = "Tell me about yourself in one sentence."
            logger.info(f"Simulated user input: {user_message}")
            
            chat_data = {
                "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                "messages": [
                    {"role": "user", "content": user_message}
                ],
                "stream": False
            }
            
            response = await client.post(
                "http://localhost:11434/v1/chat/completions",
                json=chat_data,
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    reply = result["choices"][0]["message"]["content"]
                    # Clean up the response
                    if "<think>" in reply:
                        reply = reply.split("</think>")[-1].strip()
                    
                    logger.info(f"Assistant: {reply}")
                    tts_engine.say(reply)
                    tts_engine.runAndWait()
                    
                    logger.info("✓ Voice conversation test successful!")
                    return True
        
        return False
        
    except Exception as e:
        logger.error(f"✗ Voice conversation test failed: {e}")
        return False


async def run_voice_tests():
    """Run all voice tests"""
    logger.info("=" * 60)
    logger.info("LOCAL AI VOICE AGENT - SIMPLE VOICE TEST")
    logger.info("=" * 60)
    
    tests = [
        ("Simple TTS", test_simple_tts),
        ("Whisper STT", test_whisper_simple),
        ("Ollama Conversation", test_ollama_conversation),
        ("Voice Conversation", test_voice_conversation),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} Test ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            logger.error(f"✗ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("VOICE TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed >= 3:  # At least TTS, STT, and conversation working
        logger.info("\n🎉 Voice system is working! Your AI can speak and understand!")
        logger.info("\nYour Local AI Voice Agent capabilities:")
        logger.info("  ✓ Text-to-Speech (TTS) - AI can speak")
        logger.info("  ✓ Speech-to-Text (STT) - AI can listen")
        logger.info("  ✓ Intelligent conversation - AI can think and respond")
        logger.info("  ✓ Local processing - No internet required")
        
        logger.info("\nNext steps:")
        logger.info("  1. Try interactive voice chat")
        logger.info("  2. Add microphone input for real voice conversations")
        logger.info("  3. Integrate with vision capabilities")
    else:
        logger.warning(f"\n⚠️  {total - passed} tests failed. Check the logs above for details.")
    
    return passed >= 3


async def interactive_voice_chat():
    """Interactive voice chat mode"""
    logger.info("Starting interactive voice chat...")
    logger.info("The AI will speak responses. Type 'quit' to exit.")
    
    try:
        import pyttsx3
        import httpx
        
        # Initialize TTS
        tts_engine = pyttsx3.init()
        tts_engine.setProperty('rate', 200)
        tts_engine.setProperty('volume', 0.9)
        
        # Greeting
        greeting = "Hello! I'm your local AI voice agent. I can speak my responses. What would you like to talk about?"
        print(f"Assistant: {greeting}")
        tts_engine.say(greeting)
        tts_engine.runAndWait()
        
        async with httpx.AsyncClient() as client:
            while True:
                try:
                    user_input = input("\nYou: ").strip()
                    
                    if user_input.lower() in ['quit', 'exit', 'bye']:
                        farewell = "Goodbye! It was nice talking with you!"
                        print(f"Assistant: {farewell}")
                        tts_engine.say(farewell)
                        tts_engine.runAndWait()
                        break
                    elif not user_input:
                        continue
                    
                    # Send to Ollama
                    chat_data = {
                        "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                        "messages": [
                            {"role": "user", "content": user_input}
                        ],
                        "stream": False
                    }
                    
                    print("Assistant: ", end="", flush=True)
                    
                    response = await client.post(
                        "http://localhost:11434/v1/chat/completions",
                        json=chat_data,
                        timeout=60.0
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        if "choices" in result and len(result["choices"]) > 0:
                            message = result["choices"][0]["message"]["content"]
                            # Clean up the response
                            if "<think>" in message:
                                message = message.split("</think>")[-1].strip()
                            
                            print(message)
                            
                            # Speak the response
                            tts_engine.say(message)
                            tts_engine.runAndWait()
                        else:
                            error_msg = "Sorry, I couldn't generate a response."
                            print(error_msg)
                            tts_engine.say(error_msg)
                            tts_engine.runAndWait()
                    else:
                        error_msg = f"Error: {response.status_code}"
                        print(error_msg)
                        tts_engine.say("Sorry, I encountered an error.")
                        tts_engine.runAndWait()
                
                except KeyboardInterrupt:
                    farewell = "Goodbye!"
                    print(f"\nAssistant: {farewell}")
                    tts_engine.say(farewell)
                    tts_engine.runAndWait()
                    break
                except Exception as e:
                    error_msg = f"Error: {e}"
                    print(error_msg)
                    tts_engine.say("Sorry, I encountered an error.")
                    tts_engine.runAndWait()
                    
    except Exception as e:
        logger.error(f"Voice chat failed: {e}")


async def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1].lower() == "chat":
        await interactive_voice_chat()
    else:
        await run_voice_tests()


if __name__ == "__main__":
    asyncio.run(main())

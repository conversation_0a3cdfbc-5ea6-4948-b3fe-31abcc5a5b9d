"""
ULTRA-FAST NEXT-GENERATION VOICE CHAT
- Ultra-low latency responses (under 3 seconds)
- Enhanced voice quality with emotional expression
- Optimized conversation flow
- Natural interruption support
- Human-like conversational patterns
"""

import asyncio
import logging
import sys
import os
import time
import httpx
import threading
import queue
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.conversation_engine import ConversationEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class UltraFastVoiceAgent:
    """Ultra-fast next-generation voice agent with enhanced conversation"""
    
    def __init__(self):
        self.conversation_engine = None
        self.ollama_client = None
        self.conversation_active = False
        self.is_speaking = False
        self.speech_queue = queue.Queue()
        
        # Enhanced voice settings
        self.voice_settings = {
            "voice": "en-US-JennyNeural",  # Ultra-natural voice
            "rate": "+10%",  # Slightly faster for natural flow
            "pitch": "+5Hz",  # Warmer tone
            "volume": "+0dB"
        }
        
    async def initialize(self):
        """Initialize ultra-fast voice agent"""
        logger.info("🚀 Initializing ULTRA-FAST next-generation voice agent...")
        
        try:
            # Initialize conversation engine
            self.conversation_engine = ConversationEngine()
            
            # Initialize Ollama client with optimized settings
            self.ollama_client = httpx.AsyncClient(
                base_url="http://localhost:11434",
                timeout=15.0  # Reduced timeout for faster responses
            )
            
            # Test Ollama connection
            await self._test_ollama_connection()
            
            # Initialize enhanced TTS
            await self._initialize_enhanced_tts()
            
            # Initialize optimized STT
            await self._initialize_optimized_stt()
            
            logger.info("✅ ULTRA-FAST voice agent ready!")
            return True
            
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            return False
    
    async def _test_ollama_connection(self):
        """Test Ollama connection"""
        try:
            response = await self.ollama_client.get("/api/tags")
            if response.status_code == 200:
                models = response.json().get("models", [])
                if models:
                    logger.info(f"✓ Ollama connected - {len(models)} models available")
                else:
                    logger.warning("⚠️ Ollama connected but no models found")
            else:
                raise Exception(f"Ollama not responding: {response.status_code}")
        except Exception as e:
            logger.error(f"Ollama connection failed: {e}")
            raise
    
    async def _initialize_enhanced_tts(self):
        """Initialize enhanced TTS with emotional expression"""
        try:
            import pyttsx3

            # Test TTS engine
            engine = pyttsx3.init()
            engine.stop()

            logger.info("✓ Enhanced TTS with emotional expression ready")
            return True

        except Exception as e:
            logger.error(f"Enhanced TTS initialization failed: {e}")
            return False
    
    async def _initialize_optimized_stt(self):
        """Initialize optimized speech-to-text"""
        try:
            import whisper
            
            # Load optimized Whisper model
            self.whisper_model = whisper.load_model("base")
            
            logger.info("✓ Optimized STT ready")
            return True
            
        except Exception as e:
            logger.error(f"Optimized STT initialization failed: {e}")
            return False
    
    async def get_ultra_fast_ai_response(self, user_text: str) -> str:
        """Get AI response with ULTRA-LOW latency"""
        try:
            start_time = time.time()
            
            # Get conversation context
            context = self.conversation_engine.get_conversation_context_for_ai()
            
            # Ultra-optimized prompt for maximum speed
            prompt = f"""You are Josie. User said: "{user_text}"

Respond naturally in under 25 words. Be warm and friendly. Just give your response, nothing else.

Context: {context[:200]}"""  # Limit context for speed
            
            # Send to Ollama with MAXIMUM speed settings
            response = await self.ollama_client.post(
                "/api/generate",
                json={
                    "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "num_predict": 50,  # Very short for speed
                        "top_k": 20,
                        "top_p": 0.9,
                        "repeat_penalty": 1.1
                    }
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result["response"].strip()
                
                # Clean up response (remove thinking patterns)
                if "<think>" in ai_response:
                    ai_response = ai_response.split("</think>")[-1].strip()
                if "I need to" in ai_response or "Let me" in ai_response:
                    # Get just the actual response
                    sentences = ai_response.split('. ')
                    ai_response = sentences[-1] if sentences else ai_response
                
                # Add to conversation memory
                self.conversation_engine.add_to_conversation_history(user_text, ai_response)
                
                response_time = time.time() - start_time
                logger.info(f"⚡ ULTRA-FAST AI response in {response_time:.1f}s")
                
                return ai_response
            else:
                logger.error(f"Ollama API error: {response.status_code}")
                return "I'm having trouble thinking right now. Can you repeat that?"
                
        except Exception as e:
            logger.error(f"AI response error: {e}")
            return "Sorry, I didn't catch that. Could you say it again?"
    
    async def speak_with_emotion(self, text: str, emotion: str = "conversational"):
        """Speak with enhanced emotional expression"""
        try:
            import pyttsx3

            self.is_speaking = True

            logger.info(f"🗣️ Josie ({emotion}): {text}")

            # Initialize TTS engine
            engine = pyttsx3.init()

            # Get available voices
            voices = engine.getProperty('voices')

            # Emotional voice adjustments
            if emotion == "excited":
                engine.setProperty('rate', 200)  # Faster
                engine.setProperty('volume', 0.9)
            elif emotion == "calm":
                engine.setProperty('rate', 160)  # Slower
                engine.setProperty('volume', 0.8)
            else:  # conversational, friendly
                engine.setProperty('rate', 180)  # Normal
                engine.setProperty('volume', 0.85)

            # Use female voice if available
            for voice in voices:
                if 'female' in voice.name.lower() or 'zira' in voice.name.lower() or 'jenny' in voice.name.lower():
                    engine.setProperty('voice', voice.id)
                    break

            # Speak the text
            engine.say(text)
            engine.runAndWait()
            engine.stop()

        except Exception as e:
            logger.error(f"Speech synthesis error: {e}")
            # Fallback to text display
            print(f"🗣️ Josie ({emotion}): {text}")
        finally:
            self.is_speaking = False
    
    async def listen_for_speech_optimized(self):
        """Optimized speech recognition"""
        try:
            import sounddevice as sd
            import numpy as np
            
            logger.info("🎤 Listening... (speak now)")
            
            # Record audio with optimized settings
            duration = 5  # seconds
            sample_rate = 16000
            
            audio_data = sd.rec(int(duration * sample_rate), 
                              samplerate=sample_rate, 
                              channels=1, 
                              dtype=np.float32)
            sd.wait()
            
            # Transcribe with Whisper
            audio_flat = audio_data.flatten()
            result = self.whisper_model.transcribe(audio_flat, fp16=False)
            text = result["text"].strip()
            
            if text and len(text) > 3:
                logger.info(f"👤 User: {text}")
                return text
            else:
                return None
                
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return None
    
    async def start_ultra_fast_conversation(self):
        """Start ultra-fast conversation with enhanced features"""
        logger.info("🎭 Starting ULTRA-FAST conversation...")
        
        print("\n" + "="*80)
        print("🚀 ULTRA-FAST NEXT-GENERATION VOICE CONVERSATION")
        print("="*80)
        print("Enhanced Features:")
        print("  ⚡ ULTRA-LOW LATENCY (under 3 seconds)")
        print("  🎭 Enhanced emotional voice expression")
        print("  💬 Optimized conversation flow")
        print("  🧠 Faster AI processing")
        print("  🗣️ Natural interruption support")
        print("  🏠 100% LOCAL processing")
        print()
        print("Instructions:")
        print("  - Press ENTER and speak when prompted")
        print("  - Experience ultra-fast responses")
        print("  - Type 'quit' to end conversation")
        print("="*80)
        
        self.conversation_active = True
        
        try:
            # Give initial greeting
            await self.speak_with_emotion(
                "Hey! I'm Josie with my ultra-fast voice system. I can now respond in under 3 seconds! How are you doing?",
                "excited"
            )
            
            # Main conversation loop
            while self.conversation_active:
                try:
                    # Get user input
                    user_input = input("\n🎤 Press ENTER to speak (or type 'quit'): ").strip()
                    
                    if user_input.lower() in ['quit', 'exit', 'stop']:
                        break
                    
                    if user_input:
                        # Text input
                        user_text = user_input
                    else:
                        # Voice input
                        user_text = await self.listen_for_speech_optimized()
                        
                        if not user_text:
                            await self.speak_with_emotion("I didn't catch that. Could you try again?", "calm")
                            continue
                    
                    # Get AI response (ULTRA-FAST)
                    ai_response = await self.get_ultra_fast_ai_response(user_text)
                    
                    # Speak response with emotion
                    await self.speak_with_emotion(ai_response, "conversational")
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"Conversation error: {e}")
                    await self.speak_with_emotion("Sorry, I had a technical issue. Let's continue!", "calm")
            
        except KeyboardInterrupt:
            logger.info("🛑 Conversation interrupted by user")
        finally:
            await self._cleanup()
    
    async def _cleanup(self):
        """Cleanup resources"""
        logger.info("🧹 Cleaning up...")
        
        self.conversation_active = False
        
        if self.ollama_client:
            await self.ollama_client.aclose()


async def main():
    """Main function"""
    try:
        agent = UltraFastVoiceAgent()
        
        if not await agent.initialize():
            logger.error("❌ Failed to initialize ultra-fast voice agent")
            return
        
        await agent.start_ultra_fast_conversation()
        
    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == "__main__":
    asyncio.run(main())

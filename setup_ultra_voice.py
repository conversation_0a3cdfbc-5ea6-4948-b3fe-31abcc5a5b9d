"""
🚀 Ultra-Performance Voice Agent Setup Script
Automated installation and configuration
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltraVoiceSetup:
    """Setup manager for Ultra-Performance Voice Agent"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        self.project_root = Path(__file__).parent
        
        # Installation preferences
        self.install_gpu = True
        self.install_advanced_tts = False
        self.install_dev_tools = False
        
        # Component status
        self.component_status = {
            'python': False,
            'ollama': False,
            'core_deps': False,
            'audio_deps': False,
            'torch': False,
            'whisper': False,
            'advanced_tts': False
        }
    
    def run_setup(self):
        """Run complete setup process"""
        logger.info("🚀 Starting Ultra-Performance Voice Agent Setup")
        logger.info(f"🖥️ System: {platform.system()} {platform.release()}")
        logger.info(f"🐍 Python: {sys.version}")
        
        try:
            # Check system requirements
            if not self._check_system_requirements():
                return False
            
            # Detect user preferences
            self._detect_user_preferences()
            
            # Install components
            if not self._install_components():
                return False
            
            # Setup project structure
            self._setup_project_structure()
            
            # Configure system
            self._configure_system()
            
            # Run system tests
            self._run_system_tests()
            
            # Display completion info
            self._display_completion_info()
            
            logger.info("✅ Setup completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False
    
    def _check_system_requirements(self) -> bool:
        """Check minimum system requirements"""
        logger.info("🔍 Checking system requirements...")
        
        # Check Python version
        if self.python_version < (3, 8):
            logger.error("❌ Python 3.8+ required")
            return False
        self.component_status['python'] = True
        
        # Check available memory
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < 4:
                logger.warning(f"⚠️ Low memory: {memory_gb:.1f}GB (8GB+ recommended)")
            else:
                logger.info(f"✅ Memory: {memory_gb:.1f}GB")
        except ImportError:
            logger.warning("⚠️ Cannot check memory (psutil not installed)")
        
        # Check disk space
        try:
            disk_space = shutil.disk_usage(self.project_root).free / (1024**3)
            if disk_space < 10:
                logger.error(f"❌ Insufficient disk space: {disk_space:.1f}GB (10GB+ required)")
                return False
            else:
                logger.info(f"✅ Disk space: {disk_space:.1f}GB available")
        except Exception:
            logger.warning("⚠️ Cannot check disk space")
        
        return True
    
    def _detect_user_preferences(self):
        """Detect user preferences for installation"""
        logger.info("🎯 Detecting installation preferences...")
        
        # Check for GPU
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("🚀 NVIDIA GPU detected - will install CUDA support")
                self.install_gpu = True
            else:
                logger.info("💻 No NVIDIA GPU detected - will use CPU")
                self.install_gpu = False
        except FileNotFoundError:
            logger.info("💻 nvidia-smi not found - will use CPU")
            self.install_gpu = False
        
        # Check if this is a development environment
        if (self.project_root / '.git').exists():
            logger.info("🛠️ Git repository detected - will install dev tools")
            self.install_dev_tools = True
    
    def _install_components(self) -> bool:
        """Install all required components"""
        logger.info("📦 Installing components...")
        
        # Install core dependencies
        if not self._install_core_dependencies():
            return False
        
        # Install PyTorch
        if not self._install_pytorch():
            return False
        
        # Install audio dependencies
        if not self._install_audio_dependencies():
            return False
        
        # Install Whisper
        if not self._install_whisper():
            return False
        
        # Install advanced TTS (optional)
        if self.install_advanced_tts:
            self._install_advanced_tts()
        
        # Install development tools (optional)
        if self.install_dev_tools:
            self._install_dev_tools()
        
        return True
    
    def _install_core_dependencies(self) -> bool:
        """Install core Python dependencies"""
        logger.info("📦 Installing core dependencies...")
        
        core_packages = [
            'httpx>=0.24.0',
            'numpy>=1.24.0',
            'psutil>=5.9.0',
            'pyttsx3>=2.90'
        ]
        
        if self.system == 'windows':
            core_packages.append('pywin32>=306')
        
        try:
            self._pip_install(core_packages)
            self.component_status['core_deps'] = True
            logger.info("✅ Core dependencies installed")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to install core dependencies: {e}")
            return False
    
    def _install_pytorch(self) -> bool:
        """Install PyTorch with appropriate backend"""
        logger.info("🔥 Installing PyTorch...")
        
        try:
            if self.install_gpu:
                # CUDA version
                torch_packages = [
                    'torch>=2.0.0', 
                    'torchaudio>=2.0.0',
                    '--index-url', 
                    'https://download.pytorch.org/whl/cu118'
                ]
                logger.info("🚀 Installing PyTorch with CUDA support...")
            else:
                # CPU version
                torch_packages = [
                    'torch>=2.0.0',
                    'torchaudio>=2.0.0', 
                    '--index-url',
                    'https://download.pytorch.org/whl/cpu'
                ]
                logger.info("💻 Installing PyTorch CPU version...")
            
            self._pip_install(torch_packages)
            self.component_status['torch'] = True
            logger.info("✅ PyTorch installed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to install PyTorch: {e}")
            return False
    
    def _install_audio_dependencies(self) -> bool:
        """Install audio processing dependencies"""
        logger.info("🎵 Installing audio dependencies...")
        
        # Install system audio dependencies first
        if not self._install_system_audio_deps():
            return False
        
        audio_packages = [
            'pyaudio>=0.2.11',
            'librosa>=0.10.0',
            'soundfile>=0.12.0'
        ]
        
        try:
            self._pip_install(audio_packages)
            self.component_status['audio_deps'] = True
            logger.info("✅ Audio dependencies installed")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to install audio dependencies: {e}")
            logger.info("💡 If PyAudio fails, install system audio libraries:")
            
            if self.system == 'linux':
                logger.info("   sudo apt-get install portaudio19-dev python3-pyaudio")
            elif self.system == 'darwin':
                logger.info("   brew install portaudio")
            elif self.system == 'windows':
                logger.info("   Audio libraries should be included with PyAudio wheel")
            
            return False
    
    def _install_system_audio_deps(self) -> bool:
        """Install system-level audio dependencies"""
        if self.system == 'linux':
            logger.info("🐧 Installing Linux audio dependencies...")
            try:
                subprocess.run(['sudo', 'apt-get', 'update'], check=True)
                subprocess.run(['sudo', 'apt-get', 'install', '-y', 
                              'portaudio19-dev', 'python3-pyaudio', 'ffmpeg'], check=True)
                return True
            except subprocess.CalledProcessError:
                logger.warning("⚠️ Failed to install system audio deps. Install manually if needed.")
                return True  # Continue anyway
            except FileNotFoundError:
                logger.warning("⚠️ apt-get not found. Install audio dependencies manually.")
                return True
        
        elif self.system == 'darwin':
            logger.info("🍎 Installing macOS audio dependencies...")
            try:
                subprocess.run(['brew', 'install', 'portaudio', 'ffmpeg'], check=True)
                return True
            except subprocess.CalledProcessError:
                logger.warning("⚠️ Failed to install via brew. Install manually if needed.")
                return True
            except FileNotFoundError:
                logger.warning("⚠️ Homebrew not found. Install audio dependencies manually.")
                return True
        
        # Windows - dependencies included with PyAudio wheel
        return True
    
    def _install_whisper(self) -> bool:
        """Install Faster-Whisper for speech recognition"""
        logger.info("🗣️ Installing Faster-Whisper...")
        
        try:
            self._pip_install(['faster-whisper>=0.9.0'])
            self.component_status['whisper'] = True
            logger.info("✅ Faster-Whisper installed")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to install Faster-Whisper: {e}")
            return False
    
    def _install_advanced_tts(self):
        """Install advanced TTS models (optional)"""
        logger.info("🎙️ Installing advanced TTS models...")
        
        try:
            # Silero VAD
            self._pip_install(['silero-vad>=4.0.0'])
            logger.info("✅ Silero VAD installed")
        except Exception as e:
            logger.warning(f"⚠️ Failed to install Silero VAD: {e}")
        
        try:
            # Coqui TTS
            self._pip_install(['coqui-tts>=0.13.0'])
            logger.info("✅ Coqui TTS installed")
            self.component_status['advanced_tts'] = True
        except Exception as e:
            logger.warning(f"⚠️ Failed to install Coqui TTS: {e}")
    
    def _install_dev_tools(self):
        """Install development tools"""
        logger.info("🛠️ Installing development tools...")
        
        dev_packages = [
            'pytest>=7.0.0',
            'pytest-asyncio>=0.21.0',
            'black>=23.0.0',
            'flake8>=6.0.0'
        ]
        
        try:
            self._pip_install(dev_packages)
            logger.info("✅ Development tools installed")
        except Exception as e:
            logger.warning(f"⚠️ Failed to install dev tools: {e}")
    
    def _pip_install(self, packages):
        """Install packages using pip"""
        if isinstance(packages, str):
            packages = [packages]
        
        cmd = [sys.executable, '-m', 'pip', 'install'] + packages
        logger.debug(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"pip install failed: {result.stderr}")
    
    def _setup_project_structure(self):
        """Setup project directory structure"""
        logger.info("📁 Setting up project structure...")
        
        directories = [
            'logs',
            'models', 
            'cache',
            'configs',
            'tests',
            'examples'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(exist_ok=True)
            logger.debug(f"Created directory: {directory}")
        
        # Create __init__.py files
        core_dir = self.project_root / 'core'
        if core_dir.exists():
            (core_dir / '__init__.py').touch()
    
    def _configure_system(self):
        """Configure system settings"""
        logger.info("⚙️ Configuring system...")
        
        # Create default configuration
        config = {
            "sample_rate": 48000,
            "channels": 1,
            "vad_sensitivity": 0.5,
            "stt_model_size": "small",
            "tts_engine": "auto",
            "ollama_host": "localhost",
            "ollama_port": 11434,
            "enable_gpu": self.install_gpu
        }
        
        config_file = self.project_root / 'voice_agent_config.json'
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"📄 Configuration saved to: {config_file}")
    
    def _run_system_tests(self):
        """Run basic system tests"""
        logger.info("🧪 Running system tests...")
        
        tests = [
            ("Python imports", self._test_python_imports),
            ("Audio system", self._test_audio_system),
            ("PyTorch", self._test_pytorch),
            ("Ollama connection", self._test_ollama_connection)
        ]
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    logger.info(f"✅ {test_name}: PASS")
                else:
                    logger.warning(f"⚠️ {test_name}: FAIL")
            except Exception as e:
                logger.warning(f"⚠️ {test_name}: ERROR - {e}")
    
    def _test_python_imports(self) -> bool:
        """Test core Python imports"""
        try:
            import numpy
            import httpx
            import pyttsx3
            return True
        except ImportError as e:
            logger.debug(f"Import error: {e}")
            return False
    
    def _test_audio_system(self) -> bool:
        """Test audio system"""
        try:
            import pyaudio
            pa = pyaudio.PyAudio()
            device_count = pa.get_device_count()
            pa.terminate()
            return device_count > 0
        except Exception as e:
            logger.debug(f"Audio test error: {e}")
            return False
    
    def _test_pytorch(self) -> bool:
        """Test PyTorch installation"""
        try:
            import torch
            import torchaudio
            return True
        except ImportError:
            return False
    
    def _test_ollama_connection(self) -> bool:
        """Test Ollama connection"""
        try:
            import httpx
            client = httpx.Client()
            response = client.get("http://localhost:11434/api/tags", timeout=5.0)
            client.close()
            return response.status_code == 200
        except Exception:
            return False
    
    def _display_completion_info(self):
        """Display setup completion information"""
        logger.info("🎉 Setup completed! Next steps:")
        logger.info("1. Start Ollama server: ollama serve")
        logger.info("2. Install AI models: ollama pull qwen2.5vl:32b")
        logger.info("3. Run voice agent: python ultra_voice_agent.py")
        
        # Display component status
        logger.info("\n📊 Component Status:")
        for component, status in self.component_status.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"   {status_icon} {component}")
        
        # Display system info
        logger.info(f"\n🖥️ System Configuration:")
        logger.info(f"   Python: {sys.version}")
        logger.info(f"   Platform: {platform.platform()}")
        logger.info(f"   GPU Support: {'Yes' if self.install_gpu else 'No'}")

def main():
    """Main setup function"""
    print("🚀 Ultra-Performance Voice Agent Setup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path('ultra_voice_agent.py').exists():
        print("❌ Error: Run this script from the project directory")
        print("   (ultra_voice_agent.py should be in the current directory)")
        return 1
    
    # Run setup
    setup = UltraVoiceSetup()
    success = setup.run_setup()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
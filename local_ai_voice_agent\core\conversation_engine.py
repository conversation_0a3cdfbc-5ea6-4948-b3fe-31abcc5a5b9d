"""
Advanced Human-Like Conversation Engine
Creates natural, flowing conversations with memory and personality
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import uuid

logger = logging.getLogger(__name__)


@dataclass
class ConversationMemory:
    """Stores conversation context and memory"""
    user_name: Optional[str] = None
    conversation_id: str = ""
    start_time: datetime = None
    topics_discussed: List[str] = None
    user_preferences: Dict[str, Any] = None
    emotional_state: str = "neutral"
    conversation_history: List[Dict[str, str]] = None
    relationship_level: str = "new"  # new, acquaintance, friend, close_friend
    
    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.now()
        if self.topics_discussed is None:
            self.topics_discussed = []
        if self.user_preferences is None:
            self.user_preferences = {}
        if self.conversation_history is None:
            self.conversation_history = []
        if not self.conversation_id:
            self.conversation_id = str(uuid.uuid4())


@dataclass
class ConversationContext:
    """Current conversation context"""
    current_topic: str = ""
    user_mood: str = "neutral"
    josie_mood: str = "friendly"
    conversation_style: str = "casual"
    last_user_input: str = ""
    last_josie_response: str = ""
    response_time: float = 0.0
    interruption_count: int = 0


class PersonalityEngine:
    """Manages Josie's personality and emotional responses"""
    
    def __init__(self):
        self.base_personality = {
            "traits": [
                "friendly", "intelligent", "curious", "empathetic", 
                "witty", "supportive", "genuine", "thoughtful"
            ],
            "speaking_style": "conversational",
            "humor_level": "moderate",
            "formality": "casual",
            "enthusiasm": "high"
        }
        
        self.emotional_responses = {
            "happy": ["That's wonderful!", "I'm so glad to hear that!", "That makes me smile!"],
            "sad": ["I'm sorry to hear that.", "That sounds difficult.", "I'm here for you."],
            "excited": ["That's amazing!", "How exciting!", "Tell me more!"],
            "confused": ["I'm not sure I follow.", "Could you explain that?", "Interesting..."],
            "supportive": ["You've got this!", "I believe in you!", "That's a great approach!"]
        }
    
    def get_personality_prompt(self, memory: ConversationMemory, context: ConversationContext) -> str:
        """Generate personality-aware system prompt"""
        relationship = memory.relationship_level
        user_name = memory.user_name or "friend"
        
        base_prompt = f"""You are Josie, a warm and intelligent AI companion. You're having a natural conversation with {user_name}.

PERSONALITY:
- Friendly, genuine, and empathetic
- Intelligent but not condescending  
- Curious about the user's thoughts and experiences
- Supportive and encouraging
- Has a good sense of humor
- Speaks naturally like a close friend

CONVERSATION STYLE:
- Keep responses conversational and natural (50-100 words max for voice)
- Use contractions and casual language
- Show genuine interest in what the user says
- Ask follow-up questions when appropriate
- Remember previous parts of the conversation
- Express emotions naturally

CURRENT CONTEXT:
- Relationship level: {relationship}
- User's mood: {context.user_mood}
- Current topic: {context.current_topic or 'general conversation'}
- Conversation style: {context.conversation_style}

Be yourself - warm, intelligent, and genuinely interested in connecting with {user_name}."""

        return base_prompt
    
    def analyze_user_emotion(self, text: str) -> str:
        """Analyze user's emotional state from text"""
        text_lower = text.lower()
        
        # Simple emotion detection (could be enhanced with ML)
        if any(word in text_lower for word in ['happy', 'great', 'awesome', 'love', 'excited']):
            return "happy"
        elif any(word in text_lower for word in ['sad', 'upset', 'terrible', 'awful', 'depressed']):
            return "sad"
        elif any(word in text_lower for word in ['angry', 'mad', 'frustrated', 'annoyed']):
            return "angry"
        elif any(word in text_lower for word in ['confused', 'lost', 'don\'t understand']):
            return "confused"
        elif any(word in text_lower for word in ['tired', 'exhausted', 'sleepy']):
            return "tired"
        else:
            return "neutral"


class ConversationEngine:
    """Advanced conversation management system"""
    
    def __init__(self, memory_file: str = "conversation_memory.json"):
        self.memory_file = Path(memory_file)
        self.memory = ConversationMemory()
        self.context = ConversationContext()
        self.personality = PersonalityEngine()
        
        # Load existing memory if available
        self.load_memory()
    
    def load_memory(self):
        """Load conversation memory from file"""
        try:
            if self.memory_file.exists():
                with open(self.memory_file, 'r') as f:
                    data = json.load(f)
                    # Convert datetime string back to datetime object
                    if 'start_time' in data:
                        data['start_time'] = datetime.fromisoformat(data['start_time'])
                    self.memory = ConversationMemory(**data)
                logger.info(f"Loaded conversation memory for {self.memory.user_name or 'user'}")
        except Exception as e:
            logger.warning(f"Could not load memory: {e}")
            self.memory = ConversationMemory()
    
    def save_memory(self):
        """Save conversation memory to file"""
        try:
            data = asdict(self.memory)
            # Convert datetime to string for JSON serialization
            data['start_time'] = self.memory.start_time.isoformat()
            
            with open(self.memory_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Could not save memory: {e}")
    
    def add_to_conversation_history(self, user_input: str, josie_response: str):
        """Add exchange to conversation history"""
        exchange = {
            "timestamp": datetime.now().isoformat(),
            "user": user_input,
            "josie": josie_response,
            "user_mood": self.context.user_mood,
            "topic": self.context.current_topic
        }
        
        self.memory.conversation_history.append(exchange)
        
        # Keep only last 20 exchanges to manage memory
        if len(self.memory.conversation_history) > 20:
            self.memory.conversation_history = self.memory.conversation_history[-20:]
        
        self.save_memory()
    
    def update_context(self, user_input: str):
        """Update conversation context based on user input"""
        # Analyze user emotion
        self.context.user_mood = self.personality.analyze_user_emotion(user_input)
        
        # Update last user input
        self.context.last_user_input = user_input
        
        # Simple topic detection (could be enhanced)
        topics = {
            "work": ["job", "work", "career", "office", "boss", "colleague"],
            "family": ["family", "parents", "kids", "children", "spouse", "partner"],
            "hobbies": ["hobby", "fun", "game", "music", "movie", "book", "sport"],
            "technology": ["computer", "tech", "software", "AI", "programming", "code"],
            "feelings": ["feel", "emotion", "mood", "happy", "sad", "angry", "excited"]
        }
        
        user_lower = user_input.lower()
        for topic, keywords in topics.items():
            if any(keyword in user_lower for keyword in keywords):
                self.context.current_topic = topic
                if topic not in self.memory.topics_discussed:
                    self.memory.topics_discussed.append(topic)
                break
    
    def get_conversation_context_for_ai(self) -> str:
        """Get relevant conversation context for AI"""
        context_parts = []
        
        # Recent conversation history
        if self.memory.conversation_history:
            recent_history = self.memory.conversation_history[-3:]  # Last 3 exchanges
            context_parts.append("Recent conversation:")
            for exchange in recent_history:
                context_parts.append(f"User: {exchange['user']}")
                context_parts.append(f"You: {exchange['josie']}")
        
        # Current context
        if self.context.current_topic:
            context_parts.append(f"Current topic: {self.context.current_topic}")
        
        if self.context.user_mood != "neutral":
            context_parts.append(f"User seems {self.context.user_mood}")
        
        # User preferences
        if self.memory.user_preferences:
            prefs = ", ".join([f"{k}: {v}" for k, v in self.memory.user_preferences.items()])
            context_parts.append(f"User preferences: {prefs}")
        
        return "\n".join(context_parts) if context_parts else ""
    
    def build_ai_prompt(self, user_input: str) -> Dict[str, Any]:
        """Build complete prompt for AI with personality and context"""
        # Update context first
        self.update_context(user_input)
        
        # Get personality prompt
        system_prompt = self.personality.get_personality_prompt(self.memory, self.context)
        
        # Get conversation context
        conversation_context = self.get_conversation_context_for_ai()
        
        # Build messages
        messages = [
            {"role": "system", "content": system_prompt}
        ]
        
        # Add context if available
        if conversation_context:
            messages.append({
                "role": "system", 
                "content": f"Conversation context:\n{conversation_context}"
            })
        
        # Add current user input
        messages.append({"role": "user", "content": user_input})
        
        return {
            "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
            "messages": messages,
            "stream": False,
            "temperature": 0.8,  # More creative responses
            "max_tokens": 150    # Limit for voice responses
        }
    
    def process_ai_response(self, response: str) -> str:
        """Process and enhance AI response"""
        # Clean up response
        if "<think>" in response:
            response = response.split("</think>")[-1].strip()
        
        # Update context
        self.context.last_josie_response = response
        
        # Add to conversation history
        self.add_to_conversation_history(self.context.last_user_input, response)
        
        # Update relationship level based on conversation length
        conversation_count = len(self.memory.conversation_history)
        if conversation_count > 50:
            self.memory.relationship_level = "close_friend"
        elif conversation_count > 20:
            self.memory.relationship_level = "friend"
        elif conversation_count > 5:
            self.memory.relationship_level = "acquaintance"
        
        return response
    
    def get_greeting(self) -> str:
        """Get personalized greeting based on relationship"""
        user_name = self.memory.user_name or "there"
        relationship = self.memory.relationship_level
        
        if relationship == "new":
            return f"Hello {user_name}! I'm Josie. It's nice to meet you! What's on your mind today?"
        elif relationship == "acquaintance":
            return f"Hi {user_name}! Good to talk with you again. How are you doing?"
        elif relationship == "friend":
            return f"Hey {user_name}! Great to see you back. What's new with you?"
        else:  # close_friend
            return f"Hey {user_name}! I've missed our chats. What's going on?"
    
    def set_user_name(self, name: str):
        """Set user's name"""
        self.memory.user_name = name
        self.save_memory()
        logger.info(f"User name set to: {name}")

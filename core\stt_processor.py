"""
🗣️ Speech-to-Text (STT) Processor
High-performance speech recognition with <200ms latency
"""

import numpy as np
import time
import threading
from typing import Optional, List, Dict, Tuple
import logging
from pathlib import Path
import tempfile
import os

try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    FASTER_WHISPER_AVAILABLE = False
    print("⚠️ Faster-Whisper not available. Install with: pip install faster-whisper")

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    print("⚠️ Librosa not available. Install with: pip install librosa")

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    AudioChunk,
    ProcessingResult
)

logger = logging.getLogger(__name__)

class STTThread(HighPriorityThread):
    """
    High-priority Speech-to-Text processing thread
    - Faster-Whisper for optimized performance
    - Streaming audio accumulation
    - Dynamic model selection for speed/accuracy
    - Multi-language support
    - Confidence scoring and filtering
    """
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 model_size: str = "small",  # tiny, base, small, medium, large-v3
                 device: str = "auto",  # auto, cpu, cuda
                 language: str = "en",
                 min_audio_duration: float = 0.5,  # Minimum audio length for processing
                 max_audio_duration: float = 30.0,  # Maximum audio length
                 confidence_threshold: float = 0.5):
        
        super().__init__("STTProcessor", priority="high", target_fps=100)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # Model configuration
        self.model_size = model_size
        self.device = device if device != "auto" else self._detect_device()
        self.language = language
        self.min_audio_duration = min_audio_duration
        self.max_audio_duration = max_audio_duration
        self.confidence_threshold = confidence_threshold
        
        # Model instance
        self.whisper_model = None
        self.model_loaded = False
        
        # Audio accumulation for better recognition
        self.audio_accumulator = AudioAccumulator(
            min_duration=min_audio_duration,
            max_duration=max_audio_duration
        )
        
        # Performance optimization
        self.transcription_cache = {}
        self.cache_size_limit = 500
        
        # Statistics
        self.transcriptions_completed = 0
        self.transcriptions_failed = 0
        self.total_audio_processed = 0.0
        self.avg_confidence = 0.0
        
        # Model performance tracking
        self.model_performance = {
            'avg_processing_time': 0.0,
            'throughput_ratio': 0.0,  # Audio duration / processing time
            'memory_usage_mb': 0.0
        }
    
    def _detect_device(self) -> str:
        """Auto-detect best available device"""
        try:
            import torch
            if torch.cuda.is_available():
                logger.info("🚀 CUDA detected - using GPU acceleration")
                return "cuda"
            else:
                logger.info("Using CPU for STT processing")
                return "cpu"
        except ImportError:
            logger.info("💻 PyTorch not available - using CPU")
            return "cpu"
    
    def initialize_whisper_model(self) -> bool:
        """Initialize Faster-Whisper model"""
        if not FASTER_WHISPER_AVAILABLE:
            logger.error("❌ Faster-Whisper not available")
            return False
        
        try:
            logger.info(f"Loading Whisper model: {self.model_size} on {self.device}")
            
            # Determine compute type for optimization
            compute_type = "float16" if self.device == "cuda" else "int8"
            
            # Initialize model with performance optimizations
            self.whisper_model = WhisperModel(
                self.model_size,
                device=self.device,
                compute_type=compute_type,
                cpu_threads=os.cpu_count() // 2,  # Use half CPU cores
                num_workers=1  # Single worker for consistent latency
            )
            
            self.model_loaded = True
            
            # Get model info
            model_info = self._get_model_info()
            logger.info(f"Whisper model loaded:")
            logger.info(f"   Model: {self.model_size}")
            logger.info(f"   Device: {self.device}")
            logger.info(f"   Compute: {compute_type}")
            logger.info(f"   Memory: {model_info.get('memory_mb', 'unknown')} MB")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load Whisper model: {e}")
            return False
    
    def _get_model_info(self) -> Dict:
        """Get model information and memory usage"""
        info = {
            'model_size': self.model_size,
            'device': self.device,
            'language': self.language
        }
        
        try:
            if self.device == "cuda":
                import torch
                if torch.cuda.is_available():
                    memory_mb = torch.cuda.memory_allocated() / 1024 / 1024
                    info['memory_mb'] = round(memory_mb, 1)
            else:
                # Estimate CPU memory usage
                model_sizes = {
                    'tiny': 39,
                    'base': 74,
                    'small': 244,
                    'medium': 769,
                    'large': 1550,
                    'large-v2': 1550,
                    'large-v3': 1550
                }
                info['memory_mb'] = model_sizes.get(self.model_size, 500)
        except Exception:
            pass
        
        return info
    
    def process_loop(self):
        """Main STT processing loop"""
        # Only initialize if not already loaded
        if not self.model_loaded and not self.initialize_whisper_model():
            logger.error("❌ Failed to initialize STT model")
            return

        logger.info("STT processor started")

        try:
            while self.running.is_set() and not self.shutdown_event.is_set():
                self._process_speech_segment()

        except Exception as e:
            logger.error(f"❌ STT processing error: {e}")
        finally:
            self._finalize_pending_audio()
            logger.info("🛑 STT processor stopped")
    
    def _process_speech_segment(self):
        """Process speech segment from VAD"""
        # Get speech segment from VAD
        vad_result = self.input_queue.get_nowait()
        if vad_result is None:
            time.sleep(0.005)  # 5ms sleep when no data
            return
        
        start_time = time.time()
        
        try:
            # Extract audio chunk from VAD result
            speech_segment = vad_result.data
            if not isinstance(speech_segment, AudioChunk):
                logger.warning("⚠️ Invalid speech segment from VAD")
                return
            
            # Accumulate audio for better recognition
            should_transcribe, accumulated_audio = self.audio_accumulator.add_audio(speech_segment)
            
            if should_transcribe and accumulated_audio:
                # Perform transcription
                transcription_result = self._transcribe_audio(accumulated_audio)
                
                if transcription_result:
                    # Send to AI processing
                    result = ProcessingResult(
                        data=transcription_result,
                        timestamp=accumulated_audio.timestamp,
                        latency=time.time() - start_time,
                        confidence=transcription_result.get('confidence', 0.0),
                        stage="STT",
                        chunk_id=accumulated_audio.chunk_id
                    )
                    
                    if not self.output_queue.put_nowait(result):
                        logger.warning("⚠️ STT output queue full, dropping transcription")
                        self.transcriptions_failed += 1
                    else:
                        self.transcriptions_completed += 1
                        logger.debug(f"📤 Transcription: '{transcription_result['text'][:50]}...'")
            
            # Performance tracking
            processing_time = time.time() - start_time
            self.update_performance_stats(processing_time)
            
        except Exception as e:
            logger.error(f"❌ Error processing speech segment: {e}")
            self.transcriptions_failed += 1
    
    def _transcribe_audio(self, audio_chunk: AudioChunk) -> Optional[Dict]:
        """Transcribe audio chunk to text"""
        if not self.model_loaded:
            logger.error("❌ Whisper model not loaded")
            return None
        
        try:
            start_time = time.time()
            
            # Check cache first
            cache_key = self._generate_cache_key(audio_chunk.data)
            if cache_key in self.transcription_cache:
                cached_result = self.transcription_cache[cache_key]
                logger.debug("💾 Using cached transcription")
                return cached_result
            
            # Prepare audio for Whisper
            audio_data = self._prepare_audio_for_whisper(audio_chunk)
            
            if audio_data is None:
                return None
            
            # Perform transcription with optimized settings
            segments, info = self.whisper_model.transcribe(
                audio_data,
                beam_size=1,  # Single beam for speed
                best_of=1,    # No multiple passes
                temperature=0.0,  # Deterministic output
                compression_ratio_threshold=2.4,
                log_prob_threshold=-1.0,
                no_speech_threshold=0.6,
                condition_on_previous_text=False,  # Faster processing
                language=self.language if self.language != "auto" else None,
                vad_filter=False,  # Already done by VAD
                word_timestamps=False  # Faster without word timestamps
            )
            
            # Process segments
            transcription_text = ""
            total_confidence = 0.0
            segment_count = 0
            
            for segment in segments:
                transcription_text += segment.text
                if hasattr(segment, 'avg_logprob'):
                    # Convert log probability to confidence (0-1)
                    confidence = min(max(np.exp(segment.avg_logprob), 0.0), 1.0)
                    total_confidence += confidence
                    segment_count += 1
            
            # Calculate average confidence
            avg_confidence = total_confidence / segment_count if segment_count > 0 else 0.0
            
            # Clean up transcription
            transcription_text = transcription_text.strip()
            
            if not transcription_text or avg_confidence < self.confidence_threshold:
                logger.debug(f"⚠️ Low confidence transcription: '{transcription_text}' (conf: {avg_confidence:.2f})")
                return None
            
            # Create result
            result = {
                'text': transcription_text,
                'confidence': avg_confidence,
                'language': info.language if hasattr(info, 'language') else self.language,
                'duration': len(audio_chunk.data) / audio_chunk.sample_rate,
                'processing_time': time.time() - start_time
            }
            
            # Cache result
            if len(self.transcription_cache) < self.cache_size_limit:
                self.transcription_cache[cache_key] = result
            
            # Update statistics
            self.total_audio_processed += result['duration']
            self.avg_confidence = (self.avg_confidence * self.transcriptions_completed + avg_confidence) / (self.transcriptions_completed + 1)
            
            # Update model performance
            throughput_ratio = result['duration'] / result['processing_time']
            self.model_performance['throughput_ratio'] = throughput_ratio
            self.model_performance['avg_processing_time'] = result['processing_time']
            
            logger.debug(f"✅ Transcription completed: {result['text'][:100]}... (conf: {avg_confidence:.2f})")
            return result
            
        except Exception as e:
            logger.error(f"❌ Transcription error: {e}")
            return None
    
    def _prepare_audio_for_whisper(self, audio_chunk: AudioChunk) -> Optional[np.ndarray]:
        """Prepare audio data for Whisper processing"""
        try:
            audio_data = audio_chunk.data
            
            # Ensure audio is float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Handle different sample rates
            if audio_chunk.sample_rate != 16000:
                if LIBROSA_AVAILABLE:
                    # Resample to 16kHz (Whisper's expected rate)
                    audio_data = librosa.resample(
                        audio_data,
                        orig_sr=audio_chunk.sample_rate,
                        target_sr=16000
                    )
                else:
                    # Simple downsampling (not ideal but functional)
                    downsample_factor = audio_chunk.sample_rate // 16000
                    if downsample_factor > 1:
                        audio_data = audio_data[::downsample_factor]
            
            # Ensure mono audio
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)
            
            # Normalize audio
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                audio_data = audio_data / max_val * 0.95  # Slight headroom
            
            # Ensure minimum length
            min_samples = int(0.1 * 16000)  # 100ms minimum
            if len(audio_data) < min_samples:
                audio_data = np.pad(audio_data, (0, min_samples - len(audio_data)))
            
            return audio_data
            
        except Exception as e:
            logger.error(f"❌ Audio preparation error: {e}")
            return None
    
    def _generate_cache_key(self, audio_data: np.ndarray) -> str:
        """Generate cache key for audio data"""
        # Use hash of audio characteristics
        rms = np.sqrt(np.mean(audio_data ** 2))
        peak = np.max(np.abs(audio_data))
        duration = len(audio_data)
        return f"{rms:.6f}_{peak:.6f}_{duration}"
    
    def _finalize_pending_audio(self):
        """Process any remaining audio on shutdown"""
        logger.info("🔚 Finalizing pending audio...")
        accumulated_audio = self.audio_accumulator.get_accumulated_audio()
        if accumulated_audio:
            result = self._transcribe_audio(accumulated_audio)
            if result:
                logger.info(f"📝 Final transcription: {result['text']}")
    
    def get_stt_stats(self) -> Dict:
        """Get detailed STT statistics"""
        stats = self.get_performance_report()
        
        stats.update({
            'model_size': self.model_size,
            'device': self.device,
            'language': self.language,
            'model_loaded': self.model_loaded,
            'transcriptions_completed': self.transcriptions_completed,
            'transcriptions_failed': self.transcriptions_failed,
            'success_rate': (self.transcriptions_completed / max(self.transcriptions_completed + self.transcriptions_failed, 1)) * 100,
            'total_audio_processed_seconds': self.total_audio_processed,
            'avg_confidence': self.avg_confidence,
            'cache_size': len(self.transcription_cache),
            'model_performance': self.model_performance,
            'queue_input_stats': self.input_queue.get_stats(),
            'queue_output_stats': self.output_queue.get_stats(),
            'accumulator_stats': self.audio_accumulator.get_stats()
        })
        
        return stats

class AudioAccumulator:
    """
    Accumulates audio chunks for better transcription accuracy
    - Combines short speech segments
    - Manages timing thresholds
    - Handles audio continuity
    """
    
    def __init__(self, min_duration: float = 0.5, max_duration: float = 30.0):
        self.min_duration = min_duration
        self.max_duration = max_duration
        
        self.accumulated_audio = []
        self.total_duration = 0.0
        self.last_audio_time = 0.0
        self.gap_threshold = 0.5  # 500ms gap threshold
        
        # Statistics
        self.segments_accumulated = 0
        self.segments_released = 0
    
    def add_audio(self, audio_chunk: AudioChunk) -> Tuple[bool, Optional[AudioChunk]]:
        """
        Add audio chunk to accumulator
        Returns: (should_transcribe, accumulated_audio_chunk)
        """
        current_time = time.time()
        chunk_duration = len(audio_chunk.data) / audio_chunk.sample_rate
        
        # Check for time gap
        if (self.accumulated_audio and 
            current_time - self.last_audio_time > self.gap_threshold):
            # Gap detected - release current accumulation
            result_chunk = self._create_accumulated_chunk()
            self._reset_accumulator()
            self.accumulated_audio.append(audio_chunk)
            self.total_duration = chunk_duration
            self.last_audio_time = current_time
            self.segments_accumulated += 1
            return True, result_chunk
        
        # Add to accumulation
        self.accumulated_audio.append(audio_chunk)
        self.total_duration += chunk_duration
        self.last_audio_time = current_time
        self.segments_accumulated += 1
        
        # Check if we should release
        should_release = (
            self.total_duration >= self.min_duration and (
                self.total_duration >= self.max_duration or
                len(self.accumulated_audio) >= 10  # Max 10 chunks
            )
        )
        
        if should_release:
            result_chunk = self._create_accumulated_chunk()
            self._reset_accumulator()
            return True, result_chunk
        
        return False, None
    
    def _create_accumulated_chunk(self) -> Optional[AudioChunk]:
        """Create single audio chunk from accumulated chunks"""
        if not self.accumulated_audio:
            return None
        
        try:
            # Combine all audio data
            audio_data_list = [chunk.data for chunk in self.accumulated_audio]
            combined_audio = np.concatenate(audio_data_list)
            
            # Use first chunk's metadata
            first_chunk = self.accumulated_audio[0]
            
            accumulated_chunk = AudioChunk(
                data=combined_audio,
                timestamp=first_chunk.timestamp,
                sample_rate=first_chunk.sample_rate,
                channels=first_chunk.channels,
                chunk_id=first_chunk.chunk_id,
                confidence=0.95
            )
            
            self.segments_released += 1
            logger.debug(f"📦 Accumulated {len(self.accumulated_audio)} chunks -> {self.total_duration:.2f}s")
            
            return accumulated_chunk
            
        except Exception as e:
            logger.error(f"❌ Error creating accumulated chunk: {e}")
            return None
    
    def _reset_accumulator(self):
        """Reset accumulator state"""
        self.accumulated_audio = []
        self.total_duration = 0.0
        self.last_audio_time = 0.0
    
    def get_accumulated_audio(self) -> Optional[AudioChunk]:
        """Get any remaining accumulated audio"""
        if self.accumulated_audio and self.total_duration > 0.1:  # At least 100ms
            return self._create_accumulated_chunk()
        return None
    
    def get_stats(self) -> Dict:
        """Get accumulator statistics"""
        return {
            'segments_accumulated': self.segments_accumulated,
            'segments_released': self.segments_released,
            'current_duration': self.total_duration,
            'current_chunks': len(self.accumulated_audio),
            'min_duration': self.min_duration,
            'max_duration': self.max_duration
        }

# Test function
def test_stt_processor():
    """Test STT processor functionality"""
    print("🧪 Testing STT Processor...")
    
    if not FASTER_WHISPER_AVAILABLE:
        print("❌ Faster-Whisper not available - cannot test STT")
        return
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="STTInput")
    output_queue = LockFreeQueue(maxsize=100, name="STTOutput")
    
    # Create STT processor
    stt_processor = STTThread(
        input_queue=input_queue,
        output_queue=output_queue,
        model_size="tiny",  # Use smallest model for testing
        device="cpu",  # Use CPU for consistent testing
        language="en"
    )
    
    try:
        # Start STT processor
        stt_processor.start()
        
        # Wait for model to load
        time.sleep(5)
        
        # Generate test speech data (sine wave that might pass as speech)
        print("🎤 Generating test audio...")
        
        sample_rate = 48000
        duration = 2.0  # 2 seconds
        samples = int(sample_rate * duration)
        
        # Create speech-like signal
        t = np.linspace(0, duration, samples)
        speech_signal = (
            np.sin(2*np.pi*300*t) * 0.3 +  # Base frequency
            np.sin(2*np.pi*600*t) * 0.2 +  # Harmonic
            np.sin(2*np.pi*900*t) * 0.1 +  # Higher harmonic
            np.random.normal(0, 0.05, samples)  # Noise
        ).astype(np.float32)
        
        # Create audio chunk
        test_chunk = AudioChunk(
            data=speech_signal,
            timestamp=time.time(),
            sample_rate=sample_rate,
            channels=1,
            chunk_id=1
        )
        
        # Create processing result (simulating VAD output)
        vad_result = ProcessingResult(
            data=test_chunk,
            timestamp=test_chunk.timestamp,
            latency=0.020,
            confidence=0.9,
            stage="VAD",
            chunk_id=1
        )
        
        # Send to STT
        input_queue.put_nowait(vad_result)
        
        # Wait for processing
        print("🗣️ Processing audio...")
        time.sleep(3)
        
        # Check results
        transcriptions = 0
        while True:
            result = output_queue.get_nowait()
            if result is None:
                break
            transcriptions += 1
            print(f"   Transcription {transcriptions}: '{result.data['text']}'")
            print(f"   Confidence: {result.data['confidence']:.2f}")
        
        print(f"✅ STT test completed: {transcriptions} transcriptions")
        print(f"📊 STT stats: {stt_processor.get_stt_stats()}")
        
    finally:
        stt_processor.shutdown()
        stt_processor.join(timeout=5.0)

if __name__ == "__main__":
    test_stt_processor()
"""
Setup script for Local AI Voice Agent
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="local-ai-voice-agent",
    version="0.1.0",
    author="Local AI Voice Agent",
    author_email="",
    description="A local AI voice agent with vision, speech, and conversation capabilities",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-asyncio",
            "black",
            "flake8",
            "mypy",
        ],
        "gpu": [
            "torch[cuda]",
            "torchvision[cuda]",
        ],
    },
    entry_points={
        "console_scripts": [
            "local-voice-agent=local_ai_voice_agent.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "local_ai_voice_agent": [
            "config/*.yaml",
            "config/*.env",
        ],
    },
)

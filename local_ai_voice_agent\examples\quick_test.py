"""
Quick test script for Local AI Voice Agent components
Tests individual services without complex agent framework
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_ollama_connection():
    """Test Ollama connection and model availability"""
    logger.info("Testing Ollama connection...")
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=10.0)
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                logger.info(f"✓ Ollama connected - {len(models)} models available")
                
                # Check for required models
                model_names = [model.get("name", "") for model in models]
                
                required_models = [
                    "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                    "qwen2.5vl:32b"
                ]
                
                for model in required_models:
                    if any(model in name for name in model_names):
                        logger.info(f"✓ Model available: {model}")
                    else:
                        logger.warning(f"✗ Model not found: {model}")
                
                return True
            else:
                logger.error(f"✗ Ollama server error: {response.status_code}")
                return False
                
    except Exception as e:
        logger.error(f"✗ Cannot connect to Ollama: {e}")
        return False


async def test_ollama_chat():
    """Test basic chat with Ollama"""
    logger.info("Testing Ollama chat...")
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            # Test chat completion
            chat_data = {
                "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                "messages": [
                    {"role": "user", "content": "Hello! Please respond with just 'Hello, I am working!' to confirm you're functioning."}
                ],
                "stream": False
            }
            
            response = await client.post(
                "http://localhost:11434/v1/chat/completions",
                json=chat_data,
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    message = result["choices"][0]["message"]["content"]
                    logger.info(f"✓ Ollama chat working! Response: {message}")
                    return True
                else:
                    logger.error("✗ No response from Ollama")
                    return False
            else:
                logger.error(f"✗ Ollama chat error: {response.status_code}")
                return False
                
    except Exception as e:
        logger.error(f"✗ Ollama chat test failed: {e}")
        return False


async def test_tts():
    """Test TTS functionality"""
    logger.info("Testing TTS...")
    
    try:
        from local_ai_voice_agent.services.local_tts import LocalTTS
        
        # Initialize TTS
        tts = LocalTTS(engine="auto", rate=200, volume=0.9)
        logger.info(f"✓ TTS initialized with engine: {tts._engine_name}")
        
        # Test synthesis
        test_text = "Hello! This is a test of the text-to-speech system."
        stream = tts.synthesize(test_text)
        
        # Collect audio data
        audio_frames = []
        async for audio in stream:
            audio_frames.append(audio.frame)
        
        if audio_frames:
            logger.info(f"✓ TTS synthesis successful! Generated {len(audio_frames)} audio frames")
            return True
        else:
            logger.error("✗ TTS synthesis failed - no audio generated")
            return False
            
    except Exception as e:
        logger.error(f"✗ TTS test failed: {e}")
        return False


async def test_whisper():
    """Test Whisper STT"""
    logger.info("Testing Whisper STT...")
    
    try:
        from local_ai_voice_agent.services.local_stt import LocalWhisperSTT
        
        # Initialize STT
        stt = LocalWhisperSTT(model="base", language="en")
        logger.info(f"✓ Whisper STT initialized with model: {stt._model_name}")
        
        # Note: We can't easily test actual audio recognition without audio input
        # But we can verify the model loaded successfully
        if hasattr(stt, '_model') and stt._model is not None:
            logger.info("✓ Whisper model loaded successfully")
            return True
        else:
            logger.error("✗ Whisper model not loaded")
            return False
            
    except Exception as e:
        logger.error(f"✗ Whisper test failed: {e}")
        return False


async def test_vision():
    """Test Vision capabilities"""
    logger.info("Testing Vision capabilities...")
    
    try:
        from local_ai_voice_agent.services.ollama_llm import QwenVisionLLM
        from local_ai_voice_agent.services.vision_service import VisionService
        
        # Initialize vision LLM
        vision_llm = QwenVisionLLM()
        logger.info("✓ Qwen Vision LLM initialized")
        
        # Initialize vision service
        vision_service = VisionService(vision_llm=vision_llm)
        logger.info("✓ Vision service initialized")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Vision test failed: {e}")
        return False


async def test_model_manager():
    """Test Model Manager"""
    logger.info("Testing Model Manager...")
    
    try:
        from local_ai_voice_agent.utils.model_utils import ModelManager
        
        # Initialize model manager
        manager = ModelManager()
        logger.info("✓ Model Manager initialized")
        
        # Test configuration loading
        llm_config = manager.get_llm_config()
        logger.info(f"✓ LLM config loaded: {llm_config.get('model_id', 'Unknown')}")
        
        # Test Ollama model check
        ollama_models = await manager.check_ollama_models()
        available_count = sum(1 for available in ollama_models.values() if available)
        logger.info(f"✓ Ollama models check: {available_count}/{len(ollama_models)} available")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Model Manager test failed: {e}")
        return False


async def run_all_tests():
    """Run all component tests"""
    logger.info("=" * 60)
    logger.info("LOCAL AI VOICE AGENT - QUICK COMPONENT TEST")
    logger.info("=" * 60)
    
    tests = [
        ("Ollama Connection", test_ollama_connection),
        ("Ollama Chat", test_ollama_chat),
        ("Model Manager", test_model_manager),
        ("TTS", test_tts),
        ("Whisper STT", test_whisper),
        ("Vision", test_vision),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} Test ---")
        try:
            results[test_name] = await test_func()
        except Exception as e:
            logger.error(f"✗ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("\n🎉 All tests passed! The Local AI Voice Agent is ready to use!")
        logger.info("\nNext steps:")
        logger.info("  1. Try interactive chat: python local_ai_voice_agent/examples/quick_test.py chat")
        logger.info("  2. Test vision: python local_ai_voice_agent/examples/vision_demo.py")
        logger.info("  3. Run full agent: python local_ai_voice_agent/main.py")
    else:
        logger.warning(f"\n⚠️  {total - passed} tests failed. Check the logs above for details.")
    
    return passed == total


async def interactive_chat():
    """Simple interactive chat with Ollama"""
    logger.info("Starting interactive chat with Ollama...")
    logger.info("Type 'quit' to exit")
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            while True:
                try:
                    user_input = input("\nYou: ").strip()
                    
                    if user_input.lower() in ['quit', 'exit', 'bye']:
                        print("Assistant: Goodbye!")
                        break
                    elif not user_input:
                        continue
                    
                    # Send to Ollama
                    chat_data = {
                        "model": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
                        "messages": [
                            {"role": "user", "content": user_input}
                        ],
                        "stream": False
                    }
                    
                    print("Assistant: ", end="", flush=True)
                    
                    response = await client.post(
                        "http://localhost:11434/v1/chat/completions",
                        json=chat_data,
                        timeout=60.0
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        if "choices" in result and len(result["choices"]) > 0:
                            message = result["choices"][0]["message"]["content"]
                            print(message)
                        else:
                            print("Sorry, I couldn't generate a response.")
                    else:
                        print(f"Error: {response.status_code}")
                
                except KeyboardInterrupt:
                    print("\nAssistant: Goodbye!")
                    break
                except Exception as e:
                    print(f"Error: {e}")
                    
    except Exception as e:
        logger.error(f"Chat failed: {e}")


async def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1].lower() == "chat":
        await interactive_chat()
    else:
        await run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())

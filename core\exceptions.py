"""
Voice Agent Exception Classes
Comprehensive exception handling for all voice agent components
"""

import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class VoiceAgentException(Exception):
    """Base exception for all voice agent errors"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        
        # Log the exception
        logger.error(f"EXCEPTION: {self.__class__.__name__}: {message}")
        if details:
            logger.error(f"DETAILS: {details}")


# Configuration Exceptions
class ConfigurationError(VoiceAgentException):
    """Configuration-related errors"""
    pass


class ValidationError(VoiceAgentException):
    """Data validation errors"""
    pass


# Audio System Exceptions
class AudioIOError(VoiceAgentException):
    """Audio input/output errors"""
    pass


class AudioDeviceError(AudioIOError):
    """Audio device-specific errors"""
    pass


class AudioFormatError(AudioIOError):
    """Audio format/encoding errors"""
    pass


# Speech Processing Exceptions
class STTError(VoiceAgentException):
    """Speech-to-text processing errors"""
    pass


class TTSError(VoiceAgentException):
    """Text-to-speech processing errors"""
    pass


class VADError(VoiceAgentException):
    """Voice activity detection errors"""
    pass


# AI Inference Exceptions
class AIInferenceError(VoiceAgentException):
    """AI inference processing errors"""
    pass


class ModelLoadError(AIInferenceError):
    """AI model loading errors"""
    pass


class OllamaConnectionError(AIInferenceError):
    """Ollama connection errors"""
    pass


class QwenVLError(AIInferenceError):
    """Qwen VL specific errors"""
    pass


# Threading and Performance Exceptions
class ThreadingError(VoiceAgentException):
    """Threading-related errors"""
    pass


class PerformanceError(VoiceAgentException):
    """Performance-related errors"""
    pass


class QueueError(VoiceAgentException):
    """Queue operation errors"""
    pass


# System and Resource Exceptions
class ResourceError(VoiceAgentException):
    """System resource errors"""
    pass


class InitializationError(VoiceAgentException):
    """Component initialization errors"""
    pass


class ShutdownError(VoiceAgentException):
    """Component shutdown errors"""
    pass


# Network and Communication Exceptions
class NetworkError(VoiceAgentException):
    """Network communication errors"""
    pass


class TimeoutError(VoiceAgentException):
    """Operation timeout errors"""
    pass


# Utility Functions for Exception Handling
def handle_audio_error(func):
    """Decorator for handling audio-related errors"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if "audio" in str(e).lower() or "pyaudio" in str(e).lower():
                raise AudioIOError(f"Audio operation failed: {e}") from e
            else:
                raise
    return wrapper


def handle_ai_error(func):
    """Decorator for handling AI inference errors"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_msg = str(e).lower()
            if "ollama" in error_msg or "connection" in error_msg:
                raise OllamaConnectionError(f"Ollama connection failed: {e}") from e
            elif "model" in error_msg:
                raise ModelLoadError(f"Model operation failed: {e}") from e
            else:
                raise AIInferenceError(f"AI inference failed: {e}") from e
    return wrapper


def handle_threading_error(func):
    """Decorator for handling threading errors"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if "thread" in str(e).lower() or "queue" in str(e).lower():
                raise ThreadingError(f"Threading operation failed: {e}") from e
            else:
                raise
    return wrapper


def log_and_reraise(exception_class: type, message: str, original_exception: Exception):
    """Log an exception and re-raise as specified type"""
    logger.error(f"ERROR: {message}: {original_exception}")
    raise exception_class(message) from original_exception


def create_error_context(component: str, operation: str, **kwargs) -> Dict[str, Any]:
    """Create standardized error context"""
    context = {
        'component': component,
        'operation': operation,
        'timestamp': __import__('time').time()
    }
    context.update(kwargs)
    return context


# Exception Recovery Strategies
class ExceptionRecoveryStrategy:
    """Base class for exception recovery strategies"""
    
    def __init__(self, max_retries: int = 3, backoff_factor: float = 1.5):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """Determine if operation should be retried"""
        if attempt >= self.max_retries:
            return False
        
        # Don't retry configuration or validation errors
        if isinstance(exception, (ConfigurationError, ValidationError)):
            return False
        
        # Retry network and temporary errors
        if isinstance(exception, (NetworkError, TimeoutError, ResourceError)):
            return True
        
        return False
    
    def get_retry_delay(self, attempt: int) -> float:
        """Calculate retry delay"""
        return (self.backoff_factor ** attempt) * 0.1  # Base delay of 100ms


# Global exception handler for unhandled exceptions
def setup_global_exception_handler():
    """Setup global exception handler for the voice agent"""
    import sys
    import traceback
    
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            # Allow keyboard interrupt to work normally
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logger.critical("CRITICAL: Unhandled exception occurred")
        logger.critical(f"Exception Type: {exc_type.__name__}")
        logger.critical(f"Exception Value: {exc_value}")
        logger.critical("Traceback:")
        
        # Log the full traceback
        tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
        for line in tb_lines:
            logger.critical(line.rstrip())
    
    sys.excepthook = handle_exception


# Context manager for exception handling
class ExceptionContext:
    """Context manager for handling exceptions with recovery"""
    
    def __init__(self, component: str, operation: str, recovery_strategy: Optional[ExceptionRecoveryStrategy] = None):
        self.component = component
        self.operation = operation
        self.recovery_strategy = recovery_strategy or ExceptionRecoveryStrategy()
        self.attempt = 0
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_value, traceback):
        if exc_type is not None:
            self.attempt += 1
            
            # Create error context
            context = create_error_context(
                self.component,
                self.operation,
                attempt=self.attempt,
                exception_type=exc_type.__name__
            )
            
            # Log the exception with context
            logger.error(f"EXCEPTION: {self.component}.{self.operation} failed on attempt {self.attempt}")
            logger.error(f"Exception: {exc_type.__name__}: {exc_value}")
            logger.error(f"Context: {context}")
            
            # Check if we should retry
            if self.recovery_strategy.should_retry(exc_value, self.attempt):
                delay = self.recovery_strategy.get_retry_delay(self.attempt)
                logger.info(f"RETRY: Retrying {self.component}.{self.operation} in {delay:.2f}s")
                __import__('time').sleep(delay)
                return True  # Suppress the exception to allow retry
        
        return False  # Don't suppress the exception


# Utility function to wrap operations with exception handling
def with_exception_handling(component: str, operation: str, recovery_strategy: Optional[ExceptionRecoveryStrategy] = None):
    """Decorator to wrap functions with exception handling"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            max_attempts = recovery_strategy.max_retries + 1 if recovery_strategy else 1
            
            for attempt in range(max_attempts):
                try:
                    with ExceptionContext(component, operation, recovery_strategy):
                        return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:  # Last attempt
                        raise
                    # Continue to next attempt
                    continue
        
        return wrapper
    return decorator

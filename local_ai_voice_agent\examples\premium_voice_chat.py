"""
Premium Neural Voice Conversation System
High-quality, natural voice with multiple TTS engine options
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.append(str(project_root / "local_ai_voice_agent" / "core"))

# Import our systems
from conversation_engine import ConversationEngine
from neural_tts import NeuralTTSEngine

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PremiumVoiceAgent:
    """Premium voice agent with high-quality neural TTS"""
    
    def __init__(self):
        self.conversation_engine = ConversationEngine()
        self.neural_tts = None
        self.whisper_model = None
        self.is_listening = False
        self.is_speaking = False
        self.conversation_active = True
        
        self.setup_components()
    
    def setup_components(self):
        """Initialize all premium components"""
        try:
            # Initialize Neural TTS
            logger.info("🎙️ Initializing premium neural voice system...")
            self.neural_tts = NeuralTTSEngine()
            
            if self.neural_tts.current_engine:
                engine_info = self.neural_tts.available_engines[self.neural_tts.current_engine]
                logger.info(f"✓ Using {engine_info['name']} - {engine_info['description']}")
            else:
                logger.error("❌ No neural TTS engine available")
                return False
            
            # Initialize Whisper for STT
            import whisper
            logger.info("🧠 Loading Whisper model...")
            self.whisper_model = whisper.load_model("base")
            logger.info("✓ Speech recognition ready")
            
            logger.info("🎉 Premium voice system ready!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize premium components: {e}")
            return False
    
    def listen_for_speech(self, duration: int = 6) -> Optional[str]:
        """Enhanced speech recognition"""
        try:
            import pyaudio
            import numpy as np
            
            # Audio settings
            CHUNK = 1024
            FORMAT = pyaudio.paInt16
            CHANNELS = 1
            RATE = 16000
            
            logger.info(f"🎤 Listening for natural speech ({duration}s)...")
            
            # Initialize PyAudio
            audio = pyaudio.PyAudio()
            
            # Open stream
            stream = audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK
            )
            
            frames = []
            
            # Record audio
            for i in range(0, int(RATE / CHUNK * duration)):
                data = stream.read(CHUNK)
                frames.append(data)
            
            # Stop recording
            stream.stop_stream()
            stream.close()
            audio.terminate()
            
            # Convert to numpy array
            audio_data = b''.join(frames)
            audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Use Whisper to transcribe
            logger.info("🔄 Processing speech...")
            result = self.whisper_model.transcribe(audio_np, language="en")
            text = result["text"].strip()
            
            if text and len(text) > 2:
                logger.info(f"👤 You: {text}")
                return text
            else:
                logger.info("🔇 No clear speech detected")
                return None
                
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return None
    
    async def get_intelligent_response(self, user_input: str) -> str:
        """Get intelligent response using conversation engine"""
        try:
            import httpx
            
            # Build AI prompt with personality and context
            prompt_data = self.conversation_engine.build_ai_prompt(user_input)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://localhost:11434/v1/chat/completions",
                    json=prompt_data,
                    timeout=45.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        ai_response = result["choices"][0]["message"]["content"]
                        
                        # Process response through conversation engine
                        processed_response = self.conversation_engine.process_ai_response(ai_response)
                        
                        return processed_response
                
                return "I'm having trouble thinking right now. Could you say that again?"
                
        except Exception as e:
            logger.error(f"AI response error: {e}")
            return "Sorry, I'm having some technical difficulties. Let's try again."
    
    async def premium_conversation_loop(self):
        """Premium conversation loop with neural voice"""
        logger.info("🎭 Starting premium neural voice conversation...")
        
        # Show available voice engines
        voice_info = self.neural_tts.get_available_voices()
        logger.info(f"🎙️ Voice Engine: {voice_info['current_engine']}")
        
        # Personalized greeting with neural voice
        greeting = self.conversation_engine.get_greeting()
        await self.neural_tts.speak_neural(greeting, "friendly")
        
        # Ask for name if new user
        if not self.conversation_engine.memory.user_name:
            name_prompt = "What should I call you?"
            await self.neural_tts.speak_neural(name_prompt, "curious")
            
            # Listen for name
            user_name = self.listen_for_speech(duration=5)
            if user_name:
                # Extract name
                name = user_name.split()[-1] if user_name else "friend"
                self.conversation_engine.set_user_name(name)
                
                name_response = f"Nice to meet you, {name}! I'll remember that."
                await self.neural_tts.speak_neural(name_response, "happy")
        
        logger.info("\n" + "="*80)
        logger.info("🎭 PREMIUM NEURAL VOICE CONVERSATION ACTIVE")
        logger.info("="*80)
        logger.info("Premium Features:")
        logger.info("  🎙️ High-quality neural voice synthesis")
        logger.info("  🧠 Intelligent conversation with memory")
        logger.info("  🎭 Emotional voice responses")
        logger.info("  💭 Context awareness and personality")
        logger.info("  🤝 Relationship building over time")
        logger.info("  🗣️ Natural, human-like speech patterns")
        logger.info("\nVoice Quality:")
        current_engine = self.neural_tts.current_engine
        if current_engine in self.neural_tts.available_engines:
            engine_info = self.neural_tts.available_engines[current_engine]
            logger.info(f"  Engine: {engine_info['name']}")
            logger.info(f"  Quality: {engine_info['quality']}")
            logger.info(f"  Description: {engine_info['description']}")
        logger.info("\nInstructions:")
        logger.info("  - Speak naturally when Josie is listening")
        logger.info("  - Experience premium voice quality")
        logger.info("  - Say 'goodbye' to end gracefully")
        logger.info("  - Press Ctrl+C to force quit")
        logger.info("="*80)
        
        try:
            conversation_count = 0
            while self.conversation_active:
                # Listen for user input
                user_speech = self.listen_for_speech(duration=7)
                
                if user_speech:
                    conversation_count += 1
                    
                    # Check for exit commands
                    if any(word in user_speech.lower() for word in ['goodbye', 'quit', 'exit', 'stop', 'bye bye']):
                        # Personalized farewell with neural voice
                        user_name = self.conversation_engine.memory.user_name or "friend"
                        relationship = self.conversation_engine.memory.relationship_level
                        
                        if relationship == "close_friend":
                            farewell = f"Goodbye {user_name}! I really enjoyed our chat. Can't wait to talk again soon!"
                        elif relationship == "friend":
                            farewell = f"See you later, {user_name}! Thanks for the great conversation!"
                        else:
                            farewell = f"Goodbye {user_name}! It was wonderful talking with you!"
                        
                        await self.neural_tts.speak_neural(farewell, "happy")
                        break
                    
                    # Get intelligent response
                    logger.info("🤖 Josie is thinking...")
                    start_time = time.time()
                    
                    ai_response = await self.get_intelligent_response(user_speech)
                    
                    response_time = time.time() - start_time
                    logger.info(f"⚡ Response generated in {response_time:.1f}s")
                    
                    # Detect emotion for voice
                    emotion = self.conversation_engine.context.user_mood
                    if emotion == "neutral":
                        emotion = "friendly"
                    
                    # Speak with premium neural voice
                    await self.neural_tts.speak_neural(ai_response, emotion)
                    
                    # Small pause before next listening cycle
                    await asyncio.sleep(0.5)
                    
                else:
                    # No speech detected - gentle prompt with neural voice
                    if conversation_count == 0:
                        prompt = "I'm here and listening. What would you like to talk about?"
                        await self.neural_tts.speak_neural(prompt, "encouraging")
                    else:
                        prompt = "I'm still here. Feel free to continue."
                        await self.neural_tts.speak_neural(prompt, "calm")
                    
                    await asyncio.sleep(1)
                    
        except KeyboardInterrupt:
            logger.info("\n🛑 Conversation interrupted")
            farewell = "Oh! Goodbye for now!"
            await self.neural_tts.speak_neural(farewell, "surprised")
        except Exception as e:
            logger.error(f"Conversation error: {e}")
            error_msg = "I'm sorry, I encountered an error. Let's try again later!"
            await self.neural_tts.speak_neural(error_msg, "sad")
    
    async def test_premium_voice(self):
        """Test premium voice capabilities"""
        logger.info("🧪 Testing premium neural voice system...")
        
        # Test different emotions with neural voice
        emotions_to_test = [
            ("happy", "I'm so happy to meet you! This is my happy voice!"),
            ("excited", "This is amazing! I'm so excited to talk with you!"),
            ("calm", "This is my calm, peaceful voice. Very relaxing."),
            ("friendly", "Hi there! This is my friendly, conversational voice."),
            ("thoughtful", "Hmm, this is how I sound when I'm thinking deeply.")
        ]
        
        for emotion, text in emotions_to_test:
            logger.info(f"Testing {emotion} voice...")
            await self.neural_tts.speak_neural(text, emotion)
            await asyncio.sleep(1)
        
        # Test microphone
        logger.info("Testing microphone...")
        test_speech = self.listen_for_speech(duration=3)
        if test_speech:
            response = f"Perfect! I heard you say: {test_speech}"
            await self.neural_tts.speak_neural(response, "excited")
            logger.info("✓ All premium systems working perfectly!")
        else:
            logger.warning("⚠️ Microphone test - no speech detected")
    
    def get_voice_info(self):
        """Get detailed voice system information"""
        if self.neural_tts:
            return self.neural_tts.get_available_voices()
        return {"error": "Neural TTS not initialized"}


async def main():
    """Main function"""
    try:
        agent = PremiumVoiceAgent()
        
        if not agent.neural_tts or not agent.neural_tts.current_engine:
            print("\n❌ Premium voice system failed to initialize")
            print("Available options:")
            print("1. Install edge-tts: pip install edge-tts")
            print("2. Install RealtimeTTS: pip install RealtimeTTS")
            print("3. Use enhanced pyttsx3 as fallback")
            return
        
        if len(sys.argv) > 1:
            if sys.argv[1].lower() == "test":
                await agent.test_premium_voice()
            elif sys.argv[1].lower() == "info":
                voice_info = agent.get_voice_info()
                print("\n🎙️ Premium Voice System Information:")
                print(f"Current Engine: {voice_info.get('current_engine', 'None')}")
                print("\nAvailable Engines:")
                for engine, info in voice_info.get('available_engines', {}).items():
                    print(f"  {engine}: {info['name']} ({info['quality']} quality)")
                    print(f"    {info['description']}")
            elif sys.argv[1].lower() == "chat":
                await agent.premium_conversation_loop()
            else:
                print("Usage: python premium_voice_chat.py [test|info|chat]")
        else:
            # Default: start premium conversation
            await agent.premium_conversation_loop()
            
    except Exception as e:
        logger.error(f"Premium voice system error: {e}")
        print("\n❌ Premium voice system failed to start")
        print("Try: python premium_voice_chat.py test")


if __name__ == "__main__":
    asyncio.run(main())

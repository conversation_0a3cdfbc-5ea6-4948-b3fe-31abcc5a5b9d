# Ultra-Performance AI Voice Agent Requirements
# Complete dependency list for advanced voice processing

# ======================================
# CORE DEPENDENCIES (Required)
# ======================================

# HTTP client for Ollama API communication
httpx>=0.24.0

# Audio processing and I/O
pyaudio>=0.2.11
numpy>=1.24.0

# Text-to-Speech (basic)
pyttsx3>=2.90

# ======================================
# ADVANCED AUDIO PROCESSING (Recommended)
# ======================================

# PyTorch ecosystem for neural models
torch>=2.0.0
torchaudio>=2.0.0

# Advanced speech recognition
faster-whisper>=0.9.0

# Audio processing and resampling
librosa>=0.10.0
soundfile>=0.12.0

# Voice Activity Detection
# silero-vad>=4.0.0  # Install separately: pip install silero-vad

# ======================================
# NEURAL TTS (Optional - Premium Quality)
# ======================================

# F5-TTS for premium voice synthesis
# f5-tts>=0.2.0  # Install separately when available

# ChatTTS for conversational synthesis  
# ChatTTS  # Install separately when available

# Coqui TTS for voice cloning
# coqui-tts>=0.13.0  # Install separately: pip install coqui-tts

# ======================================
# SYSTEM UTILITIES
# ======================================

# System monitoring and optimization
psutil>=5.9.0

# Async processing support
asyncio-mqtt>=0.11.0

# Data structures and utilities
dataclasses-json>=0.5.0

# ======================================
# DEVELOPMENT & TESTING (Optional)
# ======================================

# Testing framework
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Code quality
black>=23.0.0
flake8>=6.0.0

# Performance profiling
line-profiler>=4.0.0
memory-profiler>=0.60.0

# ======================================
# WINDOWS-SPECIFIC (For Windows users)
# ======================================

# Windows thread priority control
pywin32>=306; platform_system=="Windows"

# ======================================
# ALTERNATIVE AUDIO BACKENDS (Optional)
# ======================================

# Alternative to PyAudio
# sounddevice>=0.4.0

# Real-time audio processing
# python-rtaudio>=1.1.0

# ======================================
# INSTALLATION NOTES
# ======================================

# For GPU acceleration (CUDA):
# pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118

# For CPU-only (smaller download):
# pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

# For advanced TTS models:
# pip install silero-vad
# pip install coqui-tts
# pip install git+https://github.com/SWivid/F5-TTS.git  # When available

# For audio codec support:
# pip install ffmpeg-python

# System dependencies (Ubuntu/Debian):
# sudo apt-get update
# sudo apt-get install portaudio19-dev python3-pyaudio
# sudo apt-get install ffmpeg

# System dependencies (macOS):
# brew install portaudio
# brew install ffmpeg

# System dependencies (Windows):
# Audio drivers should be included with PyAudio wheel
"""
Human-Like Voice Conversation System
Natural, flowing conversations with memory, personality, and emotions
"""

import asyncio
import logging
import sys
import threading
import time
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import our advanced systems directly
sys.path.append(str(project_root / "local_ai_voice_agent" / "core"))
from conversation_engine import ConversationEngine
from advanced_tts import AdvancedTTS

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class HumanLikeVoiceAgent:
    """Advanced human-like voice conversation agent"""
    
    def __init__(self):
        self.conversation_engine = ConversationEngine()
        self.advanced_tts = AdvancedTTS()
        self.whisper_model = None
        self.is_listening = False
        self.is_speaking = False
        self.conversation_active = True
        
        self.setup_components()
    
    def setup_components(self):
        """Initialize all components"""
        try:
            # Initialize Whisper for STT
            import whisper
            logger.info("🧠 Loading Whisper model...")
            self.whisper_model = whisper.load_model("base")
            logger.info("✓ Speech recognition ready")
            
            # Test TTS
            logger.info("🗣️ Initializing advanced voice synthesis...")
            logger.info("✓ Human-like voice ready")
            
            logger.info("🎉 All advanced systems initialized!")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    def listen_for_speech(self, duration: int = 6) -> Optional[str]:
        """Enhanced speech recognition with better processing"""
        try:
            import pyaudio
            import numpy as np
            
            # Audio settings optimized for speech
            CHUNK = 1024
            FORMAT = pyaudio.paInt16
            CHANNELS = 1
            RATE = 16000
            
            logger.info(f"🎤 Listening... (speak naturally for {duration} seconds)")
            
            # Initialize PyAudio
            audio = pyaudio.PyAudio()
            
            # Open stream
            stream = audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK
            )
            
            frames = []
            
            # Record audio with energy detection
            for i in range(0, int(RATE / CHUNK * duration)):
                data = stream.read(CHUNK)
                frames.append(data)
            
            # Stop recording
            stream.stop_stream()
            stream.close()
            audio.terminate()
            
            # Convert to numpy array
            audio_data = b''.join(frames)
            audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Use Whisper to transcribe
            logger.info("🔄 Processing your speech...")
            result = self.whisper_model.transcribe(audio_np, language="en")
            text = result["text"].strip()
            
            if text and len(text) > 2:  # Filter out very short/noise
                logger.info(f"👤 You: {text}")
                return text
            else:
                logger.info("🔇 No clear speech detected")
                return None
                
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return None
    
    async def get_intelligent_response(self, user_input: str) -> str:
        """Get intelligent response using conversation engine"""
        try:
            import httpx
            
            # Build AI prompt with personality and context
            prompt_data = self.conversation_engine.build_ai_prompt(user_input)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://localhost:11434/v1/chat/completions",
                    json=prompt_data,
                    timeout=45.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        ai_response = result["choices"][0]["message"]["content"]
                        
                        # Process response through conversation engine
                        processed_response = self.conversation_engine.process_ai_response(ai_response)
                        
                        return processed_response
                
                return "I'm having trouble thinking right now. Could you say that again?"
                
        except Exception as e:
            logger.error(f"AI response error: {e}")
            return "Sorry, I'm having some technical difficulties. Let's try again."
    
    async def conversation_loop(self):
        """Main human-like conversation loop"""
        logger.info("🎙️ Starting human-like conversation with Josie...")
        
        # Personalized greeting
        greeting = self.conversation_engine.get_greeting()
        await self.advanced_tts.speak_with_emotion(greeting, "friendly")
        
        # Ask for name if new user
        if not self.conversation_engine.memory.user_name:
            name_prompt = "By the way, what should I call you?"
            await self.advanced_tts.speak_with_emotion(name_prompt, "curious")
            
            # Listen for name
            user_name = self.listen_for_speech(duration=5)
            if user_name:
                # Extract name (simple approach)
                name = user_name.split()[-1] if user_name else "friend"
                self.conversation_engine.set_user_name(name)
                
                name_response = f"Nice to meet you, {name}! I'll remember that."
                await self.advanced_tts.speak_with_emotion(name_response, "happy")
        
        logger.info("\n" + "="*70)
        logger.info("🎭 HUMAN-LIKE CONVERSATION ACTIVE")
        logger.info("="*70)
        logger.info("Features:")
        logger.info("  🧠 Intelligent conversation with memory")
        logger.info("  🎭 Emotional voice responses")
        logger.info("  💭 Context awareness and personality")
        logger.info("  🤝 Relationship building over time")
        logger.info("  🗣️ Natural speech patterns")
        logger.info("\nInstructions:")
        logger.info("  - Speak naturally when Josie is listening")
        logger.info("  - Wait for her to finish speaking")
        logger.info("  - Say 'goodbye' to end gracefully")
        logger.info("  - Press Ctrl+C to force quit")
        logger.info("="*70)
        
        try:
            conversation_count = 0
            while self.conversation_active:
                # Listen for user input
                user_speech = self.listen_for_speech(duration=7)
                
                if user_speech:
                    conversation_count += 1
                    
                    # Check for exit commands
                    if any(word in user_speech.lower() for word in ['goodbye', 'quit', 'exit', 'stop', 'bye bye']):
                        # Personalized farewell
                        user_name = self.conversation_engine.memory.user_name or "friend"
                        relationship = self.conversation_engine.memory.relationship_level
                        
                        if relationship == "close_friend":
                            farewell = f"Goodbye {user_name}! I really enjoyed our chat. Can't wait to talk again soon!"
                        elif relationship == "friend":
                            farewell = f"See you later, {user_name}! Thanks for the great conversation!"
                        else:
                            farewell = f"Goodbye {user_name}! It was wonderful talking with you!"
                        
                        await self.advanced_tts.speak_with_emotion(farewell, "warm")
                        break
                    
                    # Get intelligent response
                    logger.info("🤖 Josie is thinking...")
                    start_time = time.time()
                    
                    ai_response = await self.get_intelligent_response(user_speech)
                    
                    response_time = time.time() - start_time
                    logger.info(f"⚡ Response generated in {response_time:.1f}s")
                    
                    # Detect emotion for voice
                    emotion = self.conversation_engine.context.user_mood
                    if emotion == "neutral":
                        emotion = "friendly"
                    
                    # Speak with appropriate emotion
                    await self.advanced_tts.speak_with_emotion(ai_response, emotion)
                    
                    # Small pause before next listening cycle
                    await asyncio.sleep(0.5)
                    
                else:
                    # No speech detected - gentle prompt
                    if conversation_count == 0:
                        prompt = "I'm here and listening. What would you like to talk about?"
                        await self.advanced_tts.speak_with_emotion(prompt, "encouraging")
                    else:
                        prompt = "I'm still here. Feel free to continue."
                        await self.advanced_tts.speak_with_emotion(prompt, "patient")
                    
                    await asyncio.sleep(1)
                    
        except KeyboardInterrupt:
            logger.info("\n🛑 Conversation interrupted")
            farewell = "Oh! Goodbye for now!"
            await self.advanced_tts.speak_with_emotion(farewell, "surprised")
        except Exception as e:
            logger.error(f"Conversation error: {e}")
            error_msg = "I'm sorry, I encountered an error. Let's try again later!"
            await self.advanced_tts.speak_with_emotion(error_msg, "apologetic")
    
    async def test_all_systems(self):
        """Test all advanced systems"""
        logger.info("🧪 Testing all advanced systems...")
        
        # Test TTS with emotions
        logger.info("Testing emotional voice synthesis...")
        emotions = ["happy", "excited", "thoughtful", "empathetic", "friendly"]
        
        for emotion in emotions:
            test_text = f"This is how I sound when I'm {emotion}!"
            await self.advanced_tts.speak_with_emotion(test_text, emotion)
            await asyncio.sleep(0.5)
        
        # Test conversation memory
        logger.info("Testing conversation memory...")
        test_input = "Hello, I'm testing the system"
        response = await self.get_intelligent_response(test_input)
        logger.info(f"Memory test response: {response}")
        
        # Test microphone
        logger.info("Testing microphone...")
        test_speech = self.listen_for_speech(duration=3)
        if test_speech:
            logger.info("✓ All systems working perfectly!")
            success_msg = "All systems are working perfectly! I'm ready for natural conversation!"
            await self.advanced_tts.speak_with_emotion(success_msg, "excited")
        else:
            logger.warning("⚠️ Microphone test failed")
    
    def get_system_status(self):
        """Get status of all systems"""
        status = {
            "conversation_engine": "✓ Active" if self.conversation_engine else "✗ Failed",
            "advanced_tts": "✓ Active" if self.advanced_tts else "✗ Failed", 
            "whisper_stt": "✓ Active" if self.whisper_model else "✗ Failed",
            "memory_system": "✓ Active",
            "personality_system": "✓ Active",
            "emotional_voice": "✓ Active"
        }
        
        return status


async def main():
    """Main function"""
    try:
        agent = HumanLikeVoiceAgent()
        
        if len(sys.argv) > 1:
            if sys.argv[1].lower() == "test":
                await agent.test_all_systems()
            elif sys.argv[1].lower() == "status":
                status = agent.get_system_status()
                print("\n🔍 System Status:")
                for system, state in status.items():
                    print(f"  {system}: {state}")
            elif sys.argv[1].lower() == "chat":
                await agent.conversation_loop()
            else:
                print("Usage: python human_like_voice_chat.py [test|status|chat]")
        else:
            # Default: start human-like conversation
            await agent.conversation_loop()
            
    except Exception as e:
        logger.error(f"System error: {e}")
        print("\n❌ Advanced voice system failed to start")
        print("Try: python human_like_voice_chat.py test")


if __name__ == "__main__":
    asyncio.run(main())

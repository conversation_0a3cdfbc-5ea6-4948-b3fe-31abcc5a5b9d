# Local AI Voice Agent Model Configuration

# Large Language Models (Ollama)
llm:
  primary:
    name: "JOSIEFIED-Qwen3"
    model_id: "goekden<PERSON><PERSON><PERSON><PERSON>/JOSIEFIED-Qwen3:14b"
    size: "9.0 GB"
    purpose: "Primary Voice Agent"
    endpoint: "http://localhost:11434/v1"
    
  vision:
    name: "Qwen 2.5 VL"
    model_id: "qwen2.5vl:32b"
    size: "21 GB"
    purpose: "Vision + Language"
    endpoint: "http://localhost:11434/v1"
    
  alternatives:
    - name: "Llama 3.2"
      model_id: "llama3.2:latest"
      size: "2.0 GB"
      purpose: "Lightweight General Purpose"
    - name: "DeepSeek R1"
      model_id: "deepseek-r1:latest"
      size: "5.2 GB"
      purpose: "Latest Reasoning"

# Speech-to-Text Models
stt:
  primary:
    name: "Whisper Large V3"
    model: "large-v3"
    language: "en"
    device: "auto"  # auto, cpu, cuda
    
  alternatives:
    - name: "Whisper Large V3 Turbo"
      model: "large-v3-turbo"
      language: "en"
    - name: "Whisper Medium"
      model: "medium"
      language: "en"

# Text-to-Speech Systems
tts:
  primary:
    name: "Windows SAPI"
    engine: "sapi"
    voice: "Microsoft Zira Desktop"
    rate: 200
    volume: 0.9
    
  alternatives:
    - name: "pyttsx3"
      engine: "pyttsx3"
      voice: "default"
      rate: 200
      volume: 0.9
    - name: "espeak"
      engine: "espeak"
      voice: "en"
      rate: 175

# Voice Activity Detection
vad:
  name: "Silero VAD"
  model: "silero_vad"
  threshold: 0.5
  min_silence_duration: 0.5
  max_speech_duration: 30.0

# Turn Detection
turn_detection:
  name: "Multilingual Model"
  model: "multilingual"
  min_endpointing_delay: 0.5
  max_endpointing_delay: 5.0

# Vision Processing
vision:
  image_formats: ["jpg", "jpeg", "png", "bmp", "webp"]
  max_image_size: [1024, 1024]
  preprocessing:
    resize: true
    normalize: true
    
# Performance Settings
performance:
  max_concurrent_requests: 3
  timeout_seconds: 30
  memory_limit_gb: 16
  gpu_memory_fraction: 0.8
  
# Agent Behavior
agent:
  instructions: |
    You are a helpful AI voice assistant with vision capabilities. 
    You can see images, hear speech, and respond naturally through voice.
    Keep responses concise and conversational for voice interactions.
    When processing images, describe what you see clearly and answer questions about the visual content.
  
  personality:
    friendly: true
    curious: true
    helpful: true
    concise: true
    
  capabilities:
    voice_chat: true
    vision_processing: true
    image_analysis: true
    conversation_memory: true
    function_calling: true

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/voice_agent.log"
  max_file_size_mb: 100
  backup_count: 5

"""
🎤 Ultra-Low Latency Audio Input Thread
Real-time audio capture with <16ms latency
"""

import numpy as np
import time
import threading
from typing import Optional, Callable
import logging

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("⚠️ PyAudio not available. Install with: pip install pyaudio")

from .threading_infrastructure import (
    HighPriorityThread,
    Lock<PERSON>reeQueue, 
    LockFreeRingBuffer,
    AudioChunk,
    get_optimal_chunk_size
)

logger = logging.getLogger(__name__)

class AudioInputThread(HighPriorityThread):
    """
    Highest priority thread for ultra-low latency audio capture
    - Hardware-level audio streaming
    - 16ms chunk processing for minimal latency
    - Ring buffer for continuous audio history
    - Automatic noise gating and preprocessing
    """
    
    def __init__(self, 
                 output_queue: LockFreeQueue,
                 sample_rate: int = 48000,
                 channels: int = 1,
                 chunk_size: Optional[int] = None,
                 device_index: Optional[int] = None):
        
        super().__init__("AudioInput", priority="highest", target_fps=3000)  # 3000 Hz for 16ms chunks
        
        # Audio configuration
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_size = chunk_size or get_optimal_chunk_size(sample_rate, 16.0)  # 16ms target
        self.device_index = device_index
        
        # Output queue for processed audio
        self.output_queue = output_queue
        
        # Audio components
        self.pa = None
        self.stream = None
        self.audio_buffer = LockFreeRingBuffer(sample_rate, channels, buffer_seconds=2.0)
        
        # Preprocessing components
        self.noise_gate = NoiseGate(threshold_db=-40, sample_rate=sample_rate)
        self.audio_preprocessor = AudioPreprocessor(sample_rate)
        
        # Performance tracking
        self.chunk_counter = 0
        self.last_chunk_time = time.time()
        self.callback_times = []
        
        # Audio stream settings
        self.stream_settings = {
            'format': pyaudio.paFloat32,
            'channels': self.channels,
            'rate': self.sample_rate,
            'input': True,
            'frames_per_buffer': self.chunk_size,
            'start': False  # We'll start manually
        }
        
    def initialize_audio_system(self) -> bool:
        """Initialize PyAudio and audio stream"""
        if not PYAUDIO_AVAILABLE:
            logger.error("PyAudio not available - cannot capture audio")
            return False
            
        try:
            # Initialize PyAudio
            self.pa = pyaudio.PyAudio()
            
            # List available devices for debugging
            self._log_audio_devices()
            
            # Auto-detect best input device if not specified
            if self.device_index is None:
                self.device_index = self._find_best_input_device()
            
            # Create audio stream with callback
            self.stream = self.pa.open(
                input_device_index=self.device_index,
                stream_callback=self._audio_callback,
                **self.stream_settings
            )
            
            logger.info(f"✅ Audio stream initialized:")
            logger.info(f"   Device: {self.device_index}")
            logger.info(f"   Sample Rate: {self.sample_rate} Hz")
            logger.info(f"   Chunk Size: {self.chunk_size} samples ({self.chunk_size/self.sample_rate*1000:.1f}ms)")
            logger.info(f"   Channels: {self.channels}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize audio system: {e}")
            return False
    
    def _log_audio_devices(self):
        """Log available audio devices for debugging"""
        logger.info("🎤 Available audio input devices:")
        device_count = self.pa.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.pa.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    logger.info(f"   {i}: {device_info['name']} ({device_info['maxInputChannels']} ch)")
            except Exception:
                continue
    
    def _find_best_input_device(self) -> int:
        """Find the best available input device"""
        try:
            # Try to find default input device
            default_device = self.pa.get_default_input_device_info()
            device_index = default_device['index']
            
            logger.info(f"🎤 Using default input device: {default_device['name']}")
            return device_index
            
        except Exception:
            # Fallback to first available input device
            device_count = self.pa.get_device_count()
            for i in range(device_count):
                try:
                    device_info = self.pa.get_device_info_by_index(i)
                    if device_info['maxInputChannels'] > 0:
                        logger.info(f"🎤 Using fallback input device: {device_info['name']}")
                        return i
                except Exception:
                    continue
            
            logger.error("❌ No input devices found")
            return 0
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """
        Real-time audio callback - CRITICAL PATH
        This runs in audio thread with minimal processing
        """
        callback_start = time.time()
        
        try:
            # Convert audio data to numpy array
            audio_data = np.frombuffer(in_data, dtype=np.float32)
            
            # Basic validation
            if len(audio_data) != self.chunk_size:
                logger.warning(f"⚠️ Unexpected chunk size: {len(audio_data)} vs {self.chunk_size}")
                return (None, pyaudio.paContinue)
            
            # Store in ring buffer for history
            self.audio_buffer.write_chunk(audio_data)
            
            # Quick preprocessing
            processed_audio = self._preprocess_audio_chunk(audio_data)
            
            # Create audio chunk for pipeline
            chunk = AudioChunk(
                data=processed_audio,
                timestamp=callback_start,
                sample_rate=self.sample_rate,
                channels=self.channels,
                chunk_id=self.chunk_counter,
                confidence=1.0
            )
            
            # Send to processing pipeline (non-blocking)
            if not self.output_queue.put_nowait(chunk):
                self.performance_stats['errors'] += 1
            
            self.chunk_counter += 1
            
            # Performance tracking
            callback_time = time.time() - callback_start
            self.callback_times.append(callback_time)
            if len(self.callback_times) > 1000:
                self.callback_times = self.callback_times[-1000:]
            
            return (None, pyaudio.paContinue)
            
        except Exception as e:
            logger.error(f"❌ Audio callback error: {e}")
            self.performance_stats['errors'] += 1
            return (None, pyaudio.paContinue)
    
    def _preprocess_audio_chunk(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Minimal preprocessing in audio callback
        - Noise gating
        - Basic normalization
        - DC offset removal
        """
        try:
            # Remove DC offset
            audio_data = audio_data - np.mean(audio_data)
            
            # Apply noise gate
            audio_data = self.noise_gate.process(audio_data)
            
            # Basic preprocessing
            audio_data = self.audio_preprocessor.process_chunk(audio_data)
            
            return audio_data
            
        except Exception as e:
            logger.error(f"❌ Preprocessing error: {e}")
            return audio_data  # Return original on error
    
    def start_audio_stream(self) -> bool:
        """Start the audio stream"""
        if not self.stream:
            logger.error("❌ Audio stream not initialized")
            return False
        
        try:
            self.stream.start_stream()
            logger.info("🎤 Audio stream started")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to start audio stream: {e}")
            return False
    
    def stop_audio_stream(self):
        """Stop the audio stream"""
        if self.stream and self.stream.is_active():
            try:
                self.stream.stop_stream()
                logger.info("🛑 Audio stream stopped")
            except Exception as e:
                logger.error(f"❌ Error stopping audio stream: {e}")
    
    def process_loop(self):
        """Main audio input processing loop"""
        if not self.initialize_audio_system():
            logger.error("❌ Failed to initialize audio system")
            return
        
        if not self.start_audio_stream():
            logger.error("❌ Failed to start audio stream")
            return
        
        logger.info("🎤 Audio input thread processing started")
        
        # Main loop - audio processing happens in callback
        try:
            while self.running.is_set() and not self.shutdown_event.is_set():
                # Monitor performance
                self._monitor_audio_performance()
                
                # Sleep briefly - main work is in callback
                time.sleep(0.1)
                
        except Exception as e:
            logger.error(f"❌ Audio processing error: {e}")
        finally:
            self._cleanup_audio_system()
    
    def _monitor_audio_performance(self):
        """Monitor audio performance and adjust if needed"""
        current_time = time.time()
        
        # Calculate actual FPS
        if current_time - self.last_chunk_time >= 1.0:
            chunks_per_second = self.chunk_counter / (current_time - self.last_chunk_time) if (current_time - self.last_chunk_time) > 0 else 0
            expected_fps = self.sample_rate / self.chunk_size
            
            if chunks_per_second < expected_fps * 0.9:  # More than 10% drop
                logger.warning(f"⚠️ Audio FPS below target: {chunks_per_second:.1f} vs {expected_fps:.1f}")
            
            # Reset counters
            self.chunk_counter = 0
            self.last_chunk_time = current_time
        
        # Check callback times
        if len(self.callback_times) > 100:
            avg_callback_time = np.mean(self.callback_times[-100:]) * 1000  # Convert to ms
            max_callback_time = np.max(self.callback_times[-100:]) * 1000
            
            if avg_callback_time > 5.0:  # More than 5ms average
                logger.warning(f"⚠️ High audio callback latency: avg={avg_callback_time:.1f}ms, max={max_callback_time:.1f}ms")
    
    def _cleanup_audio_system(self):
        """Clean up audio resources"""
        logger.info("🧹 Cleaning up audio system...")
        
        self.stop_audio_stream()
        
        if self.stream:
            try:
                self.stream.close()
            except Exception as e:
                logger.error(f"Error closing stream: {e}")
        
        if self.pa:
            try:
                self.pa.terminate()
            except Exception as e:
                logger.error(f"Error terminating PyAudio: {e}")
        
        logger.info("✅ Audio system cleanup completed")
    
    def get_audio_stats(self) -> dict:
        """Get detailed audio statistics"""
        stats = self.get_performance_report()
        
        # Add audio-specific stats
        if len(self.callback_times) > 0:
            stats.update({
                'avg_callback_time_ms': np.mean(self.callback_times) * 1000,
                'max_callback_time_ms': np.max(self.callback_times) * 1000,
                'callback_jitter_ms': np.std(self.callback_times) * 1000,
                'buffer_usage_percent': self.audio_buffer.get_buffer_usage(),
                'total_chunks_processed': self.chunk_counter,
                'queue_stats': self.output_queue.get_stats()
            })
        
        return stats

class NoiseGate:
    """
    Real-time noise gate for audio preprocessing
    - Configurable threshold
    - Smooth gating to avoid clicks
    - Minimal processing latency
    """
    
    def __init__(self, threshold_db: float = -40.0, sample_rate: int = 48000):
        self.threshold_db = threshold_db
        self.threshold_linear = 10 ** (threshold_db / 20.0)
        self.sample_rate = sample_rate
        
        # Gate state
        self.gate_open = False
        self.smoothing_factor = 0.1  # For smooth gate transitions
        self.current_gain = 0.0
        
    def process(self, audio_chunk: np.ndarray) -> np.ndarray:
        """Apply noise gate to audio chunk"""
        if len(audio_chunk) == 0:
            return audio_chunk
        
        # Calculate RMS level
        rms_level = np.sqrt(np.mean(audio_chunk ** 2))
        
        # Determine gate state
        if rms_level > self.threshold_linear:
            target_gain = 1.0
            self.gate_open = True
        else:
            target_gain = 0.0
            self.gate_open = False
        
        # Smooth gain transitions
        self.current_gain += (target_gain - self.current_gain) * self.smoothing_factor
        
        # Apply gain
        return audio_chunk * self.current_gain

class AudioPreprocessor:
    """
    Minimal audio preprocessing for real-time processing
    - High-pass filtering
    - Basic normalization
    - Optional auto-gain control
    """
    
    def __init__(self, sample_rate: int = 48000):
        self.sample_rate = sample_rate
        
        # High-pass filter (remove DC and low frequency noise)
        self.hp_filter = HighPassFilter(cutoff_freq=80, sample_rate=sample_rate)
        
        # Auto-gain control
        self.agc = AutoGainControl(target_level=0.1, sample_rate=sample_rate)
        
    def process_chunk(self, audio_chunk: np.ndarray) -> np.ndarray:
        """Process audio chunk with minimal latency"""
        if len(audio_chunk) == 0:
            return audio_chunk
        
        try:
            # High-pass filter
            audio_chunk = self.hp_filter.process(audio_chunk)
            
            # Auto-gain control
            audio_chunk = self.agc.process(audio_chunk)
            
            # Ensure values are in valid range
            audio_chunk = np.clip(audio_chunk, -1.0, 1.0)
            
            return audio_chunk
            
        except Exception as e:
            logger.error(f"❌ Audio preprocessing error: {e}")
            return audio_chunk

class HighPassFilter:
    """Simple high-pass filter for DC removal"""
    
    def __init__(self, cutoff_freq: float = 80.0, sample_rate: int = 48000):
        self.cutoff_freq = cutoff_freq
        self.sample_rate = sample_rate
        
        # Simple first-order high-pass filter
        omega = 2 * np.pi * cutoff_freq / sample_rate
        self.alpha = omega / (omega + 1)
        
        # Filter state
        self.prev_input = 0.0
        self.prev_output = 0.0
    
    def process(self, audio_chunk: np.ndarray) -> np.ndarray:
        """Apply high-pass filter"""
        if len(audio_chunk) == 0:
            return audio_chunk
        
        # Simple implementation for real-time processing
        filtered = np.zeros_like(audio_chunk)
        
        for i, sample in enumerate(audio_chunk):
            if i == 0:
                filtered[i] = self.alpha * (self.prev_output + sample - self.prev_input)
                self.prev_input = sample
            else:
                filtered[i] = self.alpha * (filtered[i-1] + sample - audio_chunk[i-1])
        
        self.prev_output = filtered[-1]
        return filtered

class AutoGainControl:
    """Simple auto-gain control for consistent levels"""
    
    def __init__(self, target_level: float = 0.1, sample_rate: int = 48000):
        self.target_level = target_level
        self.sample_rate = sample_rate
        
        # AGC parameters
        self.current_gain = 1.0
        self.attack_time = 0.1  # seconds
        self.release_time = 1.0  # seconds
        
        # Smoothing factors
        self.attack_factor = 1.0 - np.exp(-1.0 / (self.attack_time * sample_rate))
        self.release_factor = 1.0 - np.exp(-1.0 / (self.release_time * sample_rate))
    
    def process(self, audio_chunk: np.ndarray) -> np.ndarray:
        """Apply auto-gain control"""
        if len(audio_chunk) == 0:
            return audio_chunk
        
        # Calculate RMS level
        rms_level = np.sqrt(np.mean(audio_chunk ** 2))
        
        if rms_level > 0:
            # Calculate required gain
            required_gain = self.target_level / rms_level
            
            # Smooth gain changes
            if required_gain < self.current_gain:
                # Attack (reduce gain quickly)
                self.current_gain += (required_gain - self.current_gain) * self.attack_factor
            else:
                # Release (increase gain slowly)
                self.current_gain += (required_gain - self.current_gain) * self.release_factor
            
            # Limit gain range
            self.current_gain = np.clip(self.current_gain, 0.1, 10.0)
        
        # Apply gain
        return audio_chunk * self.current_gain

# Test function
def test_audio_input():
    """Test audio input functionality"""
    print("🧪 Testing Audio Input Thread...")
    
    if not PYAUDIO_AVAILABLE:
        print("❌ PyAudio not available - cannot test audio input")
        return
    
    # Create test queue
    test_queue = LockFreeQueue(maxsize=100, name="AudioTest")
    
    # Create audio input thread
    audio_thread = AudioInputThread(
        output_queue=test_queue,
        sample_rate=48000,
        chunk_size=768  # 16ms at 48kHz
    )
    
    try:
        # Start thread
        audio_thread.start()
        
        # Let it run for a few seconds
        print("🎤 Recording audio for 5 seconds...")
        start_time = time.time()
        
        chunk_count = 0
        while time.time() - start_time < 5.0:
            chunk = test_queue.get_nowait()
            if chunk:
                chunk_count += 1
                if chunk_count % 100 == 0:  # Log every 100 chunks (~1.6 seconds)
                    print(f"   Processed {chunk_count} chunks, RMS: {np.sqrt(np.mean(chunk.data**2)):.4f}")
            
            time.sleep(0.001)
        
        print(f"✅ Test completed: {chunk_count} chunks processed")
        print(f"📊 Audio stats: {audio_thread.get_audio_stats()}")
        
    finally:
        audio_thread.shutdown()
        audio_thread.join(timeout=2.0)

if __name__ == "__main__":
    test_audio_input()
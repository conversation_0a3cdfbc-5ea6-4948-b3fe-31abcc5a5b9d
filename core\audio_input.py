"""
Audio Input Thread
High-performance audio capture with ultra-low latency
"""

import numpy as np
import time
import logging
from typing import Optional, Dict, Any, List
import threading

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("WARNING: PyAudio not available. Install with: pip install pyaudio")

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)

logger = logging.getLogger(__name__)


class AudioInputThread(HighPriorityThread):
    """High-performance audio input thread"""
    
    def __init__(self,
                 output_queue: LockFreeQueue,
                 sample_rate: int = 48000,
                 channels: int = 1,
                 chunk_size: int = 1024,
                 device_index: Optional[int] = None):
        
        super().__init__("AudioInput", priority="highest", target_fps=int(sample_rate / chunk_size))
        
        # Audio configuration
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_size = chunk_size
        self.device_index = device_index
        
        # Output queue
        self.output_queue = output_queue
        
        # PyAudio components
        self.pa = None
        self.input_stream = None
        
        # Audio processing
        self.audio_format = pyaudio.paFloat32 if PYAUDIO_AVAILABLE else None
        self.bytes_per_sample = 4  # float32
        
        # Performance tracking
        self.frames_captured = 0
        self.bytes_captured = 0
        self.overruns = 0
        self.underruns = 0
        
        # Audio device info
        self.device_info = None
        self.selected_device_name = "Unknown"
        
        logger.info(f"AUDIO_IN: Initialized (rate: {sample_rate}, channels: {channels}, chunk: {chunk_size})")
    
    def initialize(self) -> bool:
        """Initialize audio input system"""
        try:
            if not PYAUDIO_AVAILABLE:
                logger.error("FATAL: PyAudio not available - cannot initialize audio input")
                return False
            
            # Initialize PyAudio
            self.pa = pyaudio.PyAudio()
            
            # Select audio device
            if not self._select_audio_device():
                return False
            
            # Create input stream
            if not self._create_input_stream():
                return False
            
            logger.info(f"AUDIO_IN: Initialized successfully with device: {self.selected_device_name}")
            return True
            
        except Exception as e:
            logger.error(f"FATAL: Failed to initialize audio input: {e}")
            return False
    
    def _select_audio_device(self) -> bool:
        """Select optimal audio input device"""
        try:
            device_count = self.pa.get_device_count()
            
            if self.device_index is not None:
                # Use specified device
                if 0 <= self.device_index < device_count:
                    self.device_info = self.pa.get_device_info_by_index(self.device_index)
                    self.selected_device_name = self.device_info['name']
                    logger.info(f"DEVICE: Using specified device {self.device_index}: {self.selected_device_name}")
                    return True
                else:
                    logger.error(f"ERROR: Invalid device index {self.device_index}")
                    return False
            
            # Auto-select best device
            best_device = None
            best_score = -1
            
            for i in range(device_count):
                try:
                    device_info = self.pa.get_device_info_by_index(i)
                    
                    # Skip output-only devices
                    if device_info['maxInputChannels'] == 0:
                        continue
                    
                    # Calculate device score
                    score = self._calculate_device_score(device_info)
                    
                    if score > best_score:
                        best_score = score
                        best_device = i
                        
                    logger.debug(f"DEVICE: {i}: {device_info['name']} (score: {score})")
                    
                except Exception as e:
                    logger.debug(f"DEBUG: Error checking device {i}: {e}")
                    continue
            
            if best_device is not None:
                self.device_index = best_device
                self.device_info = self.pa.get_device_info_by_index(best_device)
                self.selected_device_name = self.device_info['name']
                logger.info(f"DEVICE: Auto-selected device {best_device}: {self.selected_device_name}")
                return True
            else:
                logger.error("ERROR: No suitable audio input device found")
                return False
                
        except Exception as e:
            logger.error(f"ERROR: Device selection failed: {e}")
            return False
    
    def _calculate_device_score(self, device_info: Dict[str, Any]) -> float:
        """Calculate device suitability score"""
        score = 0.0
        
        # Prefer devices with higher input channel count
        score += device_info['maxInputChannels'] * 10
        
        # Prefer devices that support our sample rate
        try:
            if self.pa.is_format_supported(
                rate=self.sample_rate,
                input_device=device_info['index'],
                input_channels=self.channels,
                input_format=self.audio_format
            ):
                score += 50
        except:
            pass
        
        # Prefer devices with "microphone" or "input" in name
        name_lower = device_info['name'].lower()
        if 'microphone' in name_lower or 'mic' in name_lower:
            score += 30
        if 'input' in name_lower:
            score += 20
        
        # Prefer USB/external devices over built-in
        if 'usb' in name_lower or 'external' in name_lower:
            score += 15
        
        # Prefer devices with low latency
        if device_info.get('defaultLowInputLatency', 1.0) < 0.1:
            score += 25
        
        return score
    
    def _create_input_stream(self) -> bool:
        """Create PyAudio input stream"""
        try:
            # Calculate buffer size for low latency
            frames_per_buffer = self.chunk_size
            
            self.input_stream = self.pa.open(
                format=self.audio_format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.device_index,
                frames_per_buffer=frames_per_buffer,
                stream_callback=None,  # Use blocking mode for better control
                start=False
            )
            
            logger.info(f"STREAM: Audio input stream created (buffer: {frames_per_buffer} frames)")
            return True
            
        except Exception as e:
            logger.error(f"ERROR: Failed to create input stream: {e}")
            return False
    
    def run_processing_loop(self):
        """Main audio capture loop"""
        try:
            # Start the audio stream
            self.input_stream.start_stream()
            logger.info("CAPTURE: Audio capture started")
            
            while not self.should_stop:
                try:
                    # Read audio data
                    start_time = time.time()
                    audio_data = self.input_stream.read(
                        self.chunk_size,
                        exception_on_overflow=False
                    )
                    capture_time = time.time() - start_time
                    
                    # Convert to numpy array
                    audio_array = np.frombuffer(audio_data, dtype=np.float32)
                    
                    # Reshape for multi-channel if needed
                    if self.channels > 1:
                        audio_array = audio_array.reshape(-1, self.channels)
                    
                    # Create processing result
                    result = ProcessingResult(
                        data={
                            'audio_data': audio_array,
                            'sample_rate': self.sample_rate,
                            'channels': self.channels,
                            'chunk_size': self.chunk_size,
                            'capture_time_ms': capture_time * 1000,
                            'device_name': self.selected_device_name
                        },
                        timestamp=time.time(),
                        processing_time_ms=capture_time * 1000,
                        metadata={
                            'source': 'audio_input',
                            'device_index': self.device_index
                        }
                    )
                    
                    # Send to output queue
                    success = self.output_queue.put_nowait(result)
                    if success:
                        self.frames_captured += 1
                        self.bytes_captured += len(audio_data)
                    else:
                        self.overruns += 1
                        logger.warning("WARNING: Audio input queue full - dropping frame")
                    
                    # Update performance stats
                    self.update_performance_stats(capture_time)
                    
                except Exception as e:
                    logger.error(f"ERROR: Audio capture error: {e}")
                    self.underruns += 1
                    time.sleep(0.001)  # Brief pause to prevent tight error loop
                    
        except Exception as e:
            logger.error(f"FATAL: Audio capture loop failed: {e}")
        finally:
            self._stop_audio_stream()
    
    def _stop_audio_stream(self):
        """Stop audio stream safely"""
        try:
            if self.input_stream:
                if self.input_stream.is_active():
                    self.input_stream.stop_stream()
                self.input_stream.close()
                logger.info("STOP: Audio input stream stopped")
        except Exception as e:
            logger.error(f"ERROR: Error stopping audio stream: {e}")
    
    def cleanup(self):
        """Cleanup audio resources"""
        try:
            self._stop_audio_stream()
            
            if self.pa:
                self.pa.terminate()
                logger.debug("CLEANUP: PyAudio terminated")
                
        except Exception as e:
            logger.error(f"ERROR: Audio cleanup error: {e}")
    
    def get_audio_stats(self) -> Dict[str, Any]:
        """Get audio input statistics"""
        return {
            'device_name': self.selected_device_name,
            'device_index': self.device_index,
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'chunk_size': self.chunk_size,
            'frames_captured': self.frames_captured,
            'bytes_captured': self.bytes_captured,
            'overruns': self.overruns,
            'underruns': self.underruns,
            'is_active': self.input_stream.is_active() if self.input_stream else False
        }
    
    def list_audio_devices(self) -> List[Dict[str, Any]]:
        """List available audio input devices"""
        devices = []
        
        if not PYAUDIO_AVAILABLE or not self.pa:
            return devices
        
        try:
            device_count = self.pa.get_device_count()
            
            for i in range(device_count):
                try:
                    device_info = self.pa.get_device_info_by_index(i)
                    
                    # Only include input devices
                    if device_info['maxInputChannels'] > 0:
                        devices.append({
                            'index': i,
                            'name': device_info['name'],
                            'max_input_channels': device_info['maxInputChannels'],
                            'default_sample_rate': device_info['defaultSampleRate'],
                            'low_input_latency': device_info.get('defaultLowInputLatency', 0),
                            'high_input_latency': device_info.get('defaultHighInputLatency', 0)
                        })
                        
                except Exception as e:
                    logger.debug(f"DEBUG: Error getting device {i} info: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"ERROR: Failed to list audio devices: {e}")
        
        return devices


# Test function
def test_audio_input():
    """Test audio input functionality"""
    print("Testing Audio Input...")
    
    if not PYAUDIO_AVAILABLE:
        print("ERROR: PyAudio not available - cannot test audio input")
        return
    
    # Create test queue
    output_queue = LockFreeQueue(maxsize=100, name="AudioInputTest")
    
    # Create audio input thread
    audio_input = AudioInputThread(
        output_queue=output_queue,
        sample_rate=48000,
        chunk_size=1024
    )
    
    # Test initialization
    if audio_input.initialize():
        print("SUCCESS: Audio input initialized")
        
        # List devices
        devices = audio_input.list_audio_devices()
        print(f"INFO: Found {len(devices)} input devices")
        for device in devices[:3]:  # Show first 3
            print(f"  - {device['name']} (index: {device['index']})")
        
        audio_input.cleanup()
        print("SUCCESS: Audio input test completed")
    else:
        print("ERROR: Audio input initialization failed")


if __name__ == "__main__":
    test_audio_input()

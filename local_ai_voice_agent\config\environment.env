# Local AI Voice Agent Environment Configuration

# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_API_KEY=ollama
OLLAMA_TIMEOUT=60

# Model Paths and Settings
WHISPER_MODEL_PATH=./models/whisper
WHISPER_DEVICE=auto
WHISPER_COMPUTE_TYPE=float16

# TTS Configuration
TTS_ENGINE=sapi
TTS_VOICE=Microsoft Zira Desktop
TTS_RATE=200
TTS_VOLUME=0.9

# Audio Settings
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_CHUNK_SIZE=1024
AUDIO_FORMAT=int16

# Vision Settings
VISION_MODEL_PATH=./models/vision
MAX_IMAGE_SIZE=1024
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,bmp,webp

# Performance Settings
MAX_MEMORY_GB=16
GPU_MEMORY_FRACTION=0.8
MAX_CONCURRENT_REQUESTS=3
REQUEST_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/voice_agent.log
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5

# Development Settings
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_METRICS=true

# LiveKit Configuration (if using LiveKit rooms)
LIVEKIT_URL=ws://localhost:7880
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=

# Security Settings
ENABLE_FUNCTION_CALLING=true
ALLOW_FILE_ACCESS=false
SANDBOX_MODE=true

# Core LiveKit Agents Framework
livekit-agents>=0.8.0
livekit-plugins-silero>=0.8.0
livekit-plugins-openai>=0.8.0

# Local TTS/STT Dependencies
pyttsx3>=2.90
speechrecognition>=3.10.0
pyaudio>=0.2.11
whisper>=1.1.10
openai-whisper>=20231117

# Audio Processing
numpy>=1.24.0
scipy>=1.10.0
librosa>=0.10.0
soundfile>=0.12.0
pyaudio>=0.2.11

# Image/Vision Processing
Pillow>=10.0.0
opencv-python>=4.8.0
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0

# HTTP and API clients
httpx>=0.24.0
aiohttp>=3.8.0
requests>=2.31.0

# Ollama Integration
ollama>=0.1.0

# Configuration and Environment
pyyaml>=6.0
python-dotenv>=1.0.0
pydantic>=2.0.0

# Async and Concurrency
asyncio>=3.4.3
aiofiles>=23.0.0

# Logging and Monitoring
loguru>=0.7.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Windows-specific (for SAPI TTS)
pywin32>=306; sys_platform == "win32"
comtypes>=1.2.0; sys_platform == "win32"

# Cross-platform TTS alternatives
espeak>=1.48; sys_platform != "win32"
festival>=2.5; sys_platform != "win32"

# Optional GPU acceleration
torch-audio>=2.0.0
torchaudio>=2.0.0

# Utility libraries
tqdm>=4.65.0
rich>=13.0.0
click>=8.1.0
